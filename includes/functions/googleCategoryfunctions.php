<?php





	function validateGoogleCategory($input) {
		// $input is the word being supplied by the user
		$handle = @fopen("/var/www/vhosts/cadservices.co.uk/httpdocs2/moreinfo/autocadlt/taxonomy.en-US.txt", 'r');
		if ($handle) {
		  while (!feof($handle)) {				
			if (strpos(fgets($handle),$input)===0) {
				fclose($handle);
			  return True;
			  }
			}				  
		  }
		  fclose($handle);
		return false;
	}
		   //  generateGoogleCategory($cInfo->categories_id,$cInfo->google_category_baseline,$cInfo->parent_id,$catRow->catBaseline,$catBaseline,$catGoogleTxt,$catTempPar)


function findCat($curID, $catTempPar, $catTempDes, $catIndex)
{
	if( (isset($catTempPar[$curID])) && ($catTempPar[$curID] != 0) )
	{
		if(isset($catIndex[$catTempPar[$curID]]))
		{
			$temp=$catIndex[$catTempPar[$curID]];
		}
		else
		{
			$catIndex = findCat($catTempPar[$curID], $catTempPar, $catTempDes, $catIndex);
			$temp = $catIndex[$catTempPar[$curID]];
		}
	}
	if( (isset($catTempPar[$curID])) && (isset($catTempDes[$curID])) && ($catTempPar[$curID] == 0) )
	{
		$catIndex[$curID] = $catTempDes[$curID];
	}
	else
	{
		$catIndex[$curID] = $temp . " > " . $catTempDes[$curID];
	}
	return $catIndex;
foreach($catTempDes as $curID=>$des)  //don't need the $des
{
	$catIndex = findCat($curID, $catTempPar, $catTempDes, $catIndex);
}
}






	function generateGoogleCategory($catId,$catGoogleTxt,$catTempPar,$catBaseline,$validate){

		$catPath = array();
		$mcParentCats = array();
		//echo $catId . ',' . $catGoogleTxt. ',' . $catTempPar. ',' . $catBaseline. ',' . $validate . '<br>';
		
		$mcParentCats = getcatParentIds($catId,$catBaseline[$catId],$catTempPar[$catId],$catPath,$catTempPar);
		$googleCategory = '';
			
		foreach($mcParentCats as $curCat){// else parse through the parent categories until we get a category to  et
			/*if(isset($catBaseline[$catId]) && $catBaseline[$catId] == 1){ // is it set as a baseline ie overides parent
				$googleCategory = $catGoogleTxt[$curCat];
			} */
			//echo 'gooooo';
			if ($catGoogleTxt[$curCat] != ''){
				//echo '1';
				$dupe = strpos($googleCategory, $catGoogleTxt[$curCat]);
				if ($dupe === false) {
					if($googleCategory != '') {
						$googleCategory = $catGoogleTxt[$curCat] . ' > ' . $googleCategory;
					} else {
						$googleCategory = $catGoogleTxt[$curCat];
					}
				} else {break;}
			}
		}
		//echo 'gcat' . $googleCategory;
		
		if(isset($validate) && $validate == 1){
			$tempCats = explode(" > ",$googleCategory);
			$currGoogleCategory = '';
			$isValid = 'Valid';
			if(!empty($googleCategory)) {
				for ($i = 0, $n = sizeof($tempCats); $i < $n; $i++) {
					if($isValid == 'Valid'){
						if ($currGoogleCategory != ''){
							$validation = $currGoogleCategory . ' > ' . $tempCats[$i];
						} else {
							$validation = $tempCats[$i];
						}
						//echo '<br>validating: "' .  $validation . '"';
						if (validateGoogleCategory($validation) === True){
							$currGoogleCategory = $validation;
							//echo ' PASS!!!';
						} else {
							//echo ' FAIL';
							$isValid = 'Not Valid';
							if ($outputStyled){}
							$currGoogleCategory  = $currGoogleCategory . '</span> <span style="font-weight:bold;color:red"> > ' . $tempCats[$i];
						}
					} else {
						$currGoogleCategory  = $currGoogleCategory . ' > ' . $tempCats[$i];
					}
				}	
			} else { 
				$currGoogleCategory = '</span> <span style="font-weight:bold;color:red">Not Set';$isValid = 'Not Set';
			}
		} else {
			$currGoogleCategory = $googleCategory;
		}
		return  $currGoogleCategory;
	}
	// <span style="color:green">' . . '</span><br>This Google Category is: ' . $isValid;
	function getcatParentIds($curID, $baseLine, $catParentId, $catPath, $catTempPar){
		/*echo '<br />##$curID = ' . $curID . '<br />##$catParentId = ' . $catParentId . '<br />##$catPath = ' . $catPath . '<br />##$catTempPar = ' . $catTempPar;
		echo '<br />##$catTempPar[' . $catParentId . '] = ' . $catTempPar[$catParentId];*/
		$catPath[] = $curID;
	//	echo 'if (isset(' . $catParentId . ') && ( ' . $catParentId . ' != 0) && ' . $baseLine . ' != 1)<br>';
		if (isset($catParentId) && ($catParentId != 0) && $baseLine != 1){
		//	echo '<br />recursion = ' . $catParentId . ' & ' . $catBaseline[$catParentId] . ' & ' . '$catTempPar[' .  $catParentId . '] = ' . $catTempPar[$catParentId] . ' & ' . $catPath . ' & ' . $catTempPar;
			$catPath = getcatParentIds($catParentId,$catBaseline[$catParentId],$catTempPar[$catParentId], $catPath,$catTempPar);
		}
		return $catPath;
	}			
?>