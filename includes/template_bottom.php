<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2010 osCommerce

  Released under the GNU General Public License
*/
?>

</div> <!-- bodyContent //-->

<?php
if ($oscTemplate->hasBlocks('boxes_column_left')) {
?>

	<div id="columnLeft" class="col-md-<?php echo $oscTemplate->getGridColumnWidth(); ?>  col-md-pull-<?php echo $oscTemplate->getGridContentWidth(); ?> sidebar-offcanvas">
		<?php echo $oscTemplate->getBlocks('boxes_column_left'); ?>
	</div>

<?php
}

if ($oscTemplate->hasBlocks('boxes_column_right')) {
?>

	<div id="columnRight" class="col-md-<?php echo $oscTemplate->getGridColumnWidth(); ?>">
		<?php echo $oscTemplate->getBlocks('boxes_column_right'); ?>
	</div>

<?php
}
?>

</div> <!-- row -->

</div> <!-- bodyWrapper //-->

<?php require('includes/footer.php');
require('min/utils.php');

echo '<script src="' . Minify_getUri('js.js') . '"></script>';


//<script src="min/g=js.js?v=1.11"></script>
?>
<script>
	$(document).ready(function() {
		$('#bodyContent').animate({
			opacity: 1
		});
		if (frm = $('#fancyFormForm')) {
			frm.submit(function(ev) {
				$.ajax({
					type: frm.attr('method'),
					url: frm.attr('action'),
					data: frm.serialize(),
					beforeSend: function(data) {
						$('#fancyFormSpinner').show();
						var thisModal = $('#fancyForm');
						if ($('#queryFrmextra').val().replace(" ", "").toLowerCase() == 'ybzq') {
							$('#extraError').hide();
							thisModal.modal('hide');
							var notiModal = $('#notificationModal');
							notiModal.find('#noti-modal-title').html('Quotation Request Submitted');
							notiModal.find('#noti-modal-body').html('<strong><p class="text-center">Thank-you for your request,<br> A member of our sales team will be in touch shortly.</p></strong>');
							notiModal.modal('show');
							frm.trigger("reset");
						} else {
							$('#extraError').fadeOut('slow').fadeIn('slow');
							return false;
						}
					},
					success: function(data) {}
				});
				ev.preventDefault();
			});
			$('#fancyForm').on('show.bs.modal', function(event) {
				var button = $(event.relatedTarget);
				var recipient = button.data('product');
				var modal = $(this);
				modal.find('#queryFrmProduct').val(recipient);
			})
		}
	});
</script>
<!-- font awesome -->
<script defer src="https://use.fontawesome.com/releases/v5.0.6/js/all.js"></script>
<?php echo $oscTemplate->getBlocks('footer_scripts'); ?>
<a href="cadserv_admin/index.php" rel="nofollow" style="visibility:hidden;display:none;">&nbsp;</a>

<div class="modal fade" id="formErrorModal" role="dialog" tabindex="-1">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header"><button aria-label="Close" class="close" data-dismiss="modal" type="button"><span aria-hidden="true">&times;</span></button>

				<h4 class="modal-title">Form Error</h4>
			</div>

			<div class="modal-body" id="formErrorModalMessage">&nbsp;</div>

			<div class="modal-footer"><button class="btn btn-primary" data-dismiss="modal" type="button">Close</button></div>
		</div>
		<!-- /.modal-content -->
	</div>
	<!-- /.modal-dialog -->
</div>
<!-- /.modal -->
</div>
<script type="text/javascript">
	_linkedin_partner_id = "5284385";
	window._linkedin_data_partner_ids = window._linkedin_data_partner_ids || [];
	window._linkedin_data_partner_ids.push(_linkedin_partner_id);
</script>
<script type="text/javascript">
	(function(l) {
		if (!l) {
			window.lintrk = function(a, b) {
				window.lintrk.q.push([a, b])
			};
			window.lintrk.q = []
		}
		var s = document.getElementsByTagName("script")[0];
		var b = document.createElement("script");
		b.type = "text/javascript";
		b.async = true;
		b.src = "https://snap.licdn.com/li.lms-analytics/insight.min.js";
		s.parentNode.insertBefore(b, s);
	})(window.lintrk);
</script> <noscript> <img height="1" width="1" style="display:none;" alt="" src="https://px.ads.linkedin.com/collect/?pid=5284385&fmt=gif" /> </noscript><!-- Meta Pixel Code -->
<script>
	! function(f, b, e, v, n, t, s) {
		if (f.fbq) return;
		n = f.fbq = function() {
			n.callMethod ? n.callMethod.apply(n, arguments) : n.queue.push(arguments)
		};
		if (!f._fbq) f._fbq = n;
		n.push = n;
		n.loaded = !0;
		n.version = '2.0';
		n.queue = [];
		t = b.createElement(e);
		t.async = !0;
		t.src = v;
		s = b.getElementsByTagName(e)[0];
		s.parentNode.insertBefore(t, s)
	}(window, document, 'script', 'https://connect.facebook.net/en_US/fbevents.js');
	fbq('init', '1236967857300475');
	fbq('track', 'PageView');
</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=1236967857300475&ev=PageView&noscript=1" /></noscript><!-- End Meta Pixel Code -->

<?php
if (isset($product_info) && isset($product_info['manufacturers_id'])) {
	switch ($product_info['manufacturers_id']) {
		case 58:
			echo PHP_EOL . ' <!---------- Meta Pixel Code for bluebeam --> ' . PHP_EOL . PHP_EOL;
?>
			<span style="display:none">
				<script>
					//nop
					//
				</script>
				<!-- Meta Pixel Code for bluebeam -->
				<script>
					! function(f, b, e, v, n, t, s) {
						if (f.fbq) return;
						n = f.fbq = function() {
							n.callMethod ?
								n.callMethod.apply(n, arguments) : n.queue.push(arguments)
						};
						if (!f._fbq) f._fbq = n;
						n.push = n;
						n.loaded = !0;
						n.version = '2.0';
						n.queue = [];
						t = b.createElement(e);
						t.async = !0;
						t.src = v;
						s = b.getElementsByTagName(e)[0];
						s.parentNode.insertBefore(t, s)
					}(window, document, 'script',
						'https://connect.facebook.net/en_US/fbevents.js');
					fbq('init', '3527523707549070');
					fbq('track', 'PageView');
				</script>
				<noscript><img height="1" width="1" style="display:none"
						src=https://www.facebook.com/tr?id=3527523707549070&ev=PageView&noscript=1 /></noscript>
				<!-- End Meta Pixel Code bluebeam -->

			<?PHP break;
		case 77: ?>
				<!-- Meta sketchup Pixel Code -->
				<script>
					! function(f, b, e, v, n, t, s) {
						if (f.fbq) return;
						n = f.fbq = function() {
							n.callMethod ?
								n.callMethod.apply(n, arguments) : n.queue.push(arguments)
						};
						if (!f._fbq) f._fbq = n;
						n.push = n;
						n.loaded = !0;
						n.version = '2.0';
						n.queue = [];
						t = b.createElement(e);
						t.async = !0;
						t.src = v;
						s = b.getElementsByTagName(e)[0];
						s.parentNode.insertBefore(t, s)
					}(window, document, 'script',
						'https://connect.facebook.net/en_US/fbevents.js');
					fbq('init', '1508879763130510');
					fbq('track', 'PageView');
				</script>
				<noscript><img height="1" width="1" style="display:none"
						src=https://www.facebook.com/tr?id=1508879763130510&ev=PageView&noscript=1 /></noscript>
				<!-- End sketchup Meta Pixel Code -->
		<?php
			break;
	} ?>
			</span>
			<script>
				$('.btn-buy').click(function() {
					fbq('track', 'AddToCart');
				});
			</script>
		<?php } ?>
	</body>
</html>