<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2017 osCommerce

  Released under the GNU General Public License
*/

  class cm_pi_products_variations_table {
    var $code;
    var $group;
    var $title;
    var $description;
    var $sort_order;
    var $enabled = false;
 
    function __construct() {
      $this->code = get_class($this);
      $this->group = basename(dirname(__FILE__));

      $this->title = "Product Variations";
      $this->description = MODULE_CONTENT_PRODUCT_INFO_PRODUCT_VARIATIONS_TABLE_DESCRIPTION;
      $this->description .= '<div class="secWarning">' . MODULE_CONTENT_BOOTSTRAP_ROW_DESCRIPTION . '</div>';

      if ( defined('MODULE_CONTENT_PRODUCT_INFO_PRODUCT_VARIATIONS_TABLE_STATUS') ) {
        $this->sort_order = MODULE_CONTENT_PRODUCT_INFO_PRODUCT_VARIATIONS_TABLE_SORT_ORDER;
        $this->enabled = (MODULE_CONTENT_PRODUCT_INFO_PRODUCT_VARIATIONS_TABLE_STATUS == 'True');
      }
    }

    function execute() {
      global $oscTemplate, $category, $cPath_array, $cPath, $current_category_id, $languages_id, $messageStack, $currencies, $currency, $PHP_SELF, $products_attributes;
      
      $content_width = (int)MODULE_CONTENT_PRODUCT_INFO_PRODUCT_VARIATIONS_TABLE_CONTENT_WIDTH;
      $product_width = (int)MODULE_CONTENT_PRODUCT_INFO_PRODUCT_VARIATIONS_TABLE_DISPLAY_EACH;
      
   //select p.products_model, pd.products_name, m.manufacturers_name, p.products_quantity, p.products_image, p.products_weight, p.products_id, SUBSTRING_INDEX(pd.products_description, ' ', 20) as products_description, p.manufacturers_id, p.products_price, p.products_tax_class_id, IF(s.status, s.specials_new_products_price, NULL) as specials_new_products_price, IF(s.status, s.specials_new_products_price, p.products_price) as final_price, p.products_quantity as in_stock, if(s.status, 1, 0) as is_special, p.products_sort_order, p.products_request_quote from " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_PRODUCTS . " p left join " . TABLE_MANUFACTURERS . " m on p.manufacturers_id = m.manufacturers_id left join " . TABLE_SPECIALS . " s on p.products_id = s.products_id, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_status = '1' and p.products_id = p2c.products_id and pd.products_id = p2c.products_id and pd.language_id = '" . (int)$languages_id . "' and p2c.categories_id = '" . (int)$current_category_id . "'"
		$variations = $products_attributes->variations;
	    $num_products = count($variations);
      if ($num_products > 0) {
        ob_start();
        include('includes/modules/content/' . $this->group . '/templates/tpl_' . basename(__FILE__));
        $template = ob_get_clean();

        $oscTemplate->addContent($template, $this->group);
      }
    }

    function isEnabled() {
      return $this->enabled;
    }

    function check() {
      return defined('MODULE_CONTENT_PRODUCT_INFO_PRODUCT_VARIATIONS_TABLE_STATUS');
    }

    function install() {
      tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Enable Reviews Module', 'MODULE_CONTENT_PRODUCT_INFO_PRODUCT_VARIATIONS_TABLE_STATUS', 'True', 'Should the Also Purchased block be shown on the product info page?', '6', '1', 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
      tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Content Width', 'MODULE_CONTENT_PRODUCT_INFO_PRODUCT_VARIATIONS_TABLE_CONTENT_WIDTH', '12', 'What width container should the content be shown in?', '6', '1', 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), ', now())");
      tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Number of Products', 'MODULE_CONTENT_PRODUCT_INFO_PRODUCT_VARIATIONS_TABLE_CONTENT_LIMIT', '4', 'How many products (maximum) should be shown?', '6', '1', now())");
      tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Product Width', 'MODULE_CONTENT_PRODUCT_INFO_PRODUCT_VARIATIONS_TABLE_DISPLAY_EACH', '3', 'What width container should each product be shown in? (12 = full width, 6 = half width).', '6', '4', 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), ', now())");
      tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Sort Order', 'MODULE_CONTENT_PRODUCT_INFO_PRODUCT_VARIATIONS_TABLE_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', now())");
    }

    function remove() {
      tep_db_query("delete from configuration where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
      return array('MODULE_CONTENT_PRODUCT_INFO_PRODUCT_VARIATIONS_TABLE_STATUS', 'MODULE_CONTENT_PRODUCT_INFO_PRODUCT_VARIATIONS_TABLE_CONTENT_WIDTH', 'MODULE_CONTENT_PRODUCT_INFO_PRODUCT_VARIATIONS_TABLE_CONTENT_LIMIT', 'MODULE_CONTENT_PRODUCT_INFO_PRODUCT_VARIATIONS_TABLE_DISPLAY_EACH', 'MODULE_CONTENT_PRODUCT_INFO_PRODUCT_VARIATIONS_TABLE_SORT_ORDER');
    }
  }
  