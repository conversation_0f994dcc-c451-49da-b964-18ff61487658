<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2018 osCommerce

  Released under the GNU General Public License
*/

class cm_pi_options_attributes
{
    var $code;
    var $group;
    var $title;
    var $description;
    var $sort_order;
    var $enabled = false;

    function __construct()
    {
        $this->code = get_class($this);
        $this->group = basename(dirname(__FILE__));

        $this->title = MODULE_CONTENT_PI_OA_TITLE;
        $this->description = MODULE_CONTENT_PI_OA_DESCRIPTION;
        $this->description .= '<div class="secWarning">' . MODULE_CONTENT_BOOTSTRAP_ROW_DESCRIPTION . '</div>';

        if (defined('MODULE_CONTENT_PI_OA_STATUS')) {
            $this->sort_order = MODULE_CONTENT_PI_OA_SORT_ORDER;
            $this->enabled = (MODULE_CONTENT_PI_OA_STATUS == 'True');
        }
    }

    function build_attribute_selector($languages_id, $currencies, $product_info, $products_attributes,$selected_attributes = null)  {
        print_rr(['lang' => $languages_id,'currencies' => $currencies,'product_info' => $product_info,'products_attributes' => $products_attributes,'selected_attributes' => $selected_attributes]);
        if ($products_attributes->has_attributes) {
            $content_width = (int)MODULE_CONTENT_PI_OA_CONTENT_WIDTH;
            $options_output = null;
          //  print_rr($products_attributes);
            $attributes = $products_attributes->attributes;
          //  print_rr($attributes, 'intinias');
            
            // Function to check if a button should be shown based on dependencies
            function should_show_button($dependson_options_id, $dependson_values_id, $selected_attributes) {
                // If no dependency, always show the button
                if (empty($dependson_options_id) || empty($dependson_values_id)) {
                    return true;
                }
                
                // Check if the dependent option is selected
                if (isset($selected_attributes[$dependson_options_id])) {
                    // If the dependent value is selected, show the button
                    if ($selected_attributes[$dependson_options_id] == $dependson_values_id) {
                        return true;
                    }
                }
                
                // By default, hide the button if its dependency is not satisfied
                return false;
            }
            foreach ($attributes as $aKey => $products_options) {
                $products_options_array = array();
                //echo '<br>attributes:' . json_encode($products_options,JSON_PRETTY_PRINT);
                $fr_input = $fr_required = $fr_feedback = null;
                if (MODULE_CONTENT_PI_OA_ENFORCE == 'True') {
                    $fr_input = FORM_REQUIRED_INPUT;
                    $fr_required = 'required aria-required="true" ';
                    $fr_feedback = ' has-feedback';
                }
                if (MODULE_CONTENT_PI_OA_HELPER == 'True') {
                    $products_options_array[] = array('id' => '', 'text' => MODULE_CONTENT_PI_OA_ENFORCE_SELECTION);
                }
                //$products_options_query = tep_db_query("select pov.products_options_values_id, pov.products_options_values_name, pa.options_values_price, pa.price_prefix, pa.attribute_default, pa.dependson_options_id, pa.dependson_options_values_id from products_attributes pa, products_options_values pov where pa.products_id = '" . (int)$_GET['products_id'] . "' and pa.options_id = '" . (int)$aKey . "' and pa.options_values_id = pov.products_options_values_id and pov.language_id = '" . (int)$languages_id . "' order by pa.products_attributes_sort_order");

                if (is_string($_GET['products_id']) && isset($cart->contents[$_GET['products_id']]['attributes'][$aKey])) {
                    $selected_attribute = $cart->contents[$_GET['products_id']]['attributes'][$aKey];
                } else {
                    $selected_attribute = false;
                }
                $select_output_select = '<div class="form-group prod_attribute_group ' . $fr_feedback . '">
					<label for="input_' . $aKey . '" class="control-label col-sm-3">' . $products_options['products_options_name'] . '</label>
						<div class="col-sm-9 attributeWrapper">
							<select name="attribute_selects[' . $aKey . ']" data-optionid="' . $aKey . '" data-productid="' . (int)$_GET['products_id'] . '" data-optionsid="' . $aKey . '" data-lastVal="" required="" aria-required="true" id="input_' . $aKey . '" class="form-control attributeSelect" style="display:none">';
                $buttons_output = '<div id="attributes_values_id_btns_container" class="attributes_values_id_btns_container btn-group" data-optionid="' . $aKey . '" >';

                $option_selected = false;
                $select_output_options = "";
                foreach ($products_options['values'] as $vKey => $products_options_values) {
                    //$select_output .= '<!-- Does ' .  '/\}' . (int)$aKey . ':' . $vKey . '[^0-9]?/'  . ' match ' . $_GET['products_id'] . ':';
                    $selected_option = false;
                    //print_rr($products_attributes->selected_attributes[$aKey][$vKey]);
                    if (isset($products_attributes->selected_attributes[$aKey][$vKey])) {
                        $selected_option = true;
                    }
                    if ($selected_option) {
                        //$select_output .= ' Yes -->';
                        $selected_option = 'selected';
                        $selected_button = 'active btn-success';
                    } else {
                        //$select_output .= ' No -->';
                        $selected_option = '';
                        $selected_button = '';
                    }
                    // Add a style attribute for buttons that depend on other attributes
                    $dependson_style = '';
                    $dependson_class = '';
                    $show_button = true;

                    if (!empty($products_options_values['dependson_options_id']) && !empty($products_options_values['dependson_options_values_id'])) {
                        $dependson_class = ' depends-on-attribute';
                        $show_button = should_show_button(
                            $products_options_values['dependson_options_id'], 
                            $products_options_values['dependson_options_values_id'], 
                            $products_attributes->selected_attributes
                        );
                        
                        if (!$show_button) {
                            $dependson_style = ' style="display: none;"';
                        }
                    }

                    $optionsPrice = $currencies->display_price($products_options_values['options_values_price'], tep_get_tax_rate($product_info['products_tax_class_id']));
                    $select_output_options .= '<option value="' . $vKey . '" data-productId="' . tep_get_prid($_GET['products_id']) . '" data-priceIncrease="' . $products_options_values['options_values_price'] . '" ' . $selected_option . ' data-dependson_optionsid="' . $products_options_values['dependson_options_id'] . '" data-dependsOn_valuesid="' . $products_options_values['dependson_options_values_id'] . '">' . $products_options_values['products_options_values_name'];
                    if ($products_options_values['options_values_price'] != '0') {
                        $select_output_options .= ' (' . $products_options_values['price_prefix'] . $optionsPrice . ') ';
                    }
                    $select_output_options .= '</option>';
                    if (count($products_options['values']) > 1) {
                        $buttons_output .= '<button 
                                                    type="button"
                                                    hx-post="api.php?getNewData='. $product_info['products_id'] . '"                                                    
                                                    hx-target="#cm-pi-options-attributes" 
                                                    hx-swap="outerHTML"
                                                    hx-vals=\'' . json_encode(["selected_attribute" => [$aKey => $vKey]]) . '\'' . $dependson_style
                                                    id="product_attribute_btns_' . $aKey . '_' . $vKey . '" 
                                                    data-optionid="' . $aKey . '" data-valueid="' . $vKey . '" data-productId="' . tep_get_prid($product_info['products_id']) . '" 
                                                    data-priceIncrease="' . $products_options_values['options_values_price'] . '" class="btn btn-default product_attribute_btns ' . $selected_button . $dependson_class . '" 
                                                    data-buttonSet="" 
                                                    data-dependson_optionsid="' . $products_options_values['dependson_options_id'] . '" 
                                                    data-dependsOn_valuesid="' . $products_options_values['dependson_options_values_id'] . '">' . $products_options_values['products_options_values_name'] .
                                            '</button>';
                    } else {
                        $buttons_output .= '<button 
                                                type="button" 
                                                hx-post="api.php" 
                                                hx-target="#cm-pi-options-attributes" 
                                                hx-swap="outerHTML" 
                                                hx-vals=\'' . json_encode(["selected_attribute" => [$aKey => $vKey]]) . '\'' . $dependson_style
                                                id="product_attribute_btns_' . $aKey . '_' . $vKey . '" data-optionid="' . $aKey . '" data-valueid="' . $vKey . '" 
                                                data-productId="' . tep_get_prid($_GET['products_id']) . '" 
                                                data-priceIncrease="' . $products_options_values['options_values_price'] . '" 
                                                class="btn_debuttoned' . $dependson_class . '" data-buttonSet="" 
                                                data-dependson_optionsid="' . $products_options_values['dependson_options_id'] . '" 
                                                data-dependsOn_valuesid="' . $products_options_values['dependson_options_values_id'] . '">' . $products_options_values['products_options_values_name'] .
                                            '</button>';
                    }
                }

                if (!$option_selected) {
                    $select_output = $select_output_select . '<option disabled selected value>None</option>' . $select_output_options;
                }
                $select_output .= "</select>";
                $buttons_output .= "</div>
					</div>
				</div>";
                $buttons_output .= "";

                $options_output .= $select_output . $buttons_output;
            }
            
            // Add JavaScript to handle dependencies when the page loads
            $options_output .= '<script>
                document.addEventListener("htmx:afterSwap", function(event) {
                    // Process all buttons with dependencies
                    const buttons = document.querySelectorAll(".depends-on-attribute");
                    
                    buttons.forEach(function(button) {
                        const dependsOnOptionId = button.getAttribute("data-dependson_optionsid");
                        const dependsOnValueId = button.getAttribute("data-dependson_valuesid");
                        
                        if (dependsOnOptionId && dependsOnValueId) {
                            // Find if the dependency is satisfied
                            const selectedButtons = document.querySelectorAll(".btn-success");
                            let dependencySatisfied = false;
                            
                            selectedButtons.forEach(function(selectedButton) {
                                const optionId = selectedButton.getAttribute("data-optionid");
                                const valueId = selectedButton.getAttribute("data-valueid");
                                
                                if (optionId == dependsOnOptionId && valueId == dependsOnValueId) {
                                    dependencySatisfied = true;
                                }
                            });
                            
                            // Show or hide the button based on dependency
                            if (dependencySatisfied) {
                                button.style.display = "";
                            } else {
                                button.style.display = "none";
                            }
                        }
                    });
                });
            </script>';
            
            //print_rr($options_output, 'oppyouy');
            ob_start();
            include('includes/modules/content/' . $this->group . '/templates/tpl_' . basename(__FILE__));
            return ob_get_clean();
        }
    }

    function execute() {
        global $oscTemplate, $languages_id, $currencies, $product_info, $products_attributes;
        print_rr($products_attributes);
        $template = $this->build_attribute_selector($languages_id, $currencies, $product_info, $products_attributes);
        $oscTemplate->addContent($template, $this->group);
    }

    function isEnabled()
    {
        return $this->enabled;
    }

    function check()
    {
        return defined('MODULE_CONTENT_PI_OA_STATUS');
    }

    function install()
    {
        tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Enable Options & Attributes', 'MODULE_CONTENT_PI_OA_STATUS', 'True', 'Should this module be shown on the product info page?', '6', '1', 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
        tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Content Width', 'MODULE_CONTENT_PI_OA_CONTENT_WIDTH', '12', 'What width container should the content be shown in?', '6', '1', 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), ', now())");
        tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Add Helper Text', 'MODULE_CONTENT_PI_OA_HELPER', 'True', 'Should first option in dropdown be Helper Text?', '6', '1', 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
        tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Enforce Selection', 'MODULE_CONTENT_PI_OA_ENFORCE', 'True', 'Should customer be forced to select option(s)?', '6', '1', 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
        tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Sort Order', 'MODULE_CONTENT_PI_OA_SORT_ORDER', '80', 'Sort order of display. Lowest is displayed first.', '6', '0', now())");
    }

    function remove()
    {
        tep_db_query("delete from configuration where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function keys()
    {
        return array('MODULE_CONTENT_PI_OA_STATUS', 'MODULE_CONTENT_PI_OA_CONTENT_WIDTH', 'MODULE_CONTENT_PI_OA_HELPER', 'MODULE_CONTENT_PI_OA_ENFORCE', 'MODULE_CONTENT_PI_OA_SORT_ORDER');
    }
}
