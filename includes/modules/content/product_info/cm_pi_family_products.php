<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2016 osCommerce

  Released under the GNU General Public License
*/

  class cm_pi_family_products {
    var $code;
    var $group;
    var $title;
    var $description;
    var $sort_order;
    var $enabled = false;

    function __construct() {
      $this->code = get_class($this);
      $this->group = basename(dirname(__FILE__));

      $this->title = MODULE_CONTENT_PI_FAMILY_PRODUCTS_TITLE;
      $this->description = MODULE_CONTENT_PI_FAMILY_PRODUCTS_DESCRIPTION;
      $this->description .= '<div class="secWarning">' . MODULE_CONTENT_BOOTSTRAP_ROW_DESCRIPTION . '</div>';

      if ( defined('MODULE_CONTENT_PI_FAMILY_PRODUCTS_STATUS') ) {
        $this->sort_order = MODULE_CONTENT_PI_FAMILY_PRODUCTS_SORT_ORDER;
        $this->enabled = (MODULE_CONTENT_PI_FAMILY_PRODUCTS_STATUS == 'True');
      }
    }

    function execute() {
	global $oscTemplate, $product_info, $currencies;
		$content_width = (int)MODULE_CONTENT_PI_FAMILY_PRODUCTS_CONTENT_WIDTH;
		define('TABLE_FAMILIES', 'families');
		define('TABLE_PRODUCTS_FAMILIES', 'products_families');
		define('TABLE_PRODUCTS_FAMILIES', 'families.php');
		include('includes/filenames.php');
		$products_id = $product_info['products_id'];
		$familyOut = null;
		$where_clause2     = '';
		$dispType = MODULE_CONTENT_PI_FAMILY_DISPLAY_TYPE;
		$dispForm = MODULE_CONTENT_PI_FAMILY_DISPLAY_FORMAT;
		$headForm = MODULE_CONTENT_PI_FAMILY_HEADER_FORMAT;
		$headText = MODULE_CONTENT_PI_FAMILY_HEADER_TEXT;
		$maxProdu = MODULE_CONTENT_PI_FAMILY_MAX_PRODUCTS;
		
		if ($dispType == 'Box'){
			$familyOut .= '<div class="panel panel-default">';
		} else {
			$familyOut .= '<table class="table">';
		}
		if ($dispForm == 'Seperate'){} else {}
			
		//$family_name_query = tep_db_query("select family_id from " . TABLE_PRODUCTS_FAMILIES . " where products_id = '" . $products_id . "'");
		$family_name_query = tep_db_query("select pf.family_id, f.family_name from " . TABLE_PRODUCTS_FAMILIES . " pf, " . TABLE_FAMILIES . " f where pf.family_id = f.family_id and pf.products_id = '" . $products_id . "'");
		
		if (tep_db_num_rows($family_name_query) > '0') {
			if ($dispType == 'Box'){
				while ($family_results = tep_db_fetch_array($family_name_query)) {
					$family_query = tep_db_query("select distinct p.products_id, p.products_image, p.products_tax_class_id, if(s.status, s.specials_new_products_price, p.products_price) as products_price from " . TABLE_PRODUCTS_FAMILIES . " pf, " . TABLE_PRODUCTS . " p left join " . TABLE_SPECIALS . " s on p.products_id = s.products_id where p.products_status = '1' and pf.family_id = '" . (int)$family_results['family_id'] . "' and p.products_id = pf.products_id and p.products_id != '" . $products_id . "'" . $where_clause2 . " order by p.products_date_added desc limit 9 ");// . MAX_DISPLAY_NEW_PRODUCTS);
					if ($headForm == 'Family Name') {
							$familyOut .= '<h3>' . $family_results['family_name'] . '</h3>';
					} else {
						if ($headText == '') {
							$familyOut .= '<h3>' .  TABLE_HEADING_FAMILY_PRODUCTS . '</h3>';						
						} else {						
							$familyOut .= '<h3>' . $headText . '</h3>';
						}
					}
					//echo 'start: ' . $dispType . ' and ' . $dispForm ' end';
					if ($dispForm == 'Seperate') {
						if (tep_db_num_rows($family_query) > 0) {					
							$where_clause2     = '';
							while ($family = tep_db_fetch_array($family_query)) {
								$where_clause2 .= " and p.products_id != '" . $family['products_id'] . "'";
								$family['products_name']       = tep_get_products_name($family['products_id']);
								$familyOut .= '<div class="col-sm-4"><a href="' . tep_href_link('product_info.php', 'products_id=' . $family['products_id']) . '">' . tep_image('images/' . $family['products_image'], $family['products_name'], SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT) . '</a><br /><a href="' . tep_href_link('product_info.php', 'products_id=' . $family['products_id']) . '">' . $family['products_name'] . '</a><br />' . $currencies->display_price($family['products_price'], tep_get_tax_rate($family['products_tax_class_id'])) . '</div>';
							}						
						}
					} else if ($dispForm == 'Random'){
						if (tep_db_num_rows($family_name_query) > '0') {
											
							$family_name_num_rows = tep_db_num_rows($family_name_query);
							$num_of_rows_less_one = $family_name_num_rows - '1';
							
							if (tep_db_num_rows($family_name_query) == '1') {
								$family_results = tep_db_fetch_array($family_name_query);
								$family_query   = tep_db_query("select distinct p.products_id, p.products_image, p.products_tax_class_id, if(s.status, s.specials_new_products_price, p.products_price) as products_price from " . TABLE_PRODUCTS_FAMILIES . " pf, " . TABLE_PRODUCTS . " p left join " . TABLE_SPECIALS . " s on p.products_id = s.products_id where p.products_status = '1' and pf.family_id = '" . (int) $family_results['family_id'] . "' and p.products_id = pf.products_id and p.products_id != '" . $products_id . "' order by p.products_date_added desc limit " . $maxProdu);
							} else {
								$where_clause = '';
								for ($i = 0; $i < tep_db_num_rows($family_name_query); $i++) {
									$family_results = tep_db_fetch_array($family_name_query);
									if ($i < $num_of_rows_less_one) {
										$where_clause .= "(pf.family_id = '" . $family_results['family_id'] . "') OR ";
									} else {
										$where_clause .= "(pf.family_id = '" . $family_results['family_id'] . "')";
									}
								}
								$family_query = tep_db_query("select distinct p.products_id, p.products_image, p.products_tax_class_id, if(s.status, s.specials_new_products_price, p.products_price) as products_price from " . TABLE_PRODUCTS_FAMILIES . " pf, " . TABLE_PRODUCTS . " p left join " . TABLE_SPECIALS . " s on p.products_id = s.products_id where p.products_status = '1' and (" . $where_clause . ") and p.products_id = pf.products_id and p.products_id != '" . $products_id . "' limit " . $maxProdu);
							}
							if (tep_db_num_rows($family_query) > 0) {
								//new contentBoxHeading($familyOut);
								
								$where_clause2     = '';
								for ($i = 0, $j = 1; ($i < $maxProdu) && ($i < tep_db_num_rows($family_query)); $i++, $j++) {
									if (tep_db_num_rows($family_name_query) == '1') {
										$family = tep_random_select("select distinct p.products_id, p.products_image, p.products_tax_class_id, if(s.status, s.specials_new_products_price, p.products_price) as products_price from " . TABLE_PRODUCTS_FAMILIES . " pf, " . TABLE_PRODUCTS . " p left join " . TABLE_SPECIALS . " s on p.products_id = s.products_id where p.products_status = '1' and p.products_id = pf.products_id and p.products_id != '" . $products_id . "'" . $where_clause2);
									} else {
										$family = tep_random_select("select distinct p.products_id, p.products_image, p.products_tax_class_id, if(s.status, s.specials_new_products_price, p.products_price) as products_price from " . TABLE_PRODUCTS_FAMILIES . " pf, " . TABLE_PRODUCTS . " p left join " . TABLE_SPECIALS . " s on p.products_id = s.products_id where p.products_status = '1' and (" . $where_clause . ") and p.products_id = pf.products_id and p.products_id != '" . $products_id . "'" . $where_clause2);
									}
									$where_clause2 .= " and p.products_id != '" . $family['products_id'] . "'";
									
									
									$family['products_name']       = tep_get_products_name($family['products_id']);
									$familyOut .= '<div class="col-sm-4"><a href="' . tep_href_link('product_info.php', 'products_id=' . $family['products_id']) . '">' . tep_image('images/' . $family['products_image'], $family['products_name'], SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT) . '</a><br /><a href="' . tep_href_link('product_info.php', 'products_id=' . $family['products_id']) . '">' . $family['products_name'] . '</a><br />' . $currencies->display_price($family['products_price'], tep_get_tax_rate($family['products_tax_class_id'])) . '</div>';
								}
							}
						}
					}
				}
			} else if ($dispType == 'List') {
				if ($dispForm == 'Seperate') {
					while ($family_results = tep_db_fetch_array($family_name_query)) {
						
						/*$family_query = tep_db_query("select distinct p.manufacturers_id, p.products_id, p.products_image, p.products_tax_class_id, p.products_price, s.specials_new_products_price from " . TABLE_PRODUCTS_FAMILIES . " pf, " . TABLE_PRODUCTS . " p left join " . TABLE_SPECIALS . " s on p.products_id = s.products_id where p.products_status = '1' and pf.family_id = '" . (int)$family_results['family_id'] . "' and p.products_id = pf.products_id and p.products_id != '" . $products_id . "'" . $where_clause2 . " order by p.products_date_added desc limit " . $maxProdu);*/
						$family_query = tep_db_query("select distinct p.manufacturers_id, p.products_id, p.products_image, p.products_tax_class_id, p.products_price, s.specials_new_products_price from " . TABLE_PRODUCTS_FAMILIES . " pf, " . TABLE_PRODUCTS . " p left join " . TABLE_SPECIALS . " s on p.products_id = s.products_id where p.products_status = '1' and pf.family_id = '" . (int) $family_results['family_id'] . "' and p.products_id = pf.products_id and p.products_id != '" . $products_id . "'" . $where_clause2 . " order by p.products_date_added desc ");
						if ($headForm == 'Family Name') {
							$familyOut .= '<div class="panel-heading">' . $family_results['family_name'] . '</div>';
						} else {
							if ($headText == '') {
								$familyOut .= '<div class="panel-heading">' . TABLE_HEADING_FAMILY_PRODUCTS . '</div>';						
							} else {						
								$familyOut .= '<div class="panel-heading">' . $headText . '</div>';
							}
						}
						$define_list = array(
							'PRODUCT_LIST_NAME' => '1', //PRODUCT_LIST_NAME,
							'PRODUCT_LIST_PRICE' => '2', //PRODUCT_LIST_PRICE,
							'PRODUCT_LIST_BUY_NOW' => '3'
						); //PRODUCT_LIST_BUY_NOW);
						asort($define_list);
						$column_list = array();
						reset($define_list);
						foreach ($define_list as $key => $value) {
							if ($value > 0)
								$column_list[] = $key;
						}
						if (tep_db_num_rows($family_query) > 0) {
							$familyOut .= '<thead><tr>';
							for ($col = 0, $n = 3; $col < $n; $col++) {
								switch ($column_list[$col]) {
									case 'PRODUCT_LIST_NAME':
										$lc_text  = TABLE_HEADING_PRODUCTS;
										$lc_classes = '';
										break;
									case 'PRODUCT_LIST_PRICE':
										$lc_text  = TABLE_HEADING_PRICE;
										$lc_classes = 'text-right';
										break;
									case 'PRODUCT_LIST_BUY_NOW':
										$lc_text  = TABLE_HEADING_BUY_NOW;
										$lc_classes = 'text-center';
										break;
								}
								
								if (($column_list[$col] != 'PRODUCT_LIST_BUY_NOW')) {
									$lc_text = tep_create_sort_heading($_GET['sort'], $col + 1, $lc_text);
								}
								$familyOut .= '<th class="' . $lc_classes . '">' . '&nbsp;' . $lc_text . '&nbsp;</th>';
								
							}
							$familyOut .= '</tr></thead>';
							$rows = 0;					
							$where_clause2 = '';
							while ($listing = tep_db_fetch_array($family_query)) {
								$where_clause2 .= " and p.products_id != '" . $listing['products_id'] . "'";
								$listing['products_name'] = tep_get_products_name($listing['products_id']);
								$rows++;
													
								$cur_row = sizeof($familyOut) - 1;
								
								for ($col = 0, $n = 3; $col < $n; $col++) {
									$lc_classes = '';
									switch ($column_list[$col]) {
										case 'PRODUCT_LIST_NAME':
											$lc_classes = '';
											if (isset($_GET['manufacturers_id'])) {
												$lc_text = '<a href="' . tep_href_link('product_info.php', 'manufacturers_id=' . $_GET['manufacturers_id'] . '&products_id=' . $listing['products_id']) . '">' . $listing['products_name'] . '</a>';
											} else {
												$lc_text = '&nbsp;<a href="' . tep_href_link('product_info.php', ($cPath ? 'cPath=' . $cPath . '&' : '') . 'products_id=' . $listing['products_id']) . '">' . $listing['products_name'] . '</a>&nbsp;';
											}
											break;
										case 'PRODUCT_LIST_PRICE':
											$lc_classes = 'text-right';
											if (@tep_not_null($listing['specials_new_products_price'])) {
												$lc_text = '&nbsp;<s>' . $currencies->display_price($listing['products_price'], tep_get_tax_rate($listing['products_tax_class_id'])) . '</s>&nbsp;&nbsp;<span class="productSpecialPrice">' . $currencies->display_price($listing['specials_new_products_price'], tep_get_tax_rate($listing['products_tax_class_id'])) . '</span>&nbsp;';
											} else {
												$lc_text = '&nbsp;' . $currencies->display_price($listing['products_price'], tep_get_tax_rate($listing['products_tax_class_id'])) . '&nbsp;';
											}
											break;
										
										case 'PRODUCT_LIST_BUY_NOW':
											$lc_classes = 'text-center';
											$lc_text  = '<a class="btn btn-primary btn-xs" href="' . tep_href_link($_SERVER['PHP_SELF'], tep_get_all_get_params(array(
												'action'
											)) . 'action=buy_now&products_id=' . $listing['products_id']) . '">&nbsp;<span class="fa fa-shopping-cart"></span>&nbsp;Add to Cart&nbsp;</a>';
											break;
									}
									
									$familyOut .= '<td class="' . $lc_classes . '">' . $lc_text . '</td>';
								}
								$familyOut .= '</tr><tr>';
							}	
						}
                    }
				}


				if ($dispForm == 'Random') {
					$family_name_num_rows = tep_db_num_rows($family_name_query);
					$num_of_rows_less_one = $family_name_num_rows - '1';
					
					if (tep_db_num_rows($family_name_query) == '1') {
						$family_results = tep_db_fetch_array($family_name_query);
						$family_query   = tep_db_query("select distinct p.products_id, p.products_image, p.products_tax_class_id, if(s.status, s.specials_new_products_price, p.products_price) as products_price from " . TABLE_PRODUCTS_FAMILIES . " pf, " . TABLE_PRODUCTS . " p left join " . TABLE_SPECIALS . " s on p.products_id = s.products_id where p.products_status = '1' and pf.family_id = '" . (int) $family_results['family_id'] . "' and p.products_id = pf.products_id and p.products_id != '" . $products_id . "' order by p.products_date_added desc limit " . $maxProdu);
					} else {
						$where_clause  = '';
						$where_clause2 = '';
						for ($i = 0; $i < tep_db_num_rows($family_name_query); $i++) {
							$family_results = tep_db_fetch_array($family_name_query);
							if ($i < $num_of_rows_less_one) {
								$where_clause .= "(pf.family_id = '" . $family_results['family_id'] . "') OR ";
							} else {
								$where_clause .= "(pf.family_id = '" . $family_results['family_id'] . "')";
							}
						}
						$family_query = tep_db_query("select distinct p.products_id, p.products_image, p.products_tax_class_id, if(s.status, s.specials_new_products_price, p.products_price) as products_price from " . TABLE_PRODUCTS_FAMILIES . " pf, " . TABLE_PRODUCTS . " p left join " . TABLE_SPECIALS . " s on p.products_id = s.products_id where p.products_status = '1' and (" . $where_clause . ") and p.products_id = pf.products_id and p.products_id != '" . $products_id . "' limit " . $maxProdu);
					}
					
					$define_list = array(
						'PRODUCT_LIST_NAME' => '1', //PRODUCT_LIST_NAME,
						'PRODUCT_LIST_PRICE' => '2', //PRODUCT_LIST_PRICE,
						'PRODUCT_LIST_BUY_NOW' => '3'
					); //PRODUCT_LIST_BUY_NOW);
					
					asort($define_list);
					
					$column_list = array();
					reset($define_list);
					foreach ($define_list as $key => $value) {
						if ($value > 0)
							$column_list[] = $key;
					}
					if (tep_db_num_rows($family_query) > 0) {
						
						if ($headText == '') {
							$familyOut .= '<td>' . '&nbsp;' . TABLE_HEADING_FAMILY_PRODUCTS . '&nbsp;';
						} else {
							$familyOut .= '<td>' . '&nbsp;' . $headText . '&nbsp;';
						}
						
						for ($i = 1; $i < 5; $i++) {
							$familyOut .= '<td>' . '&nbsp;';
						}
						for ($col = 0, $n = 3; $col < $n; $col++) {
							switch ($column_list[$col]) {
								case 'PRODUCT_LIST_NAME':
									$lc_text  = TABLE_HEADING_PRODUCTS;
									$lc_classes = '';
									break;
								case 'PRODUCT_LIST_PRICE':
									$lc_text  = TABLE_HEADING_PRICE;
									$lc_classes = 'text-right';
									break;
								case 'PRODUCT_LIST_BUY_NOW':
									$lc_text  = TABLE_HEADING_BUY_NOW;
									$lc_classes = 'text-center';
									break;
							}
							
							if (($column_list[$col] != 'PRODUCT_LIST_BUY_NOW')) {
								$lc_text = tep_create_sort_heading($_GET['sort'], $col + 1, $lc_text);
							}
							
							$familyOut .= '<td>' . $lc_text . '</td>';
						}
						$rows = 0;
						$familyOut .= '</tr>';
						while ($listing = tep_db_fetch_array($family_query)) {
							$listing['products_name'] = tep_get_products_name($listing['products_id']);
							$rows++;
							$familyOut .= '<tr>';
							$cur_row = sizeof($familyOut) - 1;
							for ($col = 0, $n = 3; $col < $n; $col++) {
								$lc_classes = '';
								switch ($column_list[$col]) {
									case 'PRODUCT_LIST_NAME':
										$lc_classes = '';
										if (isset($_GET['manufacturers_id'])) {
											$lc_text = '<a href="' . tep_href_link('product_info.php', 'manufacturers_id=' . $_GET['manufacturers_id'] . '&products_id=' . $listing['products_id']) . '">' . $listing['products_name'] . '</a>';
										} else {
											$lc_text = '&nbsp;<a href="' . tep_href_link('product_info.php', ($cPath ? 'cPath=' . $cPath . '&' : '') . 'products_id=' . $listing['products_id']) . '">' . $listing['products_name'] . '</a>&nbsp;';
										}
										break;
									case 'PRODUCT_LIST_PRICE':
										$lc_classes = 'text-right';
										if (@tep_not_null($listing['specials_new_products_price'])) {
											$lc_text = '&nbsp;<s>' . $currencies->display_price($listing['products_price'], tep_get_tax_rate($listing['products_tax_class_id'])) . '</s>&nbsp;&nbsp;<span class="productSpecialPrice">' . $currencies->display_price($listing['specials_new_products_price'], tep_get_tax_rate($listing['products_tax_class_id'])) . '</span>&nbsp;';
										} else {
											$lc_text = '&nbsp;' . $currencies->display_price($listing['products_price'], tep_get_tax_rate($listing['products_tax_class_id'])) . '&nbsp;';
										}
										break;
									
									case 'PRODUCT_LIST_BUY_NOW':
										$lc_classes = 'text-center';
										$lc_text  = '<a href="' . tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array(
											'action'
										)) . 'action=buy_now&products_id=' . $listing['products_id']) . '">' . tep_image_button('button_buy_now.gif', IMAGE_BUTTON_BUY_NOW) . '</a>&nbsp;';
										break;
								}
								
								$familyOut .= '<td>' . $lc_text . '</td>';
							}
							$familyOut .= '</tr>';
						}
						
					}		
				}
			}
			if ($dispType == 'Box'){
				$familyOut .= '</div>';
			} else {
				$familyOut .= '</tr></table>';
			}
        ob_start();
		include('includes/modules/content/' . $this->group . '/templates/family_products.php');
		$template = ob_get_clean();
        $oscTemplate->addContent($template, $this->group);
		}      
    }

   
    function isEnabled() {
      return $this->enabled;
    }

    function check() {
      return defined('MODULE_CONTENT_PI_FAMILY_PRODUCTS_STATUS');
    }

    function install() {
      tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Enable FAMILY_PRODUCTS Module', 'MODULE_CONTENT_PI_FAMILY_PRODUCTS_STATUS', 'True', 'Should this module be shown on the product info page?', '6', '1', 'tep_cfg_select_option(array(\'True\', \'False\'), )', now())");
      tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Content Width', 'MODULE_CONTENT_PI_FAMILY_PRODUCTS_CONTENT_WIDTH', '6', 'What width container should the content be shown in?', '6', '1', 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), ', now())");
      tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Header Text', 'MODULE_CONTENT_PI_FAMILY_HEADER_TEXT', 'Upgrades & Extras for the above', '', '6', '0', '', now())");
      tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Family Header Format', 'MODULE_CONTENT_PI_FAMILY_HEADER_FORMAT', 'Family Text', 'Please choose whether your headers of your families will be your default text or the actual name of the family.', '6', '0', 'tep_cfg_select_option(array(\'Family Text\', \'Family Name\'), ', now())");
	  tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Family Display Format', 'MODULE_CONTENT_PI_FAMILY_DISPLAY_FORMAT', 'Random', 'Please choose whether your headers of your families will be your default text or the actual name of the family.', '6', '0', 'tep_cfg_select_option(array(\'Random\', \'Seperate\'), ', now())");
      tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Family Display Type', 'MODULE_CONTENT_PI_FAMILY_DISPLAY_TYPE', 'Box', 'Please choose whether you would like to display an infoBox or a list.', '6', '0', 'tep_cfg_select_option(array(\'Box\', \'List\', \'None\'), ', now())");
      tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Max. no. of products to display', 'MODULE_CONTENT_PI_FAMILY_MAX_PRODUCTS', '13', 'Maximum number of products to display as Families infobox on product_info page.', '6', '0', '', now())");
      tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Sort Order', 'MODULE_CONTENT_PI_FAMILY_PRODUCTS_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', now())");
    }

    function remove() {
      tep_db_query("delete from configuration where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
      return array('MODULE_CONTENT_PI_FAMILY_PRODUCTS_STATUS', 'MODULE_CONTENT_PI_FAMILY_PRODUCTS_CONTENT_WIDTH', 'MODULE_CONTENT_PI_FAMILY_HEADER_TEXT', 'MODULE_CONTENT_PI_FAMILY_HEADER_FORMAT', 'MODULE_CONTENT_PI_FAMILY_DISPLAY_FORMAT', 'MODULE_CONTENT_PI_FAMILY_DISPLAY_TYPE', 'MODULE_CONTENT_PI_FAMILY_MAX_PRODUCTS', 'MODULE_CONTENT_PI_FAMILY_PRODUCTS_SORT_ORDER');
    }
  }
  