<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2016 osCommerce

  Released under the GNU General Public License
*/
?>
<div class="col-sm-<?php echo $content_width; ?> new-products">

  <div class="col-sm-<?php echo $content_width; ?> new-products">

  <h3><?php echo sprintf(MODULE_CONTENT_SPECIAL_PRODUCTS_HEADING, strftime('%B')); ?></h3>
  
  <div class="row" itemscope itemtype="http://schema.org/ItemList">
    <meta itemprop="numberOfItems" content="<?php echo (int)$num_special_products; ?>" />
    <?php $count=1;
    while ($special_products = tep_db_fetch_array($special_products_query)) {
      ?>
    <div class="col-sm-<?php echo $product_width; ?>" itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem" itemscope="" itemtype="http://schema.org/Product"><meta itemprop="position" content="<?php echo $count ?>" />
      <div class="thumbnail equal-height">
        <a href="<?php echo tep_href_link('product_info.php', 'products_id=' . (int)$special_products["products_id"]); ?>"><?php echo tep_image('images/' . $special_products['products_image'], $special_products['products_name'], SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT); ?></a>
        <div class="caption">
          <p class="text-center"><a itemprop="url" href="<?php echo tep_href_link('product_info.php', 'products_id=' . (int)$special_products['products_id']); ?>"><?php echo $special_products['products_name']; ?></a></p>
          <hr>
          <p class="text-center"> <?php echo $currencies->display_price($special_products['specials_new_products_price'], tep_get_tax_rate($special_products['products_tax_class_id'])); ?></br><del><?php echo $currencies->display_price($special_products['products_price'], tep_get_tax_rate($special_products['products_tax_class_id']))?></del></p>
          <div class="text-center">
            <div class="btn-group">
              <a href="<?php echo tep_href_link('product_info.php', tep_get_all_get_params(array('action')) . 'products_id=' . (int)$special_products['products_id']); ?>" class="btn btn-default" role="button"><?php echo MODULE_CONTENT_SPECIAL_PRODUCTS_BUTTON_VIEW; ?></a>
              <?php if($special_products['products_price'] > 0 || $special_products['specials_new_products_price'] > 0 ) { ?>
					<a rel="nofollow" href="<?php echo tep_href_link($PHP_SELF, tep_get_all_get_params(array('action')) . 'action=buy_now&products_id=' . (int)$special_products['products_id']); ?>" class="btn btn-success" role="button"><?php echo MODULE_CONTENT_SPECIAL_PRODUCTS_BUTTON_BUY; ?></a>
			  <?php }else{ ?>
				  <a rel="nofollow" href="<?php echo tep_href_link('product_info.php', tep_get_all_get_params(array('action')) . 'products_id=' . (int)$special_products['products_id']); ?>" class="btn btn-success" role="button"><?php echo 'Call for Price'; ?></a> }
			  <?php } ?>
			</div>
          </div>
        </div>
      </div>
    </div>
    <?php
	$count++;
  }
  ?>
  </div>
  
</div>
  
</div>
      <!-- /*  
		 
		 spec_prods_content .= '<td width="33%" align="center" valign="bottom"><div class="productGrid"><a href="' . tep_href_link('product_info.php', 'products_id=' . $special_products["products_id"]) . '">' . tep_image('images/' . $special_products['products_image'], $special_products['products_name'], SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT, 'class=shadow1') . '</a><br /><a href="' . tep_href_link('product_info.php', 'products_id=' . $special_products['products_id']) . '">' . $special_products['products_name'] . '</a><br /><s>' . $currencies->display_price($special_products['products_price'], tep_get_tax_rate($special_products['products_tax_class_id'])) . '</s><br /><span class="productSpecialPrice">' . $currencies->display_price($special_products['specials_special_products_price'], tep_get_tax_rate($special_products['products_tax_class_id'])) . '</span></div>';*/-->