<div class="col-sm-<?php echo $content_width; ?> category-listing">
  <div itemscope itemtype="http://schema.org/ItemList">
    <meta itemprop="itemListOrder" content="http://schema.org/ItemListUnordered" />
    <meta itemprop="name" content="<?php echo $category['categories_name']; ?>" />
    
    <?php
    while ($categories = tep_db_fetch_array($categories_query)) {
      $cPath_new = tep_get_path($categories['categories_id']);
       if ( is_null($categories['categories_image'])) {             
        $products_query = tep_db_query("SELECT p.products_image,`products_sort_order` FROM products_to_categories cp inner join products p on cp.products_id = p.products_id and p.products_status = 1 and p.products_image is not null WHERE cp.categories_id ='".$categories['categories_id']."' or cp.categories_id in (SELECT categories_id FROM categories WHERE  parent_id='".$categories['categories_id']."') ORDER BY `products_sort_order` ASC LIMIT 6");
         while ($products = tep_db_fetch_array($products_query)){                    
             if ( is_file ('images/' . $products['products_image'])) {                       
                //cho "UPDATE `categories` SET `categories_image`='" . $products['products_image'] . "' WHERE categories_id = '" . $categories['categories_id'] . "'";            //$categories['categories_image'] = tep_get_image_in_category($categories['categories_id']);
                tep_db_query("UPDATE `categories` SET `categories_image`='" . $products['products_image'] . "' WHERE categories_id = '" . $categories['categories_id'] . "'");
                break;
            } 
        }
       
    }
      echo '<div class="col-sm-' . $category_width . '">';
      echo '  <div class="text-center">';
      echo '    <a href="' . tep_href_link('index.php', $cPath_new) . '">' . tep_image('images/' . $categories['categories_image'], $categories['categories_name'], SUBCATEGORY_IMAGE_WIDTH, SUBCATEGORY_IMAGE_HEIGHT) . '</a>';
      echo '    <div class="caption text-center">';
      echo '      <h5><a href="' . tep_href_link('index.php', $cPath_new) . '"><span itemprop="itemListElement">' . $categories['categories_name'] . '</span></a></h5>';
      echo '    </div>';
      echo '  </div>';
      echo '</div>';
    }
    ?>    
  </div>    
</div>
