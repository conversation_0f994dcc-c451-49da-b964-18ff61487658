<?php
/*
  $Id: TCSShip.php,v 1.0 2003/07/07 00:00:01 hpdl Exp $
  by D. M. Gremlin

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

  class TCSShip {
    var $code, $title, $description, $icon, $enabled, $shiptotal, $reason;

// class constructor
    function __construct() {
      global $order;
      $this->code = 'TCSShip';
      $this->title = MODULE_SHIPPING_TCSSHIP_TEXT_TITLE;
      $this->description = MODULE_SHIPPING_TCSSHIP_TEXT_DESCRIPTION;
      $this->sort_order = MODULE_SHIPPING_TCSSHIP_SORT_ORDER;
      $this->icon = '';
      $this->tax_class = MODULE_SHIPPING_TCSSHIP_TAX_CLASS;
      $this->enabled = ((MODULE_SHIPPING_TCSSHIP_STATUS == 'True') ? true : false);

// Enable Individual Shipping Module
//      $this->enabled = MODULE_SHIPPING_TCSSHIP_STATUS;
      if ( ($this->enabled == true) && ((int)MODULE_SHIPPING_TCSSHIP_ZONE > 0) ) {
        $check_flag = false;
        $check_query = tep_db_query("select zone_id from " . TABLE_ZONES_TO_GEO_ZONES . " where geo_zone_id = '" . MODULE_SHIPPING_TCSSHIP_ZONE . "' and zone_country_id = '" . $order->delivery['country']['id'] . "' order by zone_id");
        while ($check = tep_db_fetch_array($check_query)) {
          if ($check['zone_id'] < 1) {
            $check_flag = true;
            break;
          } elseif ($check['zone_id'] == $order->delivery['zone_id']) {
            $check_flag = true;
            break;
          }
        }

        if ($check_flag == false) {
          $this->enabled = false;
        }
      }
    }

// class methods

function quote($method = '') {
global $order, $cart, $shipping_modules;
	$cost = $this->getQtyPrice();
	$this->quotes = array('id' => $this->code,
		'module' => $this->reason,
		'methods' => array(array('id' => $this->code,
			'title' => '',
			'displayReason' => $this->DisplayReason,
			'cost' => $cost)));
			
	if ($this->tax_class > 0) {
		$this->quotes['tax'] = tep_get_tax_rate($this->tax_class, $order->delivery['country']['id'], $order->delivery['zone_id']);
	}

	if (@tep_not_null($this->icon)) $this->quotes['icon'] = tep_image($this->icon, $this->title);
	

	return $this->quotes;
}
    
function getQtyPrice() {
	global $cart, $order;
	$products = $cart->get_products();
	$this->reason = 'Standard Delivery to UK Mainland Ground floor Goods-in';
    $order_total = $this->getShippableTotal();
    $itmQty = array();	
	$electronic = false;
	$physical = false;
	$physicalProducts = '</strong><ul>';
	$electronicProducts = '</strong><ul>';
	$excemptFree = '0';
	$physicalReason = 'Standard Delivery to UK Mainland Ground Floor Goods-in of address above';
	$electronicReason = 'Digital Purchase, delivery by email to: ' . $order->customer['email_address'];
	//echo '<!-- start Products Current free status is ' .  $excemptFree . ' -->';
	for ($i=0, $n=sizeof($products); $i<$n; $i++) {
	// check currnet product has shipping set
		$products_shipping_query = tep_db_query("select products_ship_price, products_ship_qty, products_ship_key, products_ship_methods_id, products_ship_flags from products_shipping where products_id = '" . $products[$i]['id'] . "' ORDER BY products_ship_methods_id DESC " );
		$products_shipping = tep_db_fetch_array($products_shipping_query);
		
		$sQty = $products_shipping['products_ship_qty'];
		$individualShippingCost = $products_shipping['products_ship_price'];
		$sFlags = $products_shipping['products_ship_flags'];
		$sKey = $products_shipping['products_ship_key'];
		$sFlagsA = preg_split("/[:,]/" , $sFlags);
		$digiPurchase = $sFlagsA[1];
		if ($excemptFree != '1'){
			$excemptFree = $sFlagsA[0];
		}
		//echo '<!-- Product: ' . $products[$i]['name'] . ' Current free status is: ' .  $excemptFree . ' -->';
        if(@tep_not_null($sKey)){ 
            $itmQty[$sKey] += $products[$i]['quantity'];
        }
	
		if ($digiPurchase == '1') {
			$electronicProducts .= '<li>' . $products[$i]['name'] . '</li>';
			$electronic = true;
			//echo '<!-- digi product -->';
		} else {
		 //echo '<!-- phys product -->';
		   $physical = true;
		   if (($order_total > MODULE_SHIPPING_TCSSHIP_MINFREE) && $excemptFree != '1') {
				//echo '<!-- free total over: ' . MODULE_SHIPPING_TCSSHIP_MINFREE .  '-->';
				$physicalReason = 'Free Delivery on Orders over £' . MODULE_SHIPPING_TCSSHIP_MINFREE;
				$physicalProducts .= '<li>' . $products[$i]['name'] . '</li>';
				$theCalculatedCost = 0;
		   } else {
				//echo '<!-- not over: ' . MODULE_SHIPPING_TCSSHIP_MINFREE .  ' -->';
				if(@tep_not_null($itmQty[$sKey]) && @tep_not_null($sQty)){     
					 //echo '<!-- sQty: ' . $sQty .  ' -->';
					 if ($sQty == 1 && $excemptFree != '1'){// if only 1 product is required for free shipping
						  //echo '<!-- just one -->';
						  $theCalculatedCost = 0;
						  $physicalReason = 'Free Delivery to UK Mainland Ground Floor Goods-in of address above';
						  $physicalProducts .= '<li>' . $products[$i]['name'] . '</li>';
					 } else {          
						//echo '<!-- multi -->';
						if ($itmQty[$sKey] > 1 && $itmQty[$sKey] >= $sQty && $excemptFree != '1') { // if it has passed the required qty ITS FREE!!!!!
						   //echo '<!-- free ovry qty -->';
						   $theCalculatedCost = 0;
						   $physicalReason = 'Free Delivery on purchase of ' . $sQty . ' Roll(s)/Roll Packs to UK Mainland Ground floor Goods-in of address above';
						   $physicalProducts .= '<li>' . $products[$i]['name'] . '</li>';
						} else {    
						   //echo '<!-- NOT ovr qty -->';
						   //echo '<!-- if(' . $theCalculatedCost . ' != 0  || ' . $excemptFree . ' == 1) -->';
						   if($theCalculatedCost != 0 || $excemptFree == 1) {// Make sure there hasn't been any other products that qualify for free shipping
								//echo '<!-- NO PREV FREE -->';
								$physicalReason = 'Standard Delivery to UK Mainland Ground Floor Goods-in of address above';
								if (is_null($theCalculatedCost)) {
									//echo '<!-- is null -->';
									$theCalculatedCost = $individualShippingCost;
								} else {
									//echo '<!-- is ' . $theCalculatedCost . ' morethan ' . $individualShippingCost . '-->';
									if ($theCalculatedCost < $individualShippingCost){
										//echo '<!-- yes -->';
										$theCalculatedCost = $individualShippingCost;
									}
								}
						   }
						   $physicalProducts .= '<li>' . $products[$i]['name'] . '</li>';
						}
					}
				}
			}
		}
	}

	$physicalProducts .= '</ul><strong>';
	$electronicProducts .= '</ul><strong>';
	
	//echo '<!--- physical:' . $physical . ' electronic: ' . $electronic . '--->';
	
	if(($physical == true) && ($electronic == true)) {
		$this->DisplayReason = '<h3>Standard Items</h3>' . $physicalReason . $physicalProducts . '<h3>Electronic Delivery</h3>' . $electronicReason . $electronicProducts;		
		$this->reason = $physicalReason;
	} elseif ($physical) {
		$this->reason = $physicalReason;
	} elseif ($electronic) {
		$this->reason = $electronicReason;
		$theCalculatedCost = 0;
	}
	
	//echo '<!-- is @tep_not_null(' . $theCalculatedCost . ') -->';
	
	if (isset($theCalculatedCost)){
		//echo '<!-- yes -->';
		return $theCalculatedCost;
	} else {
		//echo '<!-- no -->';
		if (@tep_not_null($individualShippingCost)){ // if we got to this point then check if the item has a shipping prices?
			return $individualShippingCost;
		} else { // otherwise say NO!
			return MODULE_SHIPPING_TCSSHIP_FLATRATE;
		}
	
	}
	
}
	
function getShippableTotal() {
    global $order, $cart, $currencies;
    $order_total = $cart->show_total();
    if ($order->content_type == 'mixed') {
        $order_total = 0;

        for ($i=0, $n=sizeof($order->products); $i<$n; $i++) {
          $order_total += $currencies->calculate_price($order->products[$i]['final_price'], $order->products[$i]['tax'], $order->products[$i]['qty']);

          if (isset($order->products[$i]['attributes'])) {
            reset($order->products[$i]['attributes']);
            foreach ($order->products[$i]['attributes'] as $option => $value) {
              $virtual_check_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS_ATTRIBUTES . " pa, " . TABLE_PRODUCTS_ATTRIBUTES_DOWNLOAD . " pad where pa.products_id = '" . (int)$order->products[$i]['id'] . "' and pa.options_values_id = '" . (int)$value['value_id'] . "' and pa.products_attributes_id = pad.products_attributes_id");
              $virtual_check = tep_db_fetch_array($virtual_check_query);

              if ($virtual_check['total'] > 0) {
                $order_total -= $currencies->calculate_price($order->products[$i]['final_price'], $order->products[$i]['tax'], $order->products[$i]['qty']);
              }
            }
          }
        }
      }

      return $order_total;
    }

    function check() {
      if (!isset($this->_check)) {
        $check_query = tep_db_query("select configuration_value from " . TABLE_CONFIGURATION . " where configuration_key = 'MODULE_SHIPPING_TCSSHIP_STATUS'");
        $this->_check = tep_db_num_rows($check_query);
      }
      return $this->_check;
    }

    function install() {
     tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Enable TCS Shipping Prices', 'MODULE_SHIPPING_TCSSHIP_STATUS', 'True', 'Do you want to offer individual shipping prices?', '6', '0', 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
     tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, use_function, set_function, date_added) values ('Tax Class', 'MODULE_SHIPPING_TCSSHIP_TAX_CLASS', '0', 'Use the following tax class on the shipping fee.', '6', '0', 'tep_get_tax_class_title', 'tep_cfg_pull_down_tax_classes(', now())");
     tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, use_function, set_function, date_added) values ('Shipping Zone', 'MODULE_SHIPPING_TCSSHIP_ZONE', '0', 'If a zone is selected, only enable this shipping method for that zone.', '6', '0', 'tep_get_zone_class_title', 'tep_cfg_pull_down_zone_classes(', now())");
     tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Sort Order', 'MODULE_SHIPPING_TCSSHIP_SORT_ORDER', '0', 'Sort order of display.', '6', '0', now())");
     tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Flat Rate', 'MODULE_SHIPPING_TCSSHIP_FLATRATE', '9', 'Flat Rate', '6', '0', now())");
	tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Min for Free ship', 'MODULE_SHIPPING_TCSSHIP_MINFREE', '200', 'what is the min amount required for free shipping', '6', '0', now())");
    }

    function remove() {
      
      tep_db_query("delete from " . TABLE_CONFIGURATION . " where configuration_key as ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
      return array('MODULE_SHIPPING_TCSSHIP_STATUS', 'MODULE_SHIPPING_TCSSHIP_TAX_CLASS', 'MODULE_SHIPPING_TCSSHIP_ZONE', 'MODULE_SHIPPING_TCSSHIP_SORT_ORDER', 'MODULE_SHIPPING_TCSSHIP_FLATRATE','MODULE_SHIPPING_TCSSHIP_MINFREE');
    }
  }
?>
