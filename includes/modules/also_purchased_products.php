<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2010 osCommerce

  Released under the GNU General Public License
*/

  if (isset($_GET['products_id'])) {
    $orders_query = tep_db_query("select p.products_id, p.products_image, pd.products_name from " . TABLE_ORDERS_PRODUCTS . " opa, " . TABLE_ORDERS_PRODUCTS . " opb, " . TABLE_ORDERS . " o, " . TABLE_PRODUCTS . " p LEFT JOIN " . TABLE_PRODUCTS_DESCRIPTION . " pd on p.products_id = pd.products_id where opa.products_id = '" . (int)$_GET['products_id'] . "' and opa.orders_id = opb.orders_id and opb.products_id != '" . (int)$_GET['products_id'] . "' and opb.products_id = p.products_id and opb.orders_id = o.orders_id and p.products_status = '1' and pd.language_id = '" . (int)$languages_id . "' group by p.products_id order by o.date_purchased desc limit " . MAX_DISPLAY_ALSO_PURCHASED);
    $num_products_ordered = tep_db_num_rows($orders_query);
    if ($num_products_ordered >= MIN_DISPLAY_ALSO_PURCHASED) {

      $also_pur_prods_content = NULL;
		$count =1;
      while ($orders = tep_db_fetch_array($orders_query)) {
        $also_pur_prods_content .= '<div itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem" class="col-sm-6 col-md-4">';
        $also_pur_prods_content .= '  <div class="thumbnail"><meta itemprop="position" content="' . $count . '" />';
        $also_pur_prods_content .= '    <a href="' . tep_href_link('product_info.php', 'products_id=' . $orders['products_id']) . '">' . tep_image('images/' . $orders['products_image'], $orders['products_name'], SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT) . '</a>';
        $also_pur_prods_content .= '    <div class="caption">';
        $also_pur_prods_content .= '      <span class="text-center"><a itemprop="url" href="' . tep_href_link('product_info.php', 'products_id=' . $orders['products_id']) . '">' . $orders['products_name'] . '</a></span>';
        $also_pur_prods_content .= '    </div>';
        $also_pur_prods_content .= '  </div>';
        $also_pur_prods_content .= '</div>';
		$count++;
      }

?>

									  <br />
									  <div itemscope itemtype="http://schema.org/ItemList">
										<meta itemprop="itemListOrder" content="http://schema.org/ItemListUnordered" />
										<meta itemprop="numberOfItems" content="<?php echo (int)$num_products_ordered; ?>" />
										<span itemprop="name"><?php echo TEXT_ALSO_PURCHASED_PRODUCTS; ?></span>

										<div  class="row">
										  <?php echo $also_pur_prods_content; ?>
										</div>
										
									  </div>

<?php
    }
  }
?>
