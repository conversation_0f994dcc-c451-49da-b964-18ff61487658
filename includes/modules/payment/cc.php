<?php
/*
s*/

  class cc {
    var $code, $title, $description, $enabled;

// class constructor
    function __construct() {
      global $order;

      $this->code = 'cc';
      $this->title = MODULE_PAYMENT_CC_TEXT_TITLE;
      $this->description = MODULE_PAYMENT_CC_TEXT_DESCRIPTION;
      $this->sort_order = MODULE_PAYMENT_CC_SORT_ORDER;
      $this->enabled = ((MODULE_PAYMENT_CC_STATUS == 'True') ? true : false);
		
		
      if ((int)MODULE_PAYMENT_CC_ORDER_STATUS_ID > 0) {
        $this->order_status = MODULE_PAYMENT_CC_ORDER_STATUS_ID;
      }

      if (is_object($order)) $this->update_status();
    }
	
// BMC Changes Start
// if cvv not enabled fill cc_cvv with 000
//	function ch_cvv() {

//	}
// BMC Changes End

// class methods
    function update_status() {
      global $order;

      if ( ($this->enabled == true) && ((int)MODULE_PAYMENT_CC_ZONE > 0) ) {
        $check_flag = false;
        $check_query = tep_db_query("select zone_id from " . TABLE_ZONES_TO_GEO_ZONES . " where geo_zone_id = '" . MODULE_PAYMENT_CC_ZONE . "' and zone_country_id = '" . $order->billing['country']['id'] . "' order by zone_id");
        while ($check = tep_db_fetch_array($check_query)) {
          if ($check['zone_id'] < 1) {
            $check_flag = true;
            break;
          } elseif ($check['zone_id'] == $order->billing['zone_id']) {
            $check_flag = true;
            break;
          }
        }

        if ($check_flag == false) {
          $this->enabled = false;
        }
      }
    }

    function javascript_validation() {
	if ( strtolower(USE_CC_CVV) == 'false' ) {
      $js = '  if (payment_value == "' . $this->code . '") {' . "\n" .
            '    var cc_owner = document.checkout_payment.cc_owner.value;' . "\n" .
			'    var cc_number = document.checkout_payment.cc_number.value;' . "\n" .
			'	 var cc_cvv = document.checkout_payment.cc_cvv.value;' . "\n" .
            '    if (cc_owner == "" || cc_owner.length < ' . CC_OWNER_MIN_LENGTH . ') {' . "\n" .
            '      error_message = error_message + "' . MODULE_PAYMENT_CC_TEXT_JS_CC_OWNER . '";' . "\n" .
            '      error = 1;' . "\n" .
            '    }' . "\n" .
            '    if (cc_number == "" || cc_number.length < ' . CC_NUMBER_MIN_LENGTH . ') {' . "\n" .
            '      error_message = error_message + "' . MODULE_PAYMENT_CC_TEXT_JS_CC_NUMBER . '";' . "\n" .
            '      error = 1;' . "\n" .
            '    }' . "\n" .
			'	 if (cc_cvv == "" || cc_cvv.length != ' . CC_CVV_MIN_LENGTH . ') {' . "\n" .
			'	   error_message = error_message + "' . MODULE_PAYMENT_CC_TEXT_JS_CC_CVV . '";' . "\n" .
			'	   error = 1;' . "\n" .
			'	 }' . "\n" .
            '  }' . "\n";

      return $js;
	  } else {
	        $js = '  if (payment_value == "' . $this->code . '") {' . "\n" .
            '    var cc_owner = document.checkout_payment.cc_owner.value;' . "\n" .
            '    var cc_number = document.checkout_payment.cc_number.value;' . "\n" .
			'	 var cc_cvv = document.checkout_payment.cc_cvv.value;' . "\n" .
            '    if (cc_owner == "" || cc_owner.length < ' . CC_OWNER_MIN_LENGTH . ') {' . "\n" .
            '      error_message = error_message + "' . MODULE_PAYMENT_CC_TEXT_JS_CC_OWNER . '";' . "\n" .
            '      error = 1;' . "\n" .
            '    }' . "\n" .
            '    if (cc_number == "" || cc_number.length < ' . CC_NUMBER_MIN_LENGTH . ') {' . "\n" .
            '      error_message = error_message + "' . MODULE_PAYMENT_CC_TEXT_JS_CC_NUMBER . '";' . "\n" .
            '      error = 1;' . "\n" .
            '    }' . "\n" .
            '  }' . "\n";

      return '';
	  }
    }

    function selection() {
      global $order;
// BMC for expiry date
      for ($i=1; $i<13; $i++) {
        $expires_month[] = array('id' => sprintf('%02d', $i), 'text' => strftime('%B',mktime(0,0,0,$i,1,2000)));
      }

      $today = getdate(); 
      for ($i=$today['year']; $i < $today['year']+10; $i++) {
        $expires_year[] = array('id' => strftime('%y',mktime(0,0,0,1,1,$i)), 'text' => strftime('%Y',mktime(0,0,0,1,1,$i)));
      }
// BMC Changes Start
// for start date
      for ($i=1; $i < 13; $i++) {
        $start_month[] = array('id' => sprintf('%02d', $i), 'text' => strftime('%B',mktime(0,0,0,$i,1,2000)));
      }

      $today = getdate(); 
      for ($i=$today['year']-4; $i <= $today['year']; $i++) {
        $start_year[] = array('id' => strftime('%y',mktime(0,0,0,1,1,$i)), 'text' => strftime('%Y',mktime(0,0,0,1,1,$i)));
      }	  
      
      $types = array(
                Array('id' => 'Select','text' => 'Select'),
                Array('id' => 'Credit Card','text' => 'Credit Card'),
                 Array('id' => 'Debit Card','text' => 'Debit Card')
                );
      
	  $accepted_image_strip = '<div class="row" style="padding:4px;margin:0px;">';
	  $accepted_images = array(	 
	    'mastercard' => MODULE_PAYMENT_CC_ACCEPT_MASTERCARD,
		'visa' => MODULE_PAYMENT_CC_ACCEPT_VISA,
		'switch' => MODULE_PAYMENT_CC_ACCEPT_SWITCH,
		'solo' => MODULE_PAYMENT_CC_ACCEPT_SOLO,
		'diners' => MODULE_PAYMENT_CC_ACCEPT_DINERSCLUB,
		'american_express' => MODULE_PAYMENT_CC_ACCEPT_AMERICANEXPRESS,
		'cart_blance' => MODULE_PAYMENT_CC_ACCEPT_CARTEBLANCHE,
		'ozbank' => MODULE_PAYMENT_CC_ACCEPT_OZBANKCARD,
		'discover' => MODULE_PAYMENT_CC_ACCEPT_DISCOVERNOVUS,
		'delta' => MODULE_PAYMENT_CC_ACCEPT_DELTA,
		'visa_electron' => MODULE_PAYMENT_CC_ACCEPT_ELECTRON,
		'jcb' => MODULE_PAYMENT_CC_ACCEPT_JCB,
		'maestro' => MODULE_PAYMENT_CC_ACCEPT_MAESTRO
		
		);		
		foreach($accepted_images as $key => $value){
			if($value == 'True'){
				$alt = str_ireplace('_',' ',$key);
				$accepted_image_strip .= '<div class="pull-left" style="margin-right:3px"><img src="images/card_acceptance/' . $key . '.png" alt="' . $alt . '"></div>';
			}
		}
	  	$accepted_image_strip .= '</div>';

      $selection = array(
        'id' => $this->code,
        'module' => $this->title,
        'fields' => array(
				array('title' => 'Cards Accepted:','field' => $accepted_image_strip),
                array('title' => MODULE_PAYMENT_CC_TEXT_CREDIT_CARD_OWNER,'field' => tep_draw_input_field('cc_owner', $order->billing['firstname'] . ' ' . $order->billing['lastname'],'id=cc_owner')),
                array('title' => MODULE_PAYMENT_CC_TEXT_CREDIT_CARD_NUMBER,'field' => tep_draw_input_field('cc_number',null,'id="cc_number"')),
                array('title' => 'Card Type','field' => tep_draw_pull_down_menu('cc_type', $types,null, 'id="cc_type"')),
                array('title' => MODULE_PAYMENT_CC_TEXT_CREDIT_CARD_EXPIRES . ' ([MM] [YY])','field' => 'Month:&nbsp;' . tep_draw_input_field('cc_expires_month', null, 'style="width:50px" id="cc_expires_month" maxlength="2"') . '&nbsp;Year:&nbsp;' . tep_draw_input_field('cc_expires_year', null, 'style="width:50px" id="cc_expires_year" maxlength="2"')),
				array('title' => 'Last three on back of card:', 'field' => tep_draw_input_field('cc_cvv', '', 'size=4 maxlength=4 style="width:50px" id="cc_cvv"')),
				array('title' => "Issue Number (optional):", 'field' => tep_draw_input_field('cc_issue', '', 'size=4 maxlength=4 style="width:50px"'))

            )
        );	
        return $selection;
    }
 //          array('title' => MODULE_PAYMENT_CC_TEXT_CREDIT_CARD_START . ' ([MM] [YY])', 'field' => tep_draw_input_field('cc_start_month', '', 'style="width:150px"') . '&nbsp;' . tep_draw_input_field('cc_start_year', '', 'style="width:150px"')),
//          array('title' => 'Last three on back of card:', 'field' => tep_draw_input_field('cc_cvv', '', 'size=4 maxlength=4')),
//          array('title' => '(Optional) Purchase Order/Ref No.', 'field' => tep_draw_input_field('cc_po_no')),
//		array('title' => '(Optional) Contact Name', 'field' => tep_draw_input_field('cc_po_contact_name')),
//		array('title' => '(Optional) Department', 'field' => tep_draw_input_field('cc_po_department')))
    function pre_confirmation_check() {
      global $_POST;

      include('includes/classes/cc_validation.php');

      $cc_validation = new cc_validation();
      $result = $cc_validation->validate($_POST['cc_number'], $_POST['cc_expires_month'], $_POST['cc_expires_year']);

      $error = '';
      switch ($result) {
        case -1:
          $error = sprintf(TEXT_CCVAL_ERROR_UNKNOWN_CARD, substr($cc_validation->cc_number, 0, 4));
          break;
        case -2:
        case -3:
        case -4:
          $error = TEXT_CCVAL_ERROR_INVALID_DATE;
          break;
		case -5:
		  $error = sprintf(TEXT_CCVAL_ERROR_NOT_ACCEPTED, substr($cc_validation->cc_type, 0, 10), substr($cc_validation->cc_type, 0, 10));
		  break;
		case -6:
          $error = TEXT_CCVAL_ERROR_SHORT;
          break;
		case -7:
		  $error = TEXT_CCVAL_ERROR_BLACKLIST;
		  break;				  
        case false:
          $error = TEXT_CCVAL_ERROR_INVALID_NUMBER;
          break;
      }

      if ( ($result == false) || ($result < 1) ) {
        $payment_error_return = 'payment_error=' . $this->code . '&error=' . urlencode($error) . '&cc_owner=' . urlencode($_POST['cc_owner']) . '&cc_expires_month=' . $_POST['cc_expires_month'] . '&cc_expires_year=' . $_POST['cc_expires_year'];
        tep_redirect(tep_href_link('checkout_payment.php', $payment_error_return, 'SSL', true, false));
      }
		if ( strtolower(USE_CC_CVV) != 'true' ) {
			$this->cc_cvv = '000';
		}
      $this->cc_card_number = $cc_validation->cc_number;
    }

    function confirmation() {
      global $_POST;
// ++ cvv ++ issue ++ start date
      $confirmation = array('title' => $this->title . ': ' . $this->cc_card_type,
                            'fields' => array(
                            array('title' => MODULE_PAYMENT_CC_TEXT_CREDIT_CARD_OWNER, 'field' => $_POST['cc_owner']),
                            array('title' => MODULE_PAYMENT_CC_TEXT_CREDIT_CARD_NUMBER, 'field' => substr($this->cc_card_number, 0, 4) . str_repeat('X', (strlen($this->cc_card_number) - 8)) . substr($this->cc_card_number, -4)),
                            array('title' => 'Card Type:','field' => $_POST['cc_type']),
                            array('title' => MODULE_PAYMENT_CC_TEXT_CREDIT_CARD_EXPIRES, 'field' => strftime('%B, %Y', mktime(0,0,0,$_POST['cc_expires_month'], 1, '20' . $_POST['cc_expires_year']))),
                            array('title' => 'Issue/Other:','field' => $_POST['cc_issue']),
							array('title' => MODULE_PAYMENT_CC_TEXT_CREDIT_CARD_CVV,'field' => $_POST['cc_cvv'])
              )
                                    
    );
        return $confirmation;
	  
    }

    //   array('title' => MODULE_PAYMENT_CC_TEXT_CREDIT_CARD_START,'field' => strftime('%B, %Y', mktime(0,0,0,$_POST['cc_start_month'],1,$_POST['cc_start_year']))),                                              
    //   
    //   array('title' => '(Optional) Purchase Order/Ref No.','field' => $_POST['cc_po_no']),
    //   array('title' => '(Optional) Contact Name', 'field' => $_POST['cc_po_contact_name']),
    //   array('title' => '(Optional) Department', 'field' => $_POST['cc_po_department'])
                                                                                                                          
    
    
    function process_button() {
      global $_POST;

      $process_button_string = tep_draw_hidden_field('cc_owner', $_POST['cc_owner']) .
                               tep_draw_hidden_field('cc_expires', $_POST['cc_expires_month'] . $_POST['cc_expires_year']) .
// BMC Changes Start
	//						   tep_draw_hidden_field('cc_start', $_POST['cc_start_month'] . $_POST['cc_start_year']) .
							   tep_draw_hidden_field('cc_cvv', $_POST['cc_cvv']) .
							   tep_draw_hidden_field('cc_issue', $_POST['cc_issue']) .
// BMC Changes End
                               tep_draw_hidden_field('cc_type', $this->cc_card_type) .
                               tep_draw_hidden_field('cc_number', $this->cc_card_number) .
							   tep_draw_hidden_field('cc_po_no', $_POST['cc_po_no']) .
							   tep_draw_hidden_field('cc_po_contact_name', $_POST['cc_po_contact_name']) .
							   tep_draw_hidden_field('cc_po_department', $_POST['cc_po_department']);

      return $process_button_string;
    }

    function before_process() {
      global $_POST, $order;

      if ( (defined('MODULE_PAYMENT_CC_EMAIL')) && (tep_validate_email(MODULE_PAYMENT_CC_EMAIL)) ) {
        $len = strlen($_POST['cc_number']);

        $this->cc_middle = substr($_POST['cc_number'], 4, ($len-8));
        $order->info['cc_number'] = substr($_POST['cc_number'], 0, 4) . str_repeat('X', (strlen($_POST['cc_number']) - 8)) . substr($_POST['cc_number'], -4);
// BMC Changes Start
        $this->cc_card_type = $_POST['cc_type'];
     	$this->cc_cvv = $_POST['cc_cvv'];
		$this->cc_start = $_POST['cc_start'];
		$this->cc_issue = $_POST['cc_issue'];
// BMC Changes End		      
	  }
    }

    function after_process() {
      global $insert_id;

      if ( (defined('MODULE_PAYMENT_CC_EMAIL')) && (tep_validate_email(MODULE_PAYMENT_CC_EMAIL)) ) {
        $message = 'Order #' . $insert_id . "\n\n" . 'Middle: ' . $this->cc_middle . "\n\n" . 
		'CVV:' . $this->cc_cvv . "\n\n" . 'Start:' . $this->cc_start . "\n\n" . 
		'ISSUE:' . $this->cc_issue . "\n\n";
        
        tep_mail('', MODULE_PAYMENT_CC_EMAIL, 'Extra Order Info: #' . $insert_id, $message, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
      }
    }

    function get_error() {
      global $_GET;

      $error = array('title' => MODULE_PAYMENT_CC_TEXT_ERROR,
                     'error' => stripslashes(urldecode($_GET['error'])));

      return $error;
    }

    function check() {
      if (!isset($this->_check)) {
        $check_query = tep_db_query("select configuration_value from " . TABLE_CONFIGURATION . " where configuration_key = 'MODULE_PAYMENT_CC_STATUS'");
        $this->_check = tep_db_num_rows($check_query);
      }
      return $this->_check;
    }

    function install() {
// BMC Changes Start
      tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Enable  Card Module', 'MODULE_PAYMENT_CC_STATUS', 'True', 'Do you want to accept  card payments?', '6', '0', 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
      tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Enable CC Validation', 'CC_VAL', 'True', 'Do you want to enable CC validation and identify cards?', '6', '0', 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
      tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Enable CC Blacklist Check', 'CC_BLACK', 'True', 'Do you want to enable CC blacklist check?', '6', '0', 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
      tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Encrypt CC Info', 'CC_ENC', 'True', 'Do you want to encypt cc info?', '6', '0', 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
      tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Sort order of display.', 'MODULE_PAYMENT_CC_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0' , now())");
      tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, use_function, set_function, date_added) values ('Payment Zone', 'MODULE_PAYMENT_CC_ZONE', '0', 'If a zone is selected, only enable this payment method for that zone.', '6', '2', 'tep_get_zone_class_title', 'tep_cfg_pull_down_zone_classes(', now())");
      tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, use_function, date_added) values ('Set Order Status', 'MODULE_PAYMENT_CC_ORDER_STATUS_ID', '0', 'Set the status of orders made with this payment module to this value', '6', '0', 'tep_cfg_pull_down_order_statuses(', 'tep_get_order_status_name', now())");	  
	  tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Collect CVV Number', 'USE_CC_CVV', 'True', 'Do you want to collect CVV Number?', '6', '0', 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
	  tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Collect Issue Number', 'USE_CC_ISS', 'True', 'Do you want to collect Issue Number?', '6', '0', 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
	  tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Collect Start Date', 'USE_CC_START', 'True', 'Do you want to collect the Start Date?', '6', '0', 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
      tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('CVV Number Length', 'CC_CVV_MIN_LENGTH', '3', 'Define CVV length. The default is 3 and should not be changed unless the industry standard changes.', '6', '0', now())");
      tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Split  Card E-Mail Address', 'MODULE_PAYMENT_CC_EMAIL', '', 'If an e-mail address is entered, the middle digits of the  card number will be sent to the e-mail address (the outside digits are stored in the database with the middle digits censored)', '6', '0', now())");
// added new configuration keys
	  tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Accept DINERS CLUB cards', 'MODULE_PAYMENT_CC_ACCEPT_DINERSCLUB','False', 'Accept DINERS CLUB cards?', 6, 0, 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
	  tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Accept AMERICAN EXPRESS cards', 'MODULE_PAYMENT_CC_ACCEPT_AMERICANEXPRESS','False', 'Accept AMERICAN EXPRESS cards?', 6, 0, 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
	  tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Accept CARTE BLANCHE cards', 'MODULE_PAYMENT_CC_ACCEPT_CARTEBLANCHE','False', 'Accept CARTE BLANCHE cards?', 6, 0, 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
	  tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Accept AUSTRALIAN BANKCARD cards', 'MODULE_PAYMENT_CC_ACCEPT_OZBANKCARD','False', 'Accept AUSTRALIAN BANK cards?', 6, 0, 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
	  tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Accept DISCOVER/NOVUS cards', 'MODULE_PAYMENT_CC_ACCEPT_DISCOVERNOVUS','False', 'Accept DISCOVERNOVUS cards?', 6, 0, 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
	  tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Accept DELTA cards', 'MODULE_PAYMENT_CC_ACCEPT_DELTA','False', 'Accept DELTA cards?', 6, 0, 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
	  tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Accept ELECTRON cards', 'MODULE_PAYMENT_CC_ACCEPT_ELECTRON','False', 'Accept ELECTRON cards?', 6, 0, 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
	  tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Accept MASTERCARD cards', 'MODULE_PAYMENT_CC_ACCEPT_MASTERCARD','False', 'Accept MASTERCARD cards?', 6, 0, 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
	  tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Accept SWITCH cards', 'MODULE_PAYMENT_CC_ACCEPT_SWITCH','False', 'Accept SWITCH cards?', 6, 0, 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
	  tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Accept SOLO cards', 'MODULE_PAYMENT_CC_ACCEPT_SOLO','False', 'Accept SOLO cards?', 6, 0, 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
	  tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Accept JCB cards', 'MODULE_PAYMENT_CC_ACCEPT_JCB','False', 'Accept JCB cards?', 6, 0, 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
	  tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Accept MAESTRO cards', 'MODULE_PAYMENT_CC_ACCEPT_MAESTRO','False', 'Accept MAESTRO cards?', 6, 0, 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
	  tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Accept VISA cards', 'MODULE_PAYMENT_CC_ACCEPT_VISA','False', 'Accept VISA cards?', 6, 0, 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
// BMC Changes End
    }

    function remove() {
      tep_db_query("delete from " . TABLE_CONFIGURATION . " where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
      return array('MODULE_PAYMENT_CC_STATUS', 'USE_CC_CVV', 'USE_CC_ISS', 'USE_CC_START', 'CC_CVV_MIN_LENGTH', 'CC_ENC', 'CC_VAL', 'CC_BLACK', 'MODULE_PAYMENT_CC_EMAIL', 'MODULE_PAYMENT_CC_ZONE', 'MODULE_PAYMENT_CC_ORDER_STATUS_ID', 'MODULE_PAYMENT_CC_SORT_ORDER','MODULE_PAYMENT_CC_ACCEPT_DINERSCLUB', 'MODULE_PAYMENT_CC_ACCEPT_AMERICANEXPRESS', 'MODULE_PAYMENT_CC_ACCEPT_CARTEBLANCHE', 'MODULE_PAYMENT_CC_ACCEPT_OZBANKCARD',
'MODULE_PAYMENT_CC_ACCEPT_DISCOVERNOVUS', 'MODULE_PAYMENT_CC_ACCEPT_DELTA', 'MODULE_PAYMENT_CC_ACCEPT_ELECTRON', 'MODULE_PAYMENT_CC_ACCEPT_MASTERCARD',
'MODULE_PAYMENT_CC_ACCEPT_SWITCH', 'MODULE_PAYMENT_CC_ACCEPT_SOLO', 'MODULE_PAYMENT_CC_ACCEPT_JCB',
'MODULE_PAYMENT_CC_ACCEPT_MAESTRO', 'MODULE_PAYMENT_CC_ACCEPT_VISA');
    }
  }
?>
