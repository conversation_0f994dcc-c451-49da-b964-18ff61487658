<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2016 osCommerce

  Released under the GNU General Public License
*/

  class ht_product_attributes_class {
    var $code = 'ht_product_attributes_class';
    var $group = 'header_tags';
    var $title;
    var $description;
    var $sort_order;
    var $enabled = false;

    function __construct() {
      $this->title = "Product Attributes class";
      $this->description = "Inits product attributes class";

      if ( defined('MODULE_HEADER_TAGS_PRODUCT_ATTRIBUTES_STATUS') ) {
        $this->sort_order = MODULE_HEADER_TAGS_PRODUCT_ATTRIBUTES_SORT_ORDER;
        $this->enabled = (MODULE_HEADER_TAGS_PRODUCT_ATTRIBUTES_STATUS == 'True');
      }
    }

    function execute() {
      global $PHP_SELF, $oscTemplate, $languages_id, $product_check, $products_attributes;
      
		//if ( (basename($PHP_SELF) == 'product_info.php') || (basename($PHP_SELF) == 'product_reviews.php') ) {
			if (isset($_GET['products_id'])) {
				require_once('includes/classes/attributes.class.php');
				$products_attributes = new tcs_product_attributes($_GET['products_id'],1);
				if ($products_attributes->has_attributes){
					$products_attributes->get_attributes();
					if ($products_attributes->has_variations) $products_attributes->get_variations();
					
				}
				//print_rr($products_attributes);
			}
		//}
	}

    function isEnabled() {
      return $this->enabled;
    }

    function check() {
      return defined('MODULE_HEADER_TAGS_PRODUCT_ATTRIBUTES_STATUS');
    }

    function install() {
      tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Enable Product Title Module', 'MODULE_HEADER_TAGS_PRODUCT_ATTRIBUTES_STATUS', 'True', 'Do you want to allow product titles to be added to the page title?', '6', '1', 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
      tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Sort Order', 'MODULE_HEADER_TAGS_PRODUCT_ATTRIBUTES_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', now())");
     }

    function remove() {
      tep_db_query("delete from configuration where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
      return array('MODULE_HEADER_TAGS_PRODUCT_ATTRIBUTES_STATUS', 'MODULE_HEADER_TAGS_PRODUCT_ATTRIBUTES_SORT_ORDER');
    }
  }
  