<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2016 osCommerce

  Released under the GNU General Public License
*/

  class ht_banners_qry {
    var $code = 'ht_banners_qry';
    var $group = 'header_tags';
    var $title;
    var $description;
    var $sort_order;
    var $enabled = false;

    function __construct() {
      $this->title = 'Banner Query';
      $this->description = 'Allows banners to be displayed with one sql query';

      if ( defined('MODULE_HEADER_TAGS_BANNERS_QRY_STATUS') ) {
        $this->sort_order = MODULE_HEADER_TAGS_BANNERS_QRY_SORT_ORDER;
        $this->enabled = (MODULE_HEADER_TAGS_BANNERS_QRY_STATUS == 'True');
      }
    }

    function execute() {
         global $PHP_SELF, $oscTemplate, $banners;
	$bannerQ = tep_db_query("select banners_id, banners_title, banners_group, banners_image, banners_html_text from " . TABLE_BANNERS . " where status = '1'");
	$banners = array();
	echo 'hey:';
	while ($bannersT = tep_db_fetch_array($bannerQ)){
		echo $bannersT['banners_group'];
		$banners[$bannersT['banners_group']] = $bannersT;
	} 
    }

    function isEnabled() {
      return $this->enabled;
    }

    function check() {
      return defined('MODULE_HEADER_TAGS_BANNERS_QRY_STATUS');
    }

    function install() {
      tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Enable banner sql Module', 'MODULE_HEADER_TAGS_BANNERS_QRY_STATUS', 'True', '', '6', '1', 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
      tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Sort Order', 'MODULE_HEADER_TAGS_BANNERS_QRY_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', now())");
     }

    function remove() {
      tep_db_query("delete from configuration where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
      return array('MODULE_HEADER_TAGS_BANNERS_QRY_STATUS', 'MODULE_HEADER_TAGS_BANNERS_QRY_SORT_ORDER');
    }
  }
  