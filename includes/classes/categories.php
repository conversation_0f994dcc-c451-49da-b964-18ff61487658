<?php
/*
  $Id$

osCommerce, Open Source E-Commerce Solutions
http://www.oscommerce.com

Copyright (c) 2015 osCommerce

Released under the GNU General Public License
*/

require('includes/application_top.php');

require('includes/classes/currencies.php');
$currencies = new currencies();
//require('includes/functions/googleCategoryfunctions.php');

//require('ext/Google/ProductsSample.php');

 $products_blockFreeShip_yes = false;
 $products_blockFreeShip_no  = true;
 $products_digiProduct_yes = false;
 $products_digiProduct_no  = true;
 $products_request_quote_yes = false;
 $products_request_quote_no  = true;
 
function console_log($output, $with_script_tags = true) {
    $js_code = 'console.log(' . json_encode($output, JSON_HEX_TAG) . ');';
    if ($with_script_tags) {
        $js_code = '<script>' . $js_code . '</script>';
    }
    echo $js_code;
}


function tep_get_products_video_url($products_id, $language_id)
{
    $product_query = tep_db_query("select products_video_url from products_description where products_id = '" . (int) $products_id . "' and language_id = '" . (int) $language_id . "'");
    $product       = tep_db_fetch_array($product_query);
    
    return $product['products_video_url'];
}


function tep_get_models($models_array = '') // Function modified from tep_get_manufacturers()
{
    global $language, $first, $last;
    if (!is_array($models_array))
        $models_array = array();
    $models_query = tep_db_query("SELECT products_id,
                                         products_model 
                                  FROM products 
                                  ORDER BY products_model");
    $count        = 0;
    while ($models = tep_db_fetch_array($models_query)) {
        if ($count == 0) {
            $first = $models['products_model'];
        }
        $models_array[] = array(
            'id' => $models['products_id'],
            'text' => $models['products_model']
        );
        $count++;
        $last = $models['products_model'];
    }
    
    return $models_array;
}
// end mark

$action = (isset($_GET['action']) ? $_GET['action'] : '');
$debug = 'start_';
$sorting = false;
if (@tep_not_null($action)) {
    // Ultimate SEO URLs v2.2d
    // If the action will affect the cache entries
    if (preg_match("/(insert|update|setflag)/i", $action))
        include_once('includes/reset_seo_cache.php');
    switch ($action) {
//sort order
		case 'beginsort':
			$sorting = true;
		break;        
		case 'setsortorder':
			$count = 0;
				for ($i=0, $n=sizeof($_POST['products_id']); $i<$n; $i++) {
					echo '<br>' . $count . ': ';
					$count++;
					tep_set_product_sort_order($_POST['products_id'][$i], $_POST['sortorder'][$i], $_POST['productsprice'][$i]);
				}
				$sorting = false;	
			if(isset($_POST['search'])){
				tep_redirect(tep_href_link('categories.php', 'search=' . $_POST['search']));
			} else { 
				tep_redirect(tep_href_link('categories.php', 'cPath=' . $_POST['cPath']));
			}
		break;
		//end sort order
		case 'setflag':
            if (($_GET['flag'] == '0') || ($_GET['flag'] == '1')) {
                if (isset($_GET['pID'])) {
                    tep_set_product_status($_GET['pID'], $_GET['flag']);
                }
                if (USE_CACHE == 'true') {
                    tep_reset_cache_block('categories');
                    tep_reset_cache_block('also_purchased');
                }
            }
            if (isset($_GET['search'])){
				tep_redirect(tep_href_link('categories.php', 'search=' . $_GET['search']));
			} else {
				tep_redirect(tep_href_link('categories.php', 'cPath=' . $_GET['cPath'] . '&pID=' . $_GET['pID']));
			}
            break;
		case 'setgflag':
            if (($_GET['flag'] == '0') || ($_GET['flag'] == '1')) {
                if (isset($_GET['pID'])) {
                    tep_set_product_google_status($_GET['pID'], $_GET['flag']);
                }
                if (USE_CACHE == 'true') {
                    tep_reset_cache_block('categories');
                    tep_reset_cache_block('also_purchased');
                }
            }            
            tep_redirect(tep_href_link('categories.php', 'cPath=' . $_GET['cPath'] . '&pID=' . $_GET['pID']));
            break;
        case 'insert_category':
        case 'update_category':
            if (isset($_POST['categories_id']))
                $categories_id = tep_db_prepare_input($_POST['categories_id']);
            $sort_order = tep_db_prepare_input($_POST['sort_order']);
            
            $sql_data_array = array(
                'sort_order' => (int) $sort_order
            );
            
            /* Mark, stuff for setting up google categories*/
            $models_array = tep_get_models(); // Mark: for google categpries
            
            if (isset($_POST['categories_id'])) $categories_id = tep_db_prepare_input($_POST['categories_id']);
            $sort_order                                 = tep_db_prepare_input($_POST['sort_order']);
            $google_category                            = $_POST['google_category'];
            $google_category_baseline                   = $_POST['google_category_baseline'];
            $sql_data_array                             = array('sort_order' => $sort_order);
            $sql_data_array['google_category']          = $google_category;
            $sql_data_array['google_category_baseline'] = $google_category_baseline;
            //die(print_r($google_category));
           /* end mark*/
            if ($action == 'insert_category') {
                $insert_sql_data = array(
                    'parent_id' => $current_category_id,
                    'date_added' => 'now()'
                );
                
                $sql_data_array = array_merge($sql_data_array, $insert_sql_data);
                
                tep_db_perform(TABLE_CATEGORIES, $sql_data_array);
                
                $categories_id = tep_db_insert_id();
            } elseif ($action == 'update_category') {
                $update_sql_data = array(
                    'last_modified' => 'now()'
                );
                
                $sql_data_array = array_merge($sql_data_array, $update_sql_data);
                
                tep_db_perform(TABLE_CATEGORIES, $sql_data_array, 'update', "categories_id = '" . (int) $categories_id . "'");
            }            
            $languages = tep_get_languages();
            for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                $categories_name_array            = $_POST['categories_name'];
                $categories_description_array     = $_POST['categories_description'];
                $categories_seo_description_array = $_POST['categories_seo_description'];
                $categories_seo_keywords_array    = $_POST['categories_seo_keywords'];
                $categories_seo_title_array       = $_POST['categories_seo_title'];
                
                $language_id = $languages[$i]['id'];
                
                $sql_data_array                               = array(
                    'categories_name' => tep_db_prepare_input($categories_name_array[$language_id])
                );
                $sql_data_array['categories_description']     = tep_db_prepare_input($categories_description_array[$language_id]);
                $sql_data_array['categories_seo_description'] = tep_db_prepare_input($categories_seo_description_array[$language_id]);
                $sql_data_array['categories_seo_keywords']    = tep_db_prepare_input($categories_seo_keywords_array[$language_id]);
                $sql_data_array['categories_seo_title']       = tep_db_prepare_input($categories_seo_title_array[$language_id]);
                
                if ($action == 'insert_category') {
                    $insert_sql_data = array(
                        'categories_id' => $categories_id,
                        'language_id' => $languages[$i]['id']
                    );
                    
                    $sql_data_array = array_merge($sql_data_array, $insert_sql_data);
                    
                    tep_db_perform(TABLE_CATEGORIES_DESCRIPTION, $sql_data_array);
                } elseif ($action == 'update_category') {
                    tep_db_perform(TABLE_CATEGORIES_DESCRIPTION, $sql_data_array, 'update', "categories_id = '" . (int) $categories_id . "' and language_id = '" . (int) $languages[$i]['id'] . "'");
                }
            }
            $categories_image = new upload('categories_image');
            $categories_image->set_destination(DIR_FS_CATALOG_IMAGES);
            if ($categories_image->parse() && $categories_image->save()) {
                tep_db_query("update categories set categories_image = '" . tep_db_input($categories_image->filename) . "' where categories_id = '" . (int) $categories_id . "'");
            }
            //delete image thumbnails
            foreach (glob(DIR_FS_CATALOG_IMAGES . 'thumbs/*/' . $categories_image->filename) as $filename) {
                @unlink($filename);
            }
            // end delete image thumbnails
            
            if (USE_CACHE == 'true') {
                tep_reset_cache_block('categories');
                tep_reset_cache_block('also_purchased');
            }
            
            tep_redirect(tep_href_link('categories.php', 'cPath=' . $cPath . '&cID=' . $categories_id));
            break;
        case 'delete_category_confirm':
            if (isset($_POST['categories_id'])) {
                $categories_id = tep_db_prepare_input($_POST['categories_id']);
                
                $categories      = tep_get_category_tree($categories_id, '', '0', '', true);
                $products        = array();
                $products_delete = array();
                
                for ($i = 0, $n = sizeof($categories); $i < $n; $i++) {
                    $products_ids_query = tep_db_query("select products_id from products_to_categories where categories_id = '" . (int) $categories[$i]['id'] . "'");
                    
                    while ($products_ids = tep_db_fetch_array($products_ids_query)) {
                        $products[$products_ids['products_id']]['categories'][] = $categories[$i]['id'];
                    }
                }
                
                reset($products);
                foreach ($products as $key => $value) {
                 
                    $category_ids = '';
                    
                    for ($i = 0, $n = sizeof($value['categories']); $i < $n; $i++) {
                        $category_ids .= "'" . (int) $value['categories'][$i] . "', ";
                    }
                    $category_ids = substr($category_ids, 0, -2);
                    
                    $check_query = tep_db_query("select count(*) as total from products_to_categories where products_id = '" . (int) $key . "' and categories_id not in (" . $category_ids . ")");
                    $check       = tep_db_fetch_array($check_query);
                    if ($check['total'] < '1') {
                        $products_delete[$key] = $key;
                    }
                }
                
                // removing categories can be a lengthy process
                tep_set_time_limit(0);
                for ($i = 0, $n = sizeof($categories); $i < $n; $i++) {
                    tep_remove_category($categories[$i]['id']);
                }
                
                reset($products_delete);
                foreach ($products_delete as $productId) {
                    tep_remove_product($productId);
                }
            }
            
            if (USE_CACHE == 'true') {
                tep_reset_cache_block('categories');
                tep_reset_cache_block('also_purchased');
            }
            
            tep_redirect(tep_href_link('categories.php', 'cPath=' . $cPath));
            break;
        case 'delete_product_confirm':
            if (isset($_POST['products_id']) && isset($_POST['product_categories']) && is_array($_POST['product_categories'])) {
                $products_id         = tep_db_prepare_input($_POST['products_id']);
                $product_categories = $_POST['product_categories'];
                
                for ($i = 0, $n = sizeof($product_categories); $i < $n; $i++) {
                    tep_db_query("delete from products_to_categories where products_id = '" . (int) $products_id . "' and categories_id = '" . (int) $product_categories[$i] . "'");
                }
                
                $product_categories_query = tep_db_query("select count(*) as total from products_to_categories where products_id = '" . (int) $products_id . "'");
                $product_categories       = tep_db_fetch_array($product_categories_query);
                
                if ($product_categories['total'] == '0') {
                    tep_remove_product($products_id);
                }
            }
            
            if (USE_CACHE == 'true') {
                tep_reset_cache_block('categories');
                tep_reset_cache_block('also_purchased');
            }
            
            tep_redirect(tep_href_link('categories.php', 'cPath=' . $cPath));
            break;
        case 'move_category_confirm':
            if (isset($_POST['categories_id']) && ($_POST['categories_id'] != $_POST['move_to_category_id'])) {
                $categories_id = tep_db_prepare_input($_POST['categories_id']);
                $new_parent_id = tep_db_prepare_input($_POST['move_to_category_id']);
                
                $path = explode('_', tep_get_generated_category_path_ids($new_parent_id));
                
                if (in_array($categories_id, $path)) {
                    $messageStack->add_session(ERROR_CANNOT_MOVE_CATEGORY_TO_PARENT, 'error');
                    
                    tep_redirect(tep_href_link('categories.php', 'cPath=' . $cPath . '&cID=' . $categories_id));
                } else {
                    tep_db_query("update categories set parent_id = '" . (int) $new_parent_id . "', last_modified = now() where categories_id = '" . (int) $categories_id . "'");
                    
                    if (USE_CACHE == 'true') {
                        tep_reset_cache_block('categories');
                        tep_reset_cache_block('also_purchased');
                    }
                    
                    tep_redirect(tep_href_link('categories.php', 'cPath=' . $new_parent_id . '&cID=' . $categories_id));
                }
            }
            
            break;
        case 'move_product_confirm':
            $products_id   = tep_db_prepare_input($_POST['products_id']);
            $new_parent_id = tep_db_prepare_input($_POST['move_to_category_id']);
            
            $duplicate_check_query = tep_db_query("select count(*) as total from products_to_categories where products_id = '" . (int) $products_id . "' and categories_id = '" . (int) $new_parent_id . "'");
            $duplicate_check       = tep_db_fetch_array($duplicate_check_query);
            if ($duplicate_check['total'] < 1)
                tep_db_query("update products_to_categories set categories_id = '" . (int) $new_parent_id . "' where products_id = '" . (int) $products_id . "' and categories_id = '" . (int) $current_category_id . "'");
            
            if (USE_CACHE == 'true') {
                tep_reset_cache_block('categories');
                tep_reset_cache_block('also_purchased');
            }
            
            tep_redirect(tep_href_link('categories.php', 'cPath=' . $new_parent_id . '&pID=' . $products_id));
            break;
        case 'insert_product':
        case 'update_product':
			if (isset($_GET['pID']))
                $products_id = tep_db_prepare_input($_GET['pID']);
            $products_date_available = tep_db_prepare_input($_POST['products_date_available']);
            
            $products_date_available = (date('Y-m-d') < $products_date_available) ? $products_date_available : 'null';
            
            $sql_data_array                  = array(
                'products_quantity' => (int) tep_db_prepare_input($_POST['products_quantity']),
                'products_model' => tep_db_prepare_input($_POST['products_model']),
                'products_price' => tep_db_prepare_input($_POST['products_price']),
                'products_date_available' => $products_date_available,
                'products_weight' => (float) tep_db_prepare_input($_POST['products_weight']),
                'products_status' => tep_db_prepare_input($_POST['products_status']),
		'products_google_status' => tep_db_prepare_input($_POST['products_google_status']),
                'products_tax_class_id' => tep_db_prepare_input($_POST['products_tax_class_id']),
                'products_sort_order' => tep_db_prepare_input($_POST['products_sort_order']),
                'products_request_quote' => (int) tep_db_prepare_input($_POST['products_request_quote']),
                'manufacturers_id' => (int) tep_db_prepare_input($_POST['manufacturers_id'])
            );
            $sql_data_array['products_gtin'] = (@tep_not_null($_POST['products_gtin'])) ? str_pad(tep_db_prepare_input($_POST['products_gtin']), 14, '0', STR_PAD_LEFT) : 'null';
            $full_data_array = $sql_data_array;
            
            $products_image = new upload('products_image');
            $products_image->set_destination(DIR_FS_CATALOG_IMAGES);
            if ($products_image->parse() && $products_image->save()) {
                $sql_data_array['products_image'] = tep_db_prepare_input($products_image->filename);
			}
            $full_data_array['products_image_filename'] = tep_db_prepare_input($_POST['products_image_filename']);
			//echo 'fdr: ' . $full_data_array['products_image_filename'];
            //delete image thumbnails
            foreach (glob(DIR_FS_CATALOG_IMAGES . 'thumbs/*/' . $products_image->filename) as $filename) {
                @unlink($filename);
            }
            // end delete image thumbnails
            
            if ($action == 'insert_product') {
                $insert_sql_data = array(
                    'products_date_added' => 'now()'
                );
                
                $sql_data_array = array_merge($sql_data_array, $insert_sql_data);
                $full_data_array = array_merge($sql_data_array, $full_data_array);
                tep_db_perform(TABLE_PRODUCTS, $sql_data_array);
                $products_id = tep_db_insert_id();
                
                tep_db_query("insert into products_to_categories (products_id, categories_id) values ('" . (int) $products_id . "', '" . (int) $current_category_id . "')");
            } elseif ($action == 'update_product') {
                $update_sql_data = array(
                    'products_last_modified' => 'now()'
                );
                
                $sql_data_array = array_merge($sql_data_array, $update_sql_data);
                $full_data_array = array_merge($sql_data_array, $full_data_array);
                tep_db_perform(TABLE_PRODUCTS, $sql_data_array, 'update', "products_id = '" . (int) $products_id . "'");
            }
            
            $languages = tep_get_languages();
            for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                $language_id     = $languages[$i]['id'];
                /* cleanup  */
                $productURL      = str_replace(array(
                    'http://',
                    'https://'
                ), '', $_POST['products_url'][$language_id]);
                $productVideoURL = str_replace(array(
                    'http://',
                    'https://'
                ), '', $_POST['products_video_url'][$language_id]);
                
                $sql_data_array                             = array(
                    'products_name' => tep_db_prepare_input($_POST['products_name'][$language_id]),
                    'products_description' => tep_db_prepare_input($_POST['products_description'][$language_id]),
                    'products_url' => tep_db_prepare_input($productURL)
                );
                $sql_data_array['products_seo_description'] = tep_db_prepare_input($_POST['products_seo_description'][$language_id]);
                $sql_data_array['products_seo_keywords']    = tep_db_prepare_input($_POST['products_seo_keywords'][$language_id]);
                $sql_data_array['products_seo_title']       = tep_db_prepare_input($_POST['products_seo_title'][$language_id]);
                $sql_data_array['products_video_url']       = tep_db_prepare_input($productVideoURL);
                
                if ($action == 'insert_product') {
                    $insert_sql_data = array(
                        'products_id' => $products_id,
                        'language_id' => $language_id
                    );
                    
                    $sql_data_array = array_merge($sql_data_array, $insert_sql_data);
                    $full_data_array = array_merge($sql_data_array,$full_data_array);
                    tep_db_perform(TABLE_PRODUCTS_DESCRIPTION, $sql_data_array);
                } elseif ($action == 'update_product') {
					$full_data_array = array_merge($sql_data_array,$full_data_array);
                    tep_db_perform(TABLE_PRODUCTS_DESCRIPTION, $sql_data_array, 'update', "products_id = '" . (int) $products_id . "' and language_id = '" . (int) $language_id . "'");
                }
            }
			          // start indvship
            $tmp_products_ship_price = round(tep_db_prepare_input($_POST['products_ship_price']), 4);
           /* if ($tmp_products_ship_price == 0) {
                $tmp_products_ship_price = null;
            };*/
            $tmp_products_ship_qty = round(tep_db_prepare_input($_POST['products_ship_qty']), 4);
            if ($tmp_products_ship_qty == 0) {
                $tmp_products_ship_qty = null;
            };
            // end indvship
            
            $pi_sort_order = 0;
            $piArray       = array(
                0
            );
			foreach ($_POST as $key => $value) {
                // Update existing large product images
                if (preg_match('/^products_image_large_([0-9]+)$/', $key, $matches)) {
                    $pi_sort_order++;
                    
                    $sql_data_array = array(
                        'htmlcontent' => tep_db_prepare_input($_POST['products_image_htmlcontent_' . $matches[1]]),
                        'sort_order' => $pi_sort_order
                    );
                    
                    $t = new upload($key);
                    $t->set_destination(DIR_FS_CATALOG_IMAGES);
                    if ($t->parse() && $t->save()) {
                        $sql_data_array['image'] = tep_db_prepare_input($t->filename);
                    }
                    $full_data_array = array_merge($sql_data_array,$full_data_array);
                    tep_db_perform(TABLE_PRODUCTS_IMAGES, $sql_data_array, 'update', "products_id = '" . (int) $products_id . "' and id = '" . (int) $matches[1] . "'");
                    
                    $piArray[] = (int) $matches[1];
                } elseif (preg_match('/^products_image_large_new_([0-9]+)$/', $key, $matches)) {
                    // Insert new large product images
                    $sql_data_array = array(
                        'products_id' => (int) $products_id,
                        'htmlcontent' => tep_db_prepare_input($_POST['products_image_htmlcontent_new_' . $matches[1]])
                    );
                    
                    $t = new upload($key);
                    $t->set_destination(DIR_FS_CATALOG_IMAGES);
                    if ($t->parse() && $t->save()) {
                        $pi_sort_order++;
                        
                        $sql_data_array['image']      = tep_db_prepare_input($t->filename);
                        $sql_data_array['sort_order'] = $pi_sort_order;
                        $full_data_array = array_merge($sql_data_array,$full_data_array);
                        tep_db_perform(TABLE_PRODUCTS_IMAGES, $sql_data_array);
                        
                        $piArray[] = tep_db_insert_id();
                    }
                }
            }
          
            $product_images_query = tep_db_query("select image from products_images where products_id = '" . (int) $products_id . "' and id not in (" . implode(',', $piArray) . ")");
            if (tep_db_num_rows($product_images_query)) {
                while ($product_images = tep_db_fetch_array($product_images_query)) {
                    $duplicate_image_query = tep_db_query("select count(*) as total from products_images where image = '" . tep_db_input($product_images['image']) . "'");
                    $duplicate_image       = tep_db_fetch_array($duplicate_image_query);
                    
                    if ($duplicate_image['total'] < 2) {
                        if (file_exists(DIR_FS_CATALOG_IMAGES . $product_images['image'])) {
                            @unlink(DIR_FS_CATALOG_IMAGES . $product_images['image']);
                        }
                    }
                }
                
                tep_db_query("delete from products_images where products_id = '" . (int) $products_id . "' and id not in (" . implode(',', $piArray) . ")");
            }
            //indvship
            $sql_shipping_array    = array(
                'products_ship_key' => tep_db_prepare_input($_POST['products_ship_key']),
                'products_ship_methods_id' => tep_db_prepare_input($_POST['products_ship_methods_id']),
                'products_ship_price' => $tmp_products_ship_price,
                'products_ship_qty' => $tmp_products_ship_qty,
                'products_ship_flags' => tep_db_prepare_input($_POST['products_ship_flags_blockFreeShip']) . ',' . tep_db_prepare_input($_POST['products_ship_flags_digiProduct'])
            );
            $sql_shipping_id_array = array(
                'products_id' => (int) $products_id
            );
            $products_ship_query   = tep_db_query("SELECT * FROM products_shipping WHERE products_id = " . (int) $products_id);
            if (tep_db_num_rows($products_ship_query) > 0) {
                if (($_POST['products_ship_key'] == '') && ($_POST['products_ship_methods_id'] == '') && ($_POST['products_ship_price'] == '') && ($_POST['products_ship_qty'] == '') && ($_POST['products_ship_flags'] == '')) {
                    tep_db_query("DELETE FROM products_shipping where products_id = '" . (int) $products_id . "'");
                } else {
                    tep_db_perform('products_shipping', $sql_shipping_array, 'update', "products_id = '" . (int) $products_id . "'");
                }
            } else {
                if (($_POST['products_ship_key'] != '') || ($_POST['products_ship_methods_id'] != '') || ($_POST['products_ship_price'] != '') || ($_POST['products_ship_qty'] != '') || ($_POST['products_ship_flags_blockFreeShip'] != '') || ($_POST['products_ship_flags_digiProduct'] != '')) {
                    $sql_ship_array = array_merge($sql_shipping_array, $sql_shipping_id_array);
                    tep_db_perform('products_shipping', $sql_ship_array, 'insert');
                }
            }
            // end indvship 
			$full_data_array = array_merge($sql_data_array,$full_data_array);
            $full_data_array = array_merge($sql_shipping_id_array,$full_data_array);
            if (USE_CACHE == 'true') {
                tep_reset_cache_block('categories');
                tep_reset_cache_block('also_purchased');
            }
					
		
					
           // $tempLog->lwrite('Categories: Done ');
            //tep_redirect(tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $products_id));
            break;
        case 'copy_to_confirm':
            if (isset($_POST['products_id']) && isset($_POST['categories_id'])) {
                $products_id   = tep_db_prepare_input($_POST['products_id']);
                $categories_id = tep_db_prepare_input($_POST['categories_id']);
                
                if ($_POST['copy_as'] == 'link') {
                    if ($categories_id != $current_category_id) {
                        $check_query = tep_db_query("select count(*) as total from products_to_categories where products_id = '" . (int) $products_id . "' and categories_id = '" . (int) $categories_id . "'");
                        $check       = tep_db_fetch_array($check_query);
                        if ($check['total'] < '1') {
                            tep_db_query("insert into products_to_categories (products_id, categories_id) values ('" . (int) $products_id . "', '" . (int) $categories_id . "')");
                        }
                    } else {
                        $messageStack->add_session(ERROR_CANNOT_LINK_TO_SAME_CATEGORY, 'error');
                    }
                } else if ($_POST['copy_as'] == 'link_attributes') {
                    if ($categories_id != $current_category_id) {
                        $check_query = tep_db_query("select count(*) as total from products_to_categories p2c, products_to_categories_attrib p2ca where p2c.products_id = '" . (int) $products_id . "' and p2c.categories_id = '" . (int) $categories_id . "' and p2c.products_to_categories_attribs_id = p2ca.products_to_categories_attribs_id");
                        $check       = tep_db_fetch_array($check_query);
						$attribPartNumber	= tep_db_prepare_input($_POST['attribPartNumber']);
                        $debug .= "Attempting_";
                        if ($check['total'] < '1') {
                            $attributesArray = [];
                            foreach($_POST['id'] as $key=>$value){
                               $attributesArray[$key] = $value;
                               // $debug .= "_key_" . $key . "_" . $value;
                            }
                            tep_db_query("insert into products_to_categories_attrib (products_id, attributes, products_model) values ('" . (int) $products_id . "','" . tep_get_uprid($products_id,$attributesArray) . "','". $attribPartNumber . "')");
                            tep_db_query("insert into products_to_categories (products_id, categories_id, products_to_categories_attribs_id) values ('" . (int) $products_id . "', '" . (int) $categories_id . "', " . tep_db_insert_id() . ")");
                        }
                        $debug .= "_" . tep_db_prepare_input(implode(',',$attributesArray));
                    } else {
                        $messageStack->add_session(ERROR_CANNOT_LINK_TO_SAME_CATEGORY, 'error');
                    }
                } elseif ($_POST['copy_as'] == 'duplicate') {
                    //PRODUCT_SORT_ORDER ADDED TO THIS SELECT
                    // $product_query = tep_db_query("select products_quantity, products_model, products_image, products_price, products_date_available, products_weight, products_tax_class_id, manufacturers_id, products_gtin from products where products_id = '" . (int)$products_id . "'");
                    $product_query = tep_db_query("select products_quantity, products_model, products_image, products_price, products_date_available, products_weight, products_tax_class_id, manufacturers_id, products_gtin, products_sort_order, products_request_quote from products where products_id = '" . (int) $products_id . "' ORDER BY products_sort_order");
                    $product       = tep_db_fetch_array($product_query);
                    
                    //PRODUCT_SORT_ORDER ADDED TO THIS INSERT
                    // tep_db_query("insert into products (products_quantity, products_model,products_image, products_price, products_date_added, products_date_available, products_weight, products_status, products_tax_class_id, manufacturers_id) values ('" . tep_db_input($product['products_quantity']) . "', '" . tep_db_input($product['products_model']) . "', '" . tep_db_input($product['products_image']) . "', '" . tep_db_input($product['products_price']) . "',  now(), '" . tep_db_input($product['products_date_available']) . "', '" . tep_db_input($product['products_weight']) . "', '0', '" . (int)$product['products_tax_class_id'] . "', '" . (int)$product['manufacturers_id'] . "')");
                    tep_db_query("insert into products (products_quantity, products_model,products_image, products_price, products_date_added, products_date_available, products_weight, products_status, products_google_status, products_tax_class_id, manufacturers_id, products_gtin, products_sort_order, products_request_quote) values ('" . tep_db_input($product['products_quantity']) . "', '" . tep_db_input($product['products_model']) . "', '" . tep_db_input($product['products_image']) . "', '" . tep_db_input($product['products_price']) . "',  now(), " . (empty($product['products_date_available']) ? "null" : "'" . tep_db_input($product['products_date_available']) . "'") . ", '" . tep_db_input($product['products_weight']) . "', '0', '0', '" . (int) $product['products_tax_class_id'] . "', '" . (int) $product['manufacturers_id'] . "', '" . tep_db_input($product['products_gtin']) . "', '" . (int) $product['products_sort_order'] . "', '" . (int) $product['products_request_quote'] . "')");
                    $dup_products_id = tep_db_insert_id();
                    
                    //EOF Product sort o
                    
                    $description_query = tep_db_query("select language_id, products_name, products_description, products_url, products_seo_title, products_seo_description, products_seo_keywords, products_video_url from products_description where products_id = '" . (int) $products_id . "'");
                    while ($description = tep_db_fetch_array($description_query)) {
                        tep_db_query("insert into products_description (products_id, language_id, products_name, products_description, products_url, products_viewed, products_seo_title, products_seo_description, products_seo_keywords, products_video_url) values ('" . (int) $dup_products_id . "', '" . (int) $description['language_id'] . "', '" . tep_db_input($description['products_name']) . "', '" . tep_db_input($description['products_description']) . "', '" . tep_db_input($description['products_url']) . "', '0', '" . tep_db_input($description['products_seo_title']) . "', '" . tep_db_input($description['products_seo_description']) . "', '" . tep_db_input($description['products_seo_keywords']) . "', '" . tep_db_input($description['products_video_url']) . "')");
                    }
					
					$attributes_query = tep_db_query("select * from products_attributes where products_id = '" . (int)$products_id . "'");
                    while ($attributes = tep_db_fetch_array($attributes_query)) {
                       echo "INSERT INTO `products_attributes` ( `products_id`, `options_id`, `options_values_id`, `options_values_price`, `price_prefix`, `attribute_default`, `part_code`, `default`, `products_attributes_sort_order`, `dependson_options_id`, `dependson_options_values_id`) VALUES ('" . (int)$dup_products_id . "', '" . tep_db_input($attributes['options_id']) . "', '" . tep_db_input($attributes['options_values_id']) . "', '" . tep_db_input($attributes['options_values_price']) . "', '" . tep_db_input($attributes['price_prefix']) . "', '" . tep_db_input($attributes['part_code']) . "', '" . tep_db_input($attributes['default']) . "', '" . tep_db_input($attributes['attribute_default']) . "', '" . tep_db_input($attributes['products_attributes_sort_order']) . "', '" . tep_db_input($attributes['dependson_options_id']) . "', '" . tep_db_input($attributes['dependson_options_values_id']) . "')";

					   tep_db_query("INSERT INTO `products_attributes` ( `products_id`, `options_id`, `options_values_id`, `options_values_price`, `price_prefix`, `attribute_default`, `part_code`, `default`, `products_attributes_sort_order`, `dependson_options_id`, `dependson_options_values_id`) VALUES ('" . (int)$dup_products_id . "', '" . tep_db_input($attributes['options_id']) . "', '" . tep_db_input($attributes['options_values_id']) . "', '" . tep_db_input($attributes['options_values_price']) . "', '" . tep_db_input($attributes['price_prefix']) . "', '" . tep_db_input($attributes['part_code']) . "', '" . tep_db_input($attributes['default']) . "', '" . tep_db_input($attributes['attribute_default']) . "', '" . tep_db_input($attributes['products_attributes_sort_order']) . "', '" . tep_db_input($attributes['dependson_options_id']) . "', '" . tep_db_input($attributes['dependson_options_values_id']) . "')");

                    }
					$variations_query = tep_db_query("select * from products_variations where products_id = '" . (int)$products_id . "'");
                    while ($variations = tep_db_fetch_array($variations_query)) {
                       tep_db_query("INSERT INTO `products_variations` (`products_id`, `image_id`, `attributes`, `price`, `model`, `gtin`, `sort_order`) VALUES ('" . (int)$dup_products_id . "', '" . tep_db_input($variations['image_id']) . "', '" . tep_db_input($variations['attributes']) . "', '" . tep_db_input($variations['price']) . "', '" . tep_db_input($variations['model']) . "', '" . tep_db_input($variations['gtin']) . "', '" . tep_db_input($variations['sort_order']) . "')");

                    }
					$products_related_query = tep_db_query("select * from products_related where products_related_products_id = '" . (int)$products_id . "'");
                    while ($variations = tep_db_fetch_array($products_related_query)) {
                       tep_db_query("INSERT INTO `products_related` (`products_related_products_id`, `products_related_table_id`, `products_related_product_altName`, `products_related_related_products_id`, `products_related_related_category_id`, `products_related_direction`, `products_related_sort_order`, `products_related_linked_id`)  VALUES ('" . (int)$dup_products_id . "', '" . tep_db_input($products_related['products_related_table_id']) . "', '" . tep_db_input($products_related['products_related_product_altName']) . "', '" . tep_db_input($products_related['products_related_related_products_id']) . "', '" . tep_db_input($products_related['products_related_related_category_id']) . "', '" . tep_db_input($products_related['products_related_direction']) . "', '" . tep_db_input($products_related['products_related_sort_order']) . "', '" . tep_db_input($products_related['products_related_sort_order']) . "')");

                    }
                    
                    // start indvship
                    $shipping_query = tep_db_query("select products_ship_methods_id, products_ship_key from products_shipping where products_id = '" . (int) $products_id . "'");
                    while ($shipping = tep_db_fetch_array($shipping_query)) {
                        tep_db_query("insert into products_shipping (products_id, products_ship_methods_id, products_ship_key) values ('" . (int) $dup_products_id . "', '" . tep_db_input($shipping['products_ship_methods_id']) . "', '" . tep_db_input($shipping['products_ship_key']) . "')");
                    }
                    // end indvship
                    $product_images_query = tep_db_query("select image, htmlcontent, sort_order from products_images where products_id = '" . (int) $products_id . "'");
                    while ($product_images = tep_db_fetch_array($product_images_query)) {
                        tep_db_query("insert into products_images (products_id, image, htmlcontent, sort_order) values ('" . (int) $dup_products_id . "', '" . tep_db_input($product_images['image']) . "', '" . tep_db_input($product_images['htmlcontent']) . "', '" . tep_db_input($product_images['sort_order']) . "')");
                    }
                    tep_db_query("insert into products_to_categories (products_id, categories_id) values ('" . (int) $dup_products_id . "', '" . (int) $categories_id . "')");
                    $products_id = $dup_products_id;
                }
                
                if (USE_CACHE == 'true') {
                    tep_reset_cache_block('categories');
                    tep_reset_cache_block('also_purchased');
                }
            }
            
            tep_redirect(tep_href_link('categories.php', 'cPath=' . $categories_id . '&pID=' . $products_id . '&db=' . $debug));
            break;
    }
}

// check if the catalog image directory exists
if (is_dir(DIR_FS_CATALOG_IMAGES)) {
    if (!tep_is_writable(DIR_FS_CATALOG_IMAGES))
        $messageStack->add(ERROR_CATALOG_IMAGE_DIRECTORY_NOT_WRITEABLE, 'error');
} else {
    $messageStack->add(ERROR_CATALOG_IMAGE_DIRECTORY_DOES_NOT_EXIST, 'error');
}

require('includes/template_top.php');


// ---- Mark
if ($action == 'editContent_category') {
    if (isset($_GET['cID']) && empty($_POST)) {
        $catID            = $_GET['cID'];
        $categories_query = tep_db_query("select * from categories_content cc where cc.categories_id = " . $catID . " limit 1");
        $categoryContent  = tep_db_fetch_array($categories_query);
        if ($categoryContent) {
            $formAction = 'update_category_content';
            $theContent = $categoryContent['categories_content'];
        } else {
            $formAction = 'new_category_content';
            $theContent = '';
        }
        
        
        
        
         ?>
       <table border="0" cellspacing="0" cellpadding="2">
      <tr>
        <td colspan="4"><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td class="pageHeading" colspan="4"><?php
        echo tep_get_category_name($catID, 1); ?> Category Content</td>
            <td class="pageHeading" align="right"><?php
        echo tep_draw_separator('pixel_trans.gif', HEADING_IMAGE_WIDTH, HEADING_IMAGE_HEIGHT); ?></td>
          </tr>
        </table></td>
      </tr>
      <tr>
        <td>
        <?php
        echo tep_draw_form($editContent_category, 'categories.php', '&cID=' . $_GET['cID'] . '&action=editContent_category_preview', 'post', 'enctype="multipart/form-data"'); ?> <textarea name='categoryContent'  wrap='soft' cols='100' rows='50'><?php
        echo $theContent; ?></textarea>      </td></tr>
            
        <tr>
            <td class="main" align="left">
            <?php
        echo tep_draw_hidden_field('cID', $catID) . tep_draw_hidden_field('formAction', $formAction) . tep_image_submit('button_preview.gif', IMAGE_PREVIEW); ?></td><td class="main" align="right">
            <?php
        echo '<a href="' . tep_href_link('categories.php', 'cPath=' . $cPath . (isset($_GET['pID']) ? '&pID=' . $_GET['pID'] : '')) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'; ?>
           <td class="main" align="right">
        </td>
          </tr>
      </table></td>
      </tr>
      <tr>
        <td><?php
        echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      </tr>

    </table></form><?php
    }
} else if ($action == 'editContent_category_preview') {
    //    echo 'cID: ' . $_GET['cID'];
    if (isset($_GET['cID'])) {
        
        $catID      = $_POST['cID'];
        $formAction = $_POST['action'];
        $theContent = $_POST['categoryContent'];
        //echo 'catID: ' . $catID . '<br />' . 'formAction: ' . $formAction . '<br />' . '$theContent: ' . $theContent;  ?>
       <table border="0" cellspacing="0" cellpadding="2">
      <tr>
        <td colspan="4"><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td class="pageHeading" colspan="4"><?php
				echo tep_get_category_name($catID, 1); ?> Category Content Preview</td>
            <td class="pageHeading" align="right"><?php
				echo tep_draw_separator('pixel_trans.gif', HEADING_IMAGE_WIDTH, HEADING_IMAGE_HEIGHT); ?></td>
          </tr>
        </table>
        </td>
      </tr>
      <tr>
        <td  >
            <table border="0" width="100%" cellspacing="25" cellpadding="25">
          <tr>
            <td class="main" style="border: 1px solid #000"><?php
        	echo $theContent; ?>
        	</td>
          </tr>
        </table>
        </td>
      </tr>
      <tr>
        <td>
        <?php
        echo tep_draw_form($formAction, 'categories.php', '&cID=' . $_GET['cID'] . '&action=' . $formAction, 'post', 'enctype="multipart/form-data"'); ?> <textarea name='categoryContent'  wrap='soft' cols='100' rows='50'><?php
        echo $theContent; ?></textarea></td></tr>
        <tr>
            <td class="main" align="left"><?php
        echo '<a href="' . tep_href_link('categories.php', 'cPath=' . $cPath . (isset($_GET['pID']) ? '&pID=' . $_GET['pID'] : '')) . '">' . tep_image_button('button_back.gif', IMAGE_BACK) . '</a>'; ?></td><td><?php
        echo tep_draw_hidden_field('cID', $catID) . tep_image_submit('button_update.gif', IMAGE_UPDATE); ?></td><td class="main" align="right">
            <?php
        echo '<a href="' . tep_href_link('categories.php', 'cPath=' . $cPath . (isset($_GET['pID']) ? '&pID=' . $_GET['pID'] : '')) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'; ?>
         </tr>
      </table></td>
      </tr>
      <tr>
        <td><?php
        echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      </tr>

    </table></form><?php
    }
    /// ------------ end mark
}

if ($action == 'new_product') {
    $parameters                             = array(
        'products_name' => '',
        'products_description' => '',
        'products_url' => '',
        'products_id' => '',
        'products_quantity' => '',
        'products_model' => '',
        'products_image' => '',
        'products_larger_images' => array(),
        'products_price' => '',
        'products_weight' => '',
        'products_date_added' => '',
        'products_last_modified' => '',
        'products_date_available' => '',
        'products_status' => '',
		'products_google_status' => '',
        'products_tax_class_id' => '',
        'products_video_url' => '',
        'products_sort_order' => '',
        'products_request_quote' => '',
        'manufacturers_id' => ''
    );
    $parameters['products_gtin']            = '';
    $parameters['products_seo_description'] = '';
    $parameters['products_seo_keywords']    = '';
    $parameters['products_seo_title']       = '';
    
    $pInfo = new objectInfo($parameters);
    
    if (isset($_GET['pID']) && empty($_POST)) {
        //$product_query = tep_db_query("select pd.products_name, pd.products_description, pd.products_url, p.products_id, p.products_quantity, p.products_model, p.products_image, p.products_price, p.products_weight, p.products_date_added, p.products_last_modified, date_format(p.products_date_available, '%Y-%m-%d') as products_date_available, p.products_status, p.products_tax_class_id, p.manufacturers_id, p.products_gtin from products p, products_description pd where p.products_id = '" . (int)$_GET['pID'] . "' and p.products_id = pd.products_id and pd.language_id = '" . (int)$languages_id . "'");
        $product_query = tep_db_query("select pd.products_name, pd.products_description, pd.products_url, p.products_id, p.products_quantity, p.products_model, p.products_image, p.products_price, p.products_weight, p.products_date_added, p.products_last_modified, date_format(p.products_date_available, '%Y-%m-%d') as products_date_available, p.products_status, p.products_google_status, p.products_sort_order, p.products_tax_class_id, p.manufacturers_id, p.products_gtin, p.products_request_quote, pd.products_video_url from products p, products_description pd where p.products_id = '" . (int) $_GET['pID'] . "' and p.products_id = pd.products_id and pd.language_id = '" . (int) $languages_id . "'");
        $product       = tep_db_fetch_array($product_query);
        
        $pInfo->objectInfo($product);
        // start indvship
        $products_shipping_query = tep_db_query("SELECT * FROM products_shipping WHERE products_id=" . (int) $_GET['pID']);
        while ($products_shipping = tep_db_fetch_array($products_shipping_query)) {
            $products_ship_key        = $products_shipping['products_ship_key'];
            $products_ship_methods_id = $products_shipping['products_ship_methods_id'];
            $products_ship_price      = $products_shipping['products_ship_price'];
            $products_ship_qty        = $products_shipping['products_ship_qty'];
            $products_ship_flags      = $products_shipping['products_ship_flags'];
        }
        $shipping = array(
            'products_ship_methods_id' => $products_ship_methods_id,
            'products_ship_key' => $products_ship_key,
            'products_ship_price' => $products_ship_price,
            'products_ship_qty' => $products_ship_qty,
            'products_ship_flags' => $products_ship_flags
        );
        $pInfo->objectInfo($shipping);
        // end indvship
        
        
        if (!isset($pInfo->products_status)){
            $pInfo->products_status = '1';
	}
	if (!isset($pInfo->products_google_status)){
            $pInfo->products_google_status = '1';
	}
        $sFlagsA = preg_split("/[:,]/", $products_ship_flags);
        switch ($sFlagsA[0]) {
            case '1':
                $products_blockFreeShip_yes = true;
                $products_blockFreeShip_no  = false;
                break;
            case '0':
            default:
                $products_blockFreeShip_yes = false;
                $products_blockFreeShip_no  = true;
        }
        switch ($sFlagsA[1]) {
            case '1':
                $products_digiProduct_yes = true;
                $products_digiProduct_no  = false;
                break;
            case '0':
            default:
                $products_digiProduct_yes = false;
                $products_digiProduct_no  = true;
        }
        switch ($product['products_request_quote']) {
            case '1':
                $products_request_quote_yes = true;
                $products_request_quote_no  = false;
                break;
            case '0':
            default:
                $products_request_quote_yes = false;
                $products_request_quote_no  = true;
        } // end indvship
        $product_images_query = tep_db_query("select id, image, htmlcontent, sort_order from products_images where products_id = '" . (int) $product['products_id'] . "' order by sort_order");
        while ($product_images = tep_db_fetch_array($product_images_query)) {
            $pInfo->products_larger_images[] = array(
                'id' => $product_images['id'],
                'image' => $product_images['image'],
                'htmlcontent' => $product_images['htmlcontent'],
                'sort_order' => $product_images['sort_order']
            );
        }
    }
    
    $manufacturers_array = array(
        array(
            'id' => '',
            'text' => TEXT_NONE
        )
    );
    $manufacturers_query = tep_db_query("select manufacturers_id, manufacturers_name from manufacturers order by manufacturers_name");
    while ($manufacturers = tep_db_fetch_array($manufacturers_query)) {
        $manufacturers_array[] = array(
            'id' => $manufacturers['manufacturers_id'],
            'text' => $manufacturers['manufacturers_name']
        );
    }
    
    $tax_class_array = array(
        array(
            'id' => '0',
            'text' => TEXT_NONE
        )
    );
    $tax_class_query = tep_db_query("select tax_class_id, tax_class_title from tax_class order by tax_class_title");
    while ($tax_class = tep_db_fetch_array($tax_class_query)) {
        $tax_class_array[] = array(
            'id' => $tax_class['tax_class_id'],
            'text' => $tax_class['tax_class_title']
        );
    }
    
    $languages = tep_get_languages();
    
    if (!isset($pInfo->products_status)){
        $pInfo->products_status = '1';
    }
    switch ($pInfo->products_status) {
        case '0':
            $in_status  = false;
            $out_status = true;
            break;
        case '1':
        default:
            $in_status  = true;
            $out_status = false;
    }
    
    if (!isset($pInfo->products_google_status)){
        $pInfo->products_google_status = '1';
	}
    switch ($pInfo->products_google_status) {
        case '0':
            $in_google_status  = false;
            $out_google_status = true;
            break;
        case '1':
        default:
            $in_google_status = true;
            $out_google_status = false;
    }
    
    $form_action = (isset($_GET['pID'])) ? 'update_product' : 'insert_product'; ?>
	

	
<script type="text/javascript"><!--
var tax_rates = new Array();
<?php
    for ($i = 0, $n = sizeof($tax_class_array); $i < $n; $i++) {
        if ($tax_class_array[$i]['id'] > 0) {
            echo 'tax_rates["' . $tax_class_array[$i]['id'] . '"] = ' . tep_get_tax_rate_value($tax_class_array[$i]['id']) . ';' . "\n";
        }
    } ?>

function doRound(x, places) {
  return Math.round(x * Math.pow(10, places)) / Math.pow(10, places);
}

function getTaxRate() {
  var selected_value = document.forms["new_product"].products_tax_class_id.selectedIndex;
  var parameterVal = document.forms["new_product"].products_tax_class_id[selected_value].value;

  if ( (parameterVal > 0) && (tax_rates[parameterVal] > 0) ) {
    return tax_rates[parameterVal];
  } else {
    return 0;
  }
}

function updateGross() {
  var taxRate = getTaxRate();
  var grossValue = document.forms["new_product"].products_price.value;

  if (taxRate > 0) {
    grossValue = grossValue * ((taxRate / 100) + 1);
  }

  document.forms["new_product"].products_price_gross.value = doRound(grossValue, 4);
}

function updateNet() {
  var taxRate = getTaxRate();
  var netValue = document.forms["new_product"].products_price_gross.value;

  if (taxRate > 0) {
    netValue = netValue / ((taxRate / 100) + 1);
  }

  document.forms["new_product"].products_price.value = doRound(netValue, 4);
}
//--></script>
    <?php echo tep_draw_form('new_product', 'categories.php', 'cPath=' . $cPath . (isset($_GET['pID']) ? '&pID=' . $_GET['pID'] : '') . '&action=' . $form_action, 'post', 'enctype="multipart/form-data" id="new_product_form"'); ?>
   
   
   <table border="0" width="90%" cellspacing="0" cellpadding="2">
      <tr>
        <td colspan="4"><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td class="pageHeading" colspan="4"><?php echo sprintf(TEXT_NEW_PRODUCT, tep_output_generated_category_path($current_category_id)); ?></td>
            <td class="pageHeading" align="right"><?php echo tep_draw_separator('pixel_trans.gif', HEADING_IMAGE_WIDTH, HEADING_IMAGE_HEIGHT); ?></td>
          </tr>
        </table></td>
      </tr>
      <tr>
        <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      </tr>

         <tr>
            <td class="main"></td>


	<?php
	if (@tep_not_null($pInfo->products_image)) {
		$pi_query = tep_db_query("select image, htmlcontent from products_images where products_id = '" . (int) $pInfo->products_id . "' order by sort_order");
	} ?>

<?php
    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) { ?>

          <tr bgcolor="#eeeeee">
            <td class="main">Seo Title</td>
            <td class="main" colspan=4><?php echo tep_draw_input_field('products_seo_title[' . $languages[$i]['id'] . ']', (empty($pInfo->products_id) ? '' : tep_get_products_seo_title($pInfo->products_id, $languages[$i]['id'])), 'style="width: 500px;"'); ?></td>
          </tr>
<?php
    } ?>
         <tr>
            <td class="main"><?php echo TEXT_PRODUCTS_MANUFACTURER; ?></td>
            <td class="main"><?php echo tep_draw_pull_down_menu('manufacturers_id', $manufacturers_array, $pInfo->manufacturers_id); ?></td>
        
            <td class="main"><?php echo TEXT_PRODUCTS_MODEL; ?></td>
            <td class="main"><?php echo tep_draw_input_field('products_model', $pInfo->products_model); ?></td>
          </tr>
		  <tr>
               <td class="main"><?php echo 'GTIN:'; ?><script type="text/javascript"><!--

//--></script></td>
	  <?php // for ($i=0, $n=sizeof($languages); $i<$n; $i++) { ?>
	  <td class="main"> <?php echo tep_draw_input_field('products_gtin', $pInfo->products_gtin) . ' (UPC, EAN, ISBN or MPN)'; ?></td>
		</tr><tr><td colspan="4" style="max-width:1024px"><hr></td></tr>
		  <tr><td class="main">
		<script type="text/javascript">
		  $( document ).ready(function() {
			if (<?php if ($products_digiProduct_yes) {echo 1;} else {echo 0;} ?>){
				$('.indvShip').fadeTo('slow',.25);
					}
					$('#products_digiProduct_yes').click(function() {
						$('.indvShip').fadeTo('slow',.25);
					});
					$('#products_digiProduct_no').click(function() {
						$('.indvShip').fadeTo('slow',1);
					});
				});
      </script>
      
                <?php echo 'Digital Delivery:'; ?></td>
            
                <td class="main"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;No:&nbsp;' . tep_draw_radio_field('products_ship_flags_digiProduct', '0', $products_digiProduct_no, null, 'id="products_digiProduct_no"') . '&nbsp;Yes:&nbsp;' . tep_draw_radio_field('products_ship_flags_digiProduct', '1', $products_digiProduct_yes, null, 'id="products_digiProduct_yes"'); ?></td>
    
    </tr>
      <?php // start indvship  ?> 
          <tr class="indvShip">
            <td class="main">Group</td>
            <td class="main" valign=top><?php echo tep_draw_input_field('products_ship_key', $pInfo->products_ship_key);
    if (@tep_not_null($pInfo->products_ship_key))
        echo 'notnull';
    else
        echo 'null'; ?></td><td class="main">Current Keywords:</td>
            <td rowspan='4' class="main"> <table cellspacing="0" cellpadding="2" width="100%" border="0">
            <?php
    $i      = 0;
    $select = tep_db_query("SELECT * FROM `products_shipping` GROUP BY `products_ship_key` ORDER BY `products_ship_key` DESC");
    while ($keywords = tep_db_fetch_array($select)) {
        $i++;
        if ($i == 1) {
            echo "<tr><td>";
        } else {
            echo "<td>&nbsp;</td><td>";
        }
        echo "<a href='javascript:null();' onclick=\"document.forms['new_product'].products_ship_key.value='" . $keywords['products_ship_key'] . "';document.forms['new_product'].products_ship_price.value='" . $keywords['products_ship_price'] . "';document.forms['new_product'].products_ship_qty.value='" . $keywords['products_ship_qty'] . "';\">" . $keywords['products_ship_key'] . "";
        if ($i == 1) {
            echo "</td>";
        } else {
            echo "</td></tr>";
            $i = 0;
        }
    }
     ?></table></td>
    </tr> <!-- end Zipcode --> <!-- Indvship -->
          
    <tr class="indvShip">
            <td class="main" style="width:210px"><?php echo 'Indv. Shipping Price:'; ?></td>
            <td class="main"><?php echo tep_draw_input_field('products_ship_price', $pInfo->products_ship_price);
    if (@tep_not_null($pInfo->products_ship_price))
        echo 'notnull';
    else
        echo 'null'; ?></td>
    </tr>
    <tr class="indvShip">
            <td class="main"><?php echo 'Qty to qualify for free shipping:'; ?></td>
            <td class="main"><?php echo tep_draw_input_field('products_ship_qty', $pInfo->products_ship_qty);
    if (@tep_not_null($pInfo->products_ship_qty))
        echo 'notnull';
    else
        echo 'null'; ?></td>
          </tr>
    <tr class="indvShip">
             <td class="main">
                <?php echo 'Exempt free shipping > £200?:'; ?></td>
            
                <td class="main"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;No:&nbsp;' . tep_draw_radio_field('products_ship_flags_blockFreeShip', '0', $products_blockFreeShip_no) . '&nbsp;Yes:&nbsp;' . tep_draw_radio_field('products_ship_flags_blockFreeShip', '1', $products_blockFreeShip_yes); ?></td>
                
        <!-- end Indvship -->
            
          
    </tr>
    <tr class=""><td colspan="4" style="max-width:930px"><hr></td></tr>
          <tr>
            <td class="main"><?php echo TEXT_PRODUCTS_PRICE_NET; ?></td> 
          <td class="main"><?php echo tep_draw_input_field('products_price', $pInfo->products_price); ?>   </td>
 <td class="main">
				<?php echo 'Show "Request Quote" Button:'; ?></td><td class="main">
                <?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;No:&nbsp;' . tep_draw_radio_field('products_request_quote', '0', $products_request_quote_no) . '&nbsp;Yes:&nbsp;' . tep_draw_radio_field('products_request_quote', '1', $products_request_quote_yes); ?></td>
            

          </tr> 
          <tr>
            <td class="main"><?php echo TEXT_PRODUCTS_PRICE_GROSS; ?></td> 
            <td class="main"><?php echo tep_draw_input_field('products_price_gross', $pInfo->products_price); ?></td>
            <td class="main"><?php echo TEXT_PRODUCTS_TAX_CLASS; ?></td>
            <td class="main">
            <?php echo tep_draw_pull_down_menu('products_tax_class_id', $tax_class_array, 1, 'id="products_tax_class_id"'); ?></td> 
            
        
          </tr><tr><td colspan="4" style="max-width:930px"><hr></td></tr>
<script type="text/javascript"><!--
$( document ).ready(function() {
    $("input[name=products_price]").keyup(updateGross);    
    $("input[name=products_price_gross").keyup(updateNet);    
  //  $("[name=products_tax_class_id]").onchange(updateGross);    
    updateGross();
});

//--></script>
<?php
    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) { ?>
         <tr>
            <td  class="main" valign="top" colspan="4"><?php
        echo TEXT_PRODUCTS_IMAGE; ?></td>
		  
		<tr>
            
<?php
    } ?>

        <td colspan="4" class="main">	
 <div id="piGal" style="float: left;max-width:100%;min-width:300px">			
		 <div style="max-width:200px">
			<?php echo '<br />' . (@tep_not_null($pInfo->products_image) ? '<a href="' . DIR_WS_CATALOG_IMAGES . $pInfo->products_image . '" target="_blank">
			<img class="img-responsive" src="' . DIR_WS_CATALOG_IMAGES . $pInfo->products_image . '"></a>' . 
			tep_draw_hidden_field('products_image_filename', $pInfo->products_image): '') . tep_draw_file_field('products_image'); ?>
		 </div>

              <ul id="piList">
<?php
    $pi_counter = 0;
    
    foreach ($pInfo->products_larger_images as $pi) {
        $pi_counter++;
        
        echo '                <li id="piId' . $pi_counter . '" class="ui-state-default ui-sortable-handle" style="float:left; max-width:120px">'  . 
		'<span class="ui-icon ui-icon-arrowthick-2-n-s" style="float: right;"></span>' . 
		'<a href="#" onclick="showPiDelConfirm(' . $pi_counter . ');return false;" class="ui-icon ui-icon-trash" style="float: right;"></a>' . 
		'<strong>Image '  . $pi['id'] . '</strong><br />' . 
		'<a href="' . DIR_WS_CATALOG_IMAGES . $pi['image'] . '" target="_blank"><img class="img-responsive" src="' . DIR_WS_CATALOG_IMAGES . $pi['image'] . '"></a>' .
		tep_draw_file_field('products_image_large_' . $pi['id'],false,'style="width: 90px;overflow: hidden;"');
		
		//'<br /><br />' . TEXT_PRODUCTS_LARGE_IMAGE_HTML_CONTENT . '<br />' .
		//tep_draw_textarea_field('products_image_htmlcontent_' . $pi['id'], 'soft', '70', '3', $pi['htmlcontent']) . '</li>';
    } ?>
             </ul>
			<br style="clear:both">
              <a href="#" onclick="addNewPiForm();return false;"><span class="ui-icon ui-icon-plus" style="float: left;"></span><?php echo TEXT_PRODUCTS_ADD_LARGE_IMAGE; ?></a>

<div id="piDelConfirm" title="<?php echo TEXT_PRODUCTS_LARGE_IMAGE_DELETE_TITLE; ?>">
  <p><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span><?php echo TEXT_PRODUCTS_LARGE_IMAGE_CONFIRM_DELETE; ?></p>
</div>

<style type="text/css">
#piList { list-style-type: none; margin: 0; padding: 0; }
#piList li { margin: 5px 0; padding: 2px; }
</style>

<script type="text/javascript">
$('#piList').sortable({
  containment: 'parent'
});

var piSize = <?php echo $pi_counter; ?>;

function addNewPiForm() {
  piSize++;

  $('#piList').append('<li id="piId' + piSize + '" class="ui-state-default"><span class="ui-icon ui-icon-arrowthick-2-n-s" style="float: right;"></span><a href="#" onclick="showPiDelConfirm(' + piSize + ');return false;" class="ui-icon ui-icon-trash" style="float: right;"></a><strong><?php echo TEXT_PRODUCTS_LARGE_IMAGE; ?></strong><br /><input type="file" name="products_image_large_new_' + piSize + '" /><br /><br /><?php echo TEXT_PRODUCTS_LARGE_IMAGE_HTML_CONTENT; ?><br /><textarea name="products_image_htmlcontent_new_' + piSize + '" wrap="soft" cols="70" rows="3"></textarea></li>');
}

var piDelConfirmId = 0;

$('#piDelConfirm').dialog({
  autoOpen: false,
  resizable: false,
  draggable: false,
  modal: true,
  buttons: {
    'Delete': function() {
      $('#piId' + piDelConfirmId).effect('blind').remove();
      $(this).dialog('close');
    },
    Cancel: function() {
      $(this).dialog('close');
    }
  }
});

function showPiDelConfirm(piId) {
  piDelConfirmId = piId;

  $('#piDelConfirm').dialog('open');
}
</script>
         </div>  
		 

		    </div>  	
	 <div id="piGal" style="float: left;max-width:100%;min-width:300px">YouTube Video
	 <?php   for ($i = 0, $n = sizeof($languages); $i < $n; $i++) { 
			 echo tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']) . '&nbsp;' . tep_draw_input_field('products_video_url[' . $languages[$i]['id'] . ']', (isset($products_video_url[$languages[$i]['id']]) ? stripslashes($products_video_url[$languages[$i]['id']]) : tep_get_products_video_url($pInfo->products_id, $languages[$i]['id'])), 'size="55" '); 
         
   	    } ?>
      <?php
            if (@tep_not_null($pInfo->products_video_url)) { ?>
           <div class="videoWrapper">
                <div class="video_iframe">
                    <!-- a transparent image is preferable -->
                    <img class="videoRatio" src="<?php
                echo DIR_WS_CATALOG_IMAGES . 'placeholders/16x9.png'; ?>"/>
                    <iframe src="https://<?php
                echo $pInfo->products_video_url; ?>" frameborder="0" allowfullscreen="1"></iframe>
                </div>
            </div>
            <?php
            } ?>
   </div></td>
		 
		 
          </tr>  <?php
    // end indvship 
    if (isset($pInfo->products_tax_class_id)) {
        $taxSelection = $pInfo->products_tax_class_id;
    } else {
        $taxSelection = 1;
    } 
    //EPP: Attributes Table
    ?>
        </tr>
        <tr>
            <td colspan="4"><?php 
		
			$attributes_sql = "select * from products_attributes pa, products_options po, products_options_values pov, products_description pd WHERE pa.products_id = pd.products_id and po.products_options_id = pa.options_id and pov.products_options_values_id = pa.options_values_id and pd.language_id = '" . (int)$languages_id . "' and pa.products_id = '" . $pInfo->products_id . "' order by pa.products_attributes_sort_order, po.products_options_name, pov.products_options_values_name";
			$attributes = "select pa.* from products_attributes pa left join products_description pd on pa.products_id = pd.products_id and pd.language_id = '" . (int)$languages_id . "' and pa.products_id = '" . $pInfo->products_id . "' order by pd.products_name";
			//echo $attributes_sql;
			?>
			</td>
          </tr>
		        <tr>
            <td colspan="4"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
		  <tr><td colspan="4">
		  <div  class="col-xs-10">
		  

		<div class=" panel panel-default">
			<div class="panel-header">
			<?php if (!empty($pInfo->products_id)){ ?> 
			<div class="panel-body">
				<strong>Attributes</strong></div>		 
		   </div>
		   
		  <table class="table table-striped table-draggable" id="attributesTable">	
			<thead>
			<tr>
				<td>Option</td><td>Value</td><td align="center">Price</td><td align="center">Prefix</td><td align="center">Default</td><td align="center">DependsOn</td><td align="center">Sort Order</td><td align="center" colspan="2">Actions</td>
				</tr>
			</thead>
			<tbody>	
					
			<?php
			$rows = 0;
			$attributes_query = tep_db_query($attributes_sql);
			while ($attributes_values = tep_db_fetch_array($attributes_query)) {
				if(strpos($attributes_values['dependson_options_values_id'],',') !== false){
					$valueIds = explode(',',$attributes_values['dependson_options_values_id']);
					$dependson_options_values_name = "";
					foreach ($valueIds as $value){
						if ($dependson_options_values_name != ""){							
							$dependson_options_values_name .=  ', ';
						}
						$dependson_options_values_name .= tep_values_name($value);
					}
					
				} else {
					$dependson_options_values_name = tep_values_name($attributes_values['dependson_options_values_id']);
				}
				$products_name_only = tep_get_products_name($attributes_values['products_id']);
				$options_name = $attributes_values['products_options_name'];
				$values_name = tep_values_name($attributes_values['options_values_id']);
				$dependson_options_name = tep_options_name($attributes_values['dependson_options_id']);
				
				//echo $attributes_values['dependson_options_id'] . ' > ' . $attributes_values['dependson_options_values_id'];
				
				if (@tep_not_null($attributes_values['dependson_options_id']) && @tep_not_null($attributes_values['dependson_options_values_id'])){
					$dependsOn_string = $dependson_options_name . ': ' . $dependson_options_values_name;
				} else {
					$dependsOn_string = "None";
				}			
				
				
				$rowId =  $attributes_values['products_options_id'] . '_' . $attributes_values['products_options_values_id'];
				?>
				<tr <?php echo 'data-attributesid="' . $attributes_values['products_attributes_id'] . '" data-rowNum="' . $rowId . '" class="" id="attributesTableRow_' . $rowId . '"'?>>
				 	<td <?php echo 'id="attributesTable_optionsName_' . $rowId . '" data-options_id="' . $attributes_values['products_options_id'] . '"';?> ><?php echo $options_name; ?></td>
					<td <?php echo 'id="attributesTable_optionsValueName_' . $rowId . '" data-values_id="' . $attributes_values['products_options_values_id'] . '"';?> ><?php echo $values_name; ?></td>
					<td <?php echo 'id="attributesTable_optionsValuePrice_' . $rowId . '"'?> align="center"><?php echo $attributes_values["options_values_price"]; ?></td>
					<td <?php echo 'id="attributesTable_optionsValuePricePrefix_' . $rowId . '"'?> align="center"><?php echo $attributes_values["price_prefix"]; ?></td>					
					<td <?php echo 'id="attributesTable_optionsValuePriceDefault_' . $rowId . '"'?> align="center"><?php echo $attributes_values["attribute_default"] ? 'Yes' : 'No'; ?></td>					
					<td <?php echo 'id="attributesTable_DependsOn_' . $rowId . '"' . ' data-dependson_options_id="' . $attributes_values['dependson_options_id'] . '" data-dependson_values_id="' . $attributes_values['dependson_options_values_id'] . '"'?> ><?php echo $dependsOn_string; ?></td>
					<td style="width:5%" class="" style="width:10px"><input class="indexSO attributesSOinput" type="text" name="" size="1" maxlength="4" value="<?php echo $attributes_values['products_attributes_sort_order'];?>"></td>
					<td class="attributesOptionsEdit listsOptionsEdit">e</td>
					<td class="attributesOptionsDelete listsOptionsDelete">x</td>
				</tr>
			 <?php 
				$rows++;
			 } ?> 
			 </tbody>
			 </table>
			 <div class="panel-footer"> 			
				<?php $options = tep_db_query("select * from products_options where language_id = '" . $languages_id . "' order by products_options_name");?>
					<div class="row">
						<div id="attributes_left_container" class="col-xs-6">
							<div id="attributes_options_container" class="form-inline form-group">
							<label>Attributes: </label><br />
								<select id="attributes_options_id" class="form-control"><option disabled="" selected="" value="">Options</option>
									<?php
										while ($options_values = tep_db_fetch_array($options)) {
										  echo '<option name="attributes_' . $options_values['products_options_name'] . '" value="' . $options_values['products_options_id'] . '">' . $options_values['products_options_name'] . '</option>';
										} 
									?>
								</select>		
								<div id="attributes_values_id_container" class="form-group">
									<select id="attributes_values_id" class="form-control">
										<option disabled selected value>Values</option>
									</select>	
								</div>
								<div id="attributes_values_id_btns_container" class="btn-group"></div>
							</div>
						
						<div id="attributes_misc_container" class="form-inline form-group">
						<div id="attributes_value_price_container" class="form-group">
							<input type="text" name="attributes_value_price" id="attributes_value_price" size="6" placeholder="Price" class="form-control">&nbsp;&nbsp;
						</div>
						
						<div id="attributes_prefix_container" class="btn-group ">
							<button id="attributes_prefix_plus_btn" class="btn btn-success attributes_prefix_btns" type="button">+</button>
							<button id="attributes_prefix_minu_btn" class="btn btn-default attributes_prefix_btns" type="button">-</button>	
							<button id="attributes_prefix_mult_btn" class="btn btn-default attributes_prefix_btns" type="button">*</button>							               
							
						</div>		<input type="hidden" name="attributes_price_prefix" id="attributes_price_prefix" value="+"> 
						<div class="checkbox"><label><input type="checkbox" name="attributes_attribute_default" id="attributes_attribute_default">&nbsp;Default</label></div>
						</div>
					</div>
					
					<div id="attributes_dependson_container" class="col-xs-5">						
						<label>Depends On: </label>
						<select id="attributes_dependson_options_id" class="form-control"><option disabled selected value>Options</option>
							<?php
								$options = tep_db_query("select * from products_options where language_id = '" . $languages_id . "' order by products_options_name");
								while ($options_values = tep_db_fetch_array($options)) {
								  echo '<option name="attributes_dependson_' . $options_values['products_options_name'] . '" value="' . $options_values['products_options_id'] . '">' . $options_values['products_options_name'] . '</option>';
								} 
							?>
						</select>
				
					
						<select multiple id="attributes_dependson_values_id" class="form-control">
							<option disabled selected value>Values</option>						
						</select>
						<div id="attributes_dependson_values_id_btns_container" class="btn-group"></div>
					</div>
					<div id="attributes_submit" class="col-xs-1 form-group">		
						<label>Save: </label>
						<button id="attributes_insert_btn" class="btn btn-primary pull-right" type="button">Insert</button>   
					</div>
					</div>
				 </div>
			   </div>
<?php	   
//EPP: -- Attributes Script 
?>	
<script>
	$(document).ready(function(e) {		
		$("#attributes_insert_btn").click(function(e) {
			var error = 0;
			var culprit = "";
			var options_id = $('#attributes_options_id').val();
			var values_id = $('#attributes_values_id').val();	
			
			
			var dependson_options_id = $('#attributes_dependson_options_id').val();
			var dependson_values_id = $('#attributes_dependson_values_id').val();
			var value_price = $('#attributes_value_price').val();
			var price_prefix = encodeURIComponent($('#attributes_price_prefix').val());
           		var attribute_default = $('#attributes_attribute_default').prop("checked") == true ? 1 : 0;
            		var method = "product_attributes_addToProduct";
			$("#attributesTable tbody tr").each(function(){
				var row = $(this).data("rownum");
				var elmstring = "#attributesTable_optionsValueName_" + row;
				if ($(elmstring).data("values_id") == values_id) {
					 method = "product_attributes_addToProduct";
					 return false;
				}
			});
			$.get("api.php?action=" + method + "&products_id=<?php echo $pInfo->products_id ?>&options_id=" + options_id + "&values_id=" + values_id + "&value_price=" + value_price + "&price_prefix=" + price_prefix + "&attribute_default=" + attribute_default + "&dependson_options_id=" + dependson_options_id + "&dependson_values_id=" + dependson_values_id, function(data, status){
				if (status == "success") {
					if (data["Complete"] > 0) {										
						attributesAddRow(data);								
						e.stopPropagation();					
					}
				}
			});
		});
		
		function attributesAddRow(data) {	
			attribute_id = data["attributes"][0]["attribute_id"];
			options_name = data["attributes"][0]["options_name"];
			options_id = data["attributes"][0]["options_id"];
			values_id = data["attributes"][0]["values_id"];
			values_name = data["attributes"][0]["values_name"];
			value_price = data["attributes"][0]["value_price"];
			price_prefix = data["attributes"][0]["price_prefix"];
			attribute_default = data["attributes"][0]["attribute_default"] == "1" ? 'Yes' : 'No';
			dependson_options_id = data["attributes"][0]["dependson_options_id"];
			dependson_options_name = data["attributes"][0]["dependson_options_name"];
			dependson_values_id = data["attributes"][0]["dependson_values_id"];
			dependson_values_name = data["attributes"][0]["dependson_values_name"];
			
			var rowNum = options_id + '_' + values_id;
			
			var elements = $('<tr style="display:none" data-attributesid="' + attribute_id + '" data-rownum="' + rowNum + '" class="" id="attributesTableRow_' + rowNum + '">'
				+ '<td id="attributesTable_optionsName_' + rowNum + '" data-options_id="' + options_id + '">' + options_name + '</td>'
				+ '<td id="attributesTable_optionsValueName_' + rowNum + '" data-values_id="' + values_id + '">' + values_name + '</td>'
				+ '<td id="attributesTable_optionsValuePrice_' + rowNum + '" align="center">' + value_price + '</td>'
				+ '<td id="attributesTable_optionsValuePricePrefix_' + rowNum + '" align="center">' + price_prefix + '</td>'				
				+ '<td id="attributesTable_optionsValuePriceDefault_' + rowNum + '" align="center">' + attribute_default + '</td>'
				+ '<td id="attributesTable_DependsOn_' + rowNum + '" data-dependson_options_id="' + dependson_options_id + '" data-dependson_values_id="' + dependson_values_id + '">' + dependson_options_name + ': ' + dependson_values_name + '</td>'
				+ '<td style="width:5%" class=""><input class="indexSO attributesSOinput" type="text" name="" size="1" maxlength="4" value=""></td>'
				+ '<td class="attributesOptionsEdit listsOptionsEdit">e</td>'
				+ '<td class="attributesOptionsDelete listsOptionsDelete">x</td>'
			+ '</tr>');
			
			var row = $('#attributesTableRow_' + rowNum);					
			if (row.length) {
				row.replaceWith(elements);
				elements.fadeIn("slow");
			} else {
				elements.appendTo("#attributesTable").fadeIn("slow");
			}			
		}
		
		$("#attributesTable").on("click", ".attributesOptionsDelete", function(e) {
			var that = $(this);
			var method = "product_attributes_removeAttribute";
			$.get("api.php?action=" + method + "&products_id=<?php echo $pInfo->products_id ?>&products_attributes_id=" + that.parent().data("attributesid"), function(data, status){
				if (status == "success") {
					e.stopPropagation();
					that.parent().fadeOut("slow",function(){
						this.remove();
					});
				}
			});	
		});	
		
		
		$("#attributesTable").on("click",".attributesOptionsEdit",function(e) {
			var row = $(this).closest('tr').data('rownum');
			var options_id = $('#attributesTable_optionsName_' + row).data('options_id');
			var values_id = $('#attributesTable_optionsValueName_' + row).data('values_id');			
			var dependson_options_id = $('#attributesTable_DependsOn_' + row).data('dependson_options_id');
			var dependson_values_id = $('#attributesTable_DependsOn_' + row).data('dependson_values_id');
			var value_price = $('#attributesTable_optionsValuePrice_' + row).text().trim();
			var price_prefix = $('#attributesTable_optionsValuePricePrefix_' + row).text().trim();
            		var attribute_default = $('#attributesTable_optionsValuePriceDefault_' + row).text().trim() == "Yes" ? 1 : 0;
			
			$('#attributes_options_id').val(options_id);
			$('#attributes_options_id').trigger("change", function() {
				$('#attributes_values_id').val(values_id);
			});
			$('#attributes_value_price').val(value_price);
			$('#attributes_price_prefix').val(price_prefix);			
			$('#attributes_prefix_plus_btn').trigger('change');
			$('#attributes_attribute_default').prop("checked", attribute_default);
			if ((dependson_options_id != null) && (dependson_values_id != null)){
				$('#attributes_dependson_options_id').val(dependson_options_id);
				$('#attributes_dependson_options_id').trigger("change", function() {
					$('#attributes_dependson_values_id').val(dependson_values_id);
					$('#attributes_dependson_values_id').trigger("change");
				});
			} else {
				$('#attributes_dependson_options_id').val(0);
				$('#attributes_dependson_values_id').html("<option disabled selected value>Values</option>");
			}			
		});	
		$("#attributes_values_id").change(function(e, callback) {
			var valuesSelectBtnsCont = $('#attributes_values_id_btns_container');
			var valuesSelectCont = $('#attributes_values_id_container');
			var valuesSelect = $('#attributes_values_id');
			var valuesSelectBtns = $(".valuesSelectBtns");
			var currentVal = valuesSelect.val();
			valuesSelectBtns.removeClass('active btn-success').addClass('btn-default');
			$("#valuesSelectBtn_" + currentVal).addClass('active btn-success');
		});
		
		$("#attributes_options_id").change(function(e, callback) {			
			var valuesSelectBtnsCont = $('#attributes_values_id_btns_container');
			var valuesSelectCont = $('#attributes_values_id_container');
			var valuesSelect = $('#attributes_values_id');
			var method = "product_attributes_getValueList";
			$.get("api.php?action=" + method + "&options_id=" + $(this).val(), function(data, status){				
				if (status == "success") {
					e.stopPropagation();				
				//	attributes_values_id_btns_container = $("#attributes_values_id_btns_container");
					valuesSelectBtnsCont.empty();
					valuesSelect.empty().append($('<option disabled selected value>Values</option>'));
					var count = 0;
					var selected = "selected";
					for (x in data["attributes"]) {
						valuesSelect.append($('<option id="valuesSelectItem_' +  count + '" value="' + data["attributes"][x]["values_id"] + '"' + selected  + '>' + data["attributes"][x]["values_name"] + '</option>'));
						valuesSelectBtnsCont.append($('<button type="button" id="valuesSelectBtn_' + data["attributes"][x]["values_id"] + '"' + ' data-value="' + data["attributes"][x]["values_id"] + '" class="btn btn-default valuesSelectBtns">' + data["attributes"][x]["values_name"] + '</button>'));
						var selected = "";												
					}
					$('#attributes_values_id').trigger("change");
					valuesSelectBtns = $(".valuesSelectBtns");
					if (valuesSelectBtnsCont.width() > 450){
						valuesSelectBtnsCont.hide();
						valuesSelectCont.show();
					} else {
						valuesSelectBtnsCont.show();
						valuesSelectCont.hide();
						valuesSelectBtns.click(function(e) {
							valuesSelectBtns.removeClass('active btn-success').addClass('btn-default');
							$(this).removeClass('btn-default').addClass('active btn-success');
							valuesSelect.val($(this).data("value"));
						});
					}
				}
				valuesSelect.prop('disabled', false);			
				if (typeof callback === "function") callback();
			
			});			
		});        
		$("#attributes_dependson_options_id").change(function(e, callback) {
			//var dependson_valuesSelectBtnsCont = $('#attributes_dependson_values_id_btns_container');
			var dependson_valuesSelectCont = $('#attributes_dependson_values_id_container');
			var dependson_valuesSelect = $('#attributes_dependson_values_id');
			method = "product_attributes_addToProduct";
			$.get("api.php?action=" + method + "&options_id=" + $(this).val(), function(data, status){
				if (status == "success") {
					e.stopPropagation();				
				//	attributes_dependson_values_id_btns_container = $("#attributes_dependson_values_id_btns_container");
					//dependson_valuesSelectBtnsCont.empty();				
					dependson_valuesSelect.empty().append($('<option disabled selected value>Values</option>'));
					for (x in data["attributes"]) {
						dependson_valuesSelect.append($('<option value="' + data["attributes"][x]["values_id"] + '">' + data["attributes"][x]["values_name"] + '</option>'));
						//dependson_valuesSelectBtnsCont.append($('<button type="button" id="dependson_valuesSelectBtn_' + data["attributes"][x]["values_id"] + '" data-value="' + data["attributes"][x]["values_id"] + '" class="btn btn-default dependson_valuesSelectBtns">' + data["attributes"][x]["values_name"] + '</button>'));
					}					
				}
				dependson_valuesSelect.prop('disabled', false);
				if (typeof callback === "function") callback();
			});
		});
		$("#attributes_dependson_values_id").change(function(e, callback) {
			var valuesSelectBtnsCont = $('#attributes_dependson_values_id_btns_container');
			var valuesSelectCont = $('#attributes_dependson_values_id_container');
			var valuesSelect = $('#attributes_dependson_values_id');
			var currentVal = valuesSelect.val();	
			var valuesSelectBtns = $(".dependson_valuesSelectBtns");
			valuesSelectBtns.removeClass('active btn-success').addClass('btn-default');
			$("#dependson_valuesSelectBtn_" + currentVal).addClass('active btn-success');
			valuesSelect.prop('disabled', false);
			if (typeof callback === "function") callback();
		});
	
		$(".attributes_prefix_btns").click(function(e) {			
			$("#attributes_price_prefix").val($(this).text()).trigger('change');
				
		});	
		
		$('#attributes_price_prefix').on("change", function(e, callback) {
			$(".attributes_prefix_btns").removeClass("active btn-success").addClass('btn-default');
			var theText = $(this).val();
			if (theText == "+"){
				$("#attributes_prefix_plus_btn").addClass("active btn-success").removeClass('btn-default');
			} else if (theText == "-"){
				$("#attributes_prefix_minu_btn").addClass("active btn-success").removeClass('btn-default');
			}	
		});
		
	});
</script>

<?php } else { ?>
			<div class="panel-body">
				<strong>Save first to generate product id</strong>
				</div>
				<?php } ?> 
		   </div>
		</div>
			
<?php //EPP: Variations Table
 ?>
	<tr>
            <td colspan="4"><?php 
		
	$variations_sql = "select * from products_variations pv WHERE pv.products_id = '" . $pInfo->products_id . "' order by pv.sort_order";
	//$variations = "select pa.* from products_attributes_variations pa left join products_description pd on pa.products_id = pd.products_id and pd.language_id = '" . (int)$languages_id . "' and pa.products_id = '" . $pInfo->products_id . "' order by pd.products_name";
	//echo $variations_sql;
			?>
		</td>
        </tr>
		<tr>
	            <td colspan="4"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
	        </tr>
		<tr>
		  <td colspan="4">
		  <div  class="col-xs-10">
		<div class=" panel panel-default">
			<div class="panel-header">
			<?php if (!empty($pInfo->products_id)){ ?> 
			<div class="panel-body">
				<strong>Variations</strong></div></div>
		   
		  <table class="table table-striped table-draggable" id="variationsTable">	
			<thead>
			<tr>
				<td>Model</td><td>GTIN</td><td>Image ID</td><td>Price</td><td align="center">Options</td><td align="center">Sort Order</td><td align="center" colspan="2">Actions</td>
				</tr>
			</thead>
			<tbody>	
					
					
			<?php
			$rows = 0;
			$variations_query = tep_db_query($variations_sql);
			$attributesNameString = "";	
			while ($variations_values = tep_db_fetch_array($variations_query)) {
				$attributes = explode('{', substr($variations_values['attributes'], strpos($variations_values['attributes'], '{')+1));
			  	$attributesNameString = "";
			  	for ($i=0, $n=sizeof($attributes); $i<$n; $i++) {			           
			            $pair = explode('}', $attributes[$i]);
				    $attributesNameString .= '<span style="font-weight:bold">' . tep_options_name($pair[0]) . ':</span> ';
				    $attributesNameString .= tep_values_name($pair[1]) . ' ';				     
			           
			        }        
								
				$rowId =  $variations_values['model'] . '_' . $variations_values['products_variations_id'];
				?>
				<tr <?php echo 'data-variationsid="' . $variations_values['products_variations_id'] . '" data-rowNum="' . $rowId . '" class="" id="variationsTableRow_' . $rowId . '"'?>>
				 	<td <?php echo 'id="variationsTable_Model_' . $rowId . '" data-model="' . $variations_values['model'] . '"';?> ><?php echo $variations_values['model'] ?></td>
				 	<td <?php echo 'id="variationsTable_gtin_' . $rowId . '" data-gtin="' . $variations_values['gtin'] . '"';?> ><?php echo $variations_values['gtin'] ?></td>
				 	<td <?php echo 'id="variationsTable_image_id_' . $rowId . '"'?> align="center"><?php echo $variations_values['image_id']; ?></td>
					<td <?php echo 'id="variationsTable_Price_' . $rowId . '"'?> align="center"><?php echo $variations_values['price']; ?></td>
					<td <?php echo 'id="variationsTable_attributes_' . $rowId . '" data-attributes="' . $variations_values['attributes'] . '"';?> style="font-size:9px" ><?php echo $attributesNameString; ?></td>
							
					<td style="width:5%" class="" style="width:10px"><input class="indexSO variationsSOinput" type="text" name="" size="1" maxlength="4" value="<?php echo $variations_values['sort_order'];?>"></td>
					<td class="variationsOptionsEdit listsOptionsEdit">e</td>
					<td class="variationsOptionsDelete listsOptionsDelete">x</td>
				</tr>
				<?php 
				$rows++;
			} 	?> 
			</tbody>
			</table>
			<div class="panel-footer"> 
			<div id="variations_options_container" class="">
				<div class="row">
			<?php 
			$options_output = null;
			$input_output = '<div class="col-xs-12 form-inline"> 
			<div id="variations_model_container" class="form-group">
				<label class="control-label">&nbsp;&nbsp;Model:&nbsp;</label><input type="text" name="variations_model" id="variations_model" size="16" placeholder="model" class="form-control">
			</div>';
			$input_output .= '
			<div id="variations_gtin_container" class="form-group">
				<label class="control-label">&nbsp;&nbsp;GTIN&nbsp;</label><input type="text" name="variations_gtin" id="variations_gtin" size="18" placeholder="GTIN" class="form-control">
			</div>';
			$input_output .= '
			<div id="variations_image_id_container" class="form-group">
				<label class="control-label">&nbsp;&nbsp;Image Id:&nbsp;</label><input type="text" name="variations_image_id" id="variations_image_id" size="6" placeholder="Image Id" class="form-control">
			</div>';
			$input_output .= '<div id="variations_price_container" class="form-group">
				<label class="control-label">&nbsp;&nbsp;Price:&nbsp;</label><input type="text" name="variations_price" id="variations_price" size="6" placeholder="Price" class="form-control">
			</div></div><hr ><div class="col-xs-10 form-inline">';
				
		$products_options_name_query = tep_db_query("select distinct popt.products_options_id, popt.products_options_name from products_options popt, products_attributes patrib where patrib.products_id='" . $pInfo->products_id . "' and patrib.options_id = popt.products_options_id order by popt.products_options_name");
		if (tep_db_num_rows($products_options_name_query)) {
			while ($products_options_name = tep_db_fetch_array($products_options_name_query)) {
				$products_options_array = array();

				$fr_input = $fr_required = $fr_feedback = null;
				
				$products_options_query = tep_db_query("select pov.products_options_values_id, pov.products_options_values_name, pa.options_values_price, pa.price_prefix, pa.attribute_default, pa.dependson_options_id, pa.dependson_options_values_id from products_attributes pa, products_options_values pov where pa.products_id = '" . $pInfo->products_id . "' and pa.options_id = '" . (int)$products_options_name['products_options_id'] . "' and pa.options_values_id = pov.products_options_values_id order by pa.products_attributes_sort_order");
				

				if (is_string($_GET['products_id']) && isset($cart->contents[$_GET['products_id']]['attributes'][$products_options_name['products_options_id']])) {
					$selected_attribute = $cart->contents[$_GET['products_id']]['attributes'][$products_options_name['products_options_id']];
				} else {
					$selected_attribute = false;
				}
				$select_output_select = '<div class="form-group attribute_group form-inline ' . $fr_feedback . '">
					<label for="input_' . $products_options_name['products_options_id'] . '" class="control-label"> ' . $products_options_name['products_options_name'] . ': </label>
					<select name="id[' . $products_options_name['products_options_id'] . ']" data-optionid="' . $products_options_name['products_options_id'] . '" data-productid="' . $_GET['products_id'] . '" data-optionsid="' . $products_options_name['products_options_id'] . '" id="variations_select_' . $products_options_name['products_options_id'] . '" class="form-control variationsAttributeSelect" >';	
				$buttons_output = '<div id="attributes_values_id_btns_container" class="attributes_values_id_btns_container btn-group" data-optionid="' . $products_options_name['products_options_id'] . '" >';	
				
				$option_selected = false;
				$select_output_options = "";
				while ($products_options = tep_db_fetch_array($products_options_query)) {
					//$select_output .= '<!-- Does ' .  '/\}' . (int)$products_options_name['products_options_id'] . ':' . $products_options['products_options_values_id'] . '[^0-9]?/'  . ' match ' . $_GET['products_id'] . ':';
					$selected_option = false;
					if (strpos($_GET['products_id'],'{')){
						if (preg_match('/\{' . (int)$products_options_name['products_options_id'] . '}' . $products_options['products_options_values_id'] . '([^0-9]|$)/',$_GET['products_id'])){
							$selected_option = true;
						}
					} else if ($products_options['attribute_default']) {
						$selected_option = true;
					}
					if ($selected_option){
						//$select_output .= ' Yes -->';
						$selected_option = 'selected';
						$selected_button = 'active btn-success';
					} else {
						//$select_output .= ' No -->';
						$selected_option = '';
						$selected_button = '';
					}
					$optionsPrice = $currencies->display_price($products_options['options_values_price'], tep_get_tax_rate($product_info['products_tax_class_id']));
					$select_output_options .= '<option value="'. $products_options['products_options_values_id'] . '" data-productId="' . $_GET['products_id'] . '" data-priceIncrease="'. $products_options['options_values_price'] . '" '. $selected_option . ' data-dependson_optionsid="' . $products_options['dependson_options_id'] . '" data-dependsOn_valuesid="' . $products_options['dependson_options_values_id'] . '">' . $products_options['products_options_values_name'];
					if ($products_options['options_values_price'] != '0') {
						$select_output_options .= ' (' . $products_options['price_prefix'] . $optionsPrice .') ';
					}					
					$select_output_options .= '</option>';
				
					//$buttons_output .= '<button type="button" data-optionid="' . $products_options['products_options_id'] . '" data-valueid="'. $products_options['products_options_values_id'] . '" data-productId="' . $_GET['products_id'] . '" data-priceIncrease="'. $products_options['options_values_price'] . '" class="btn btn-default valuesSelectBtns ' . $selected_button . '" data-buttonSet="" data-dependson_optionsid="' . $products_options['dependson_options_id'] . '" data-dependsOn_valuesid="' . $products_options['dependson_options_values_id'] . '">' . $products_options['products_options_values_name'] . '</button>';
				}
				
				if(!$option_selected){
					$select_output = $select_output_select . '<option disabled selected value>None</option>' . $select_output_options;
				}
					$select_output .=   '</select></div>';
				/*	$buttons_output .= "
					</div>";
				$buttons_output .= "";*/
				
				$options_output .= $select_output;
			}
			echo $input_output  . $options_output;			
		}
	?>
</div>
<div class="col-xs-2">
	<div id="variations_submit" class="form-group">	
			<button id="variations_insert_btn" class="btn btn-primary pull-right" type="button">Insert</button>   
		</div>
	</div>
</div>

	
	
<?php //EPP: -- Variations Script	   
?>	

	
<script>
	$(document).ready(function(e) {		
		$("#variations_insert_btn").click(function(e) {
			var error = 0;
			var culprit = "";
			var model = $('#variations_model').val();			
			var gtin =  $('#variations_gtin').val();
			var image_id =  $('#variations_image_id').val();
			var price = $('#variations_price').val();			
            		var method = "product_variations_addToProduct";
            		attributeString = "";
            		var attributeSelects = $(".variationsAttributeSelect").each(function(){
	            		el = $(this);
            			attributeString += "{" + el.data("optionid") + "}" + el.find(":selected").val()
            		});
			$("#variationsTable tbody tr").each(function(){
				var row = $(this).data("rownum");
				var elmstring = "#variationsTable_optionsValueName_" + row;
				if ($(elmstring).data("model") == model) {
					 method = "product_variations_addToProduct";
					 return false;
				}
			});
			$.get("api.php?action="
			+ method + "&"
			+ "products_id=<?php echo $pInfo->products_id ?>&"			
			+ "model=" + model + "&"
			+ "gtin=" + gtin + "&"
			+ "image_id=" + image_id + "&"
			+ "price=" + price + "&"
			+ "attributes=" + attributeString, function(data, status){
				if (status == "success") {
					if (data["Complete"] > 0) {										
						variationsAddRow(data);								
						e.stopPropagation();					
					}
				}
			});
		});
		
		function variationsAddRow(data) {	
			variation_id = data["variations"][0]["products_variations_id"];
			attributes_text	= data["variations"][0]["attributes_text"];
			attributes = data["variations"][0]["attributes"];
			model = data["variations"][0]["model"];
			gtin = data["variations"][0]["gtin"];
			image_id = data["variations"][0]["image_id"];
			price = data["variations"][0]["price"];
			var rowNum = model + '_' + variation_id;
			
			var elements = $('<tr style="display:none" data-variationsId="' + variation_id + '" data-rownum="' + rowNum + '" class="" id="variationsTableRow_' + rowNum + '">'
				+ '<td id="variationsTable_model_' + rowNum + '">' + model + '</td>'
				+ '<td id="variationsTable_gtin_' + rowNum + '">' + gtin + '</td>'
				+ '<td id="image_id_' + rowNum + '">' + image_id + '</td>'
				+ '<td id="variationsTable_price_' + rowNum + '" align="center">' + price + '</td>'
				+ '<td id="variationsTable_attributes' + rowNum + '" data-attributes="' + attributes + '" valign="center">' + attributes_text + '</td>'
				+ '<td style="width:5%" class=""><input class="indexSO variationsSOinput" type="text" name="" size="1" maxlength="4" value=""></td>'
				+ '<td class="variationsOptionsEdit listsOptionsEdit">e</td>'
				+ '<td class="variationsOptionsDelete listsOptionsDelete">x</td>'
			+ '</tr>');
			
			var row = $('#variationsTableRow_' + rowNum);					
			if (row.length) {
				row.replaceWith(elements);
				elements.fadeIn("slow");
			} else {
				elements.appendTo("#variationsTable").fadeIn("slow");
			}			
		}
		
		$("#variationsTable").on("click", ".variationsOptionsDelete", function(e) {
			var that = $(this);
			var method = "product_variations_removeVariation";
			$.get("api.php?action=" + method + "&products_id=<?php echo $pInfo->products_id ?>&products_variations_id=" + that.parent().data("variationsid"), function(data, status){
				if (status == "success") {
					e.stopPropagation();
					that.parent().fadeOut("slow",function(){
						this.remove();
					});
				}
			});	
		});	
		
		
		$("#variationsTable").on("click",".variationsOptionsEdit",function(e) {
			var row = $(this).closest('tr').data('rownum');			
			
			
			var model = $('#variationsTable_Model_' + row).data('model');			
			var gtin = $('#variationsTable_gtin_' + row).data('gtin');
			
			var value_price = $('#variationsTable_Price_' + row).text().trim();
			
			$("#variations_model").val(model);
			$("#variations_gtin").val(gtin);
			$("#variations_price").val(value_price);
			
            		var attributes = $('#variationsTable_attributes_' + row).data("attributes");
			attributes = attributes.substring(1).split('{');            		
            		attributesNameString = "";
			
			for (let i = 0, n = attributes.length; i < n; i++) {
			    let pair = attributes[i].split('}');
			    $("#variations_select_" + pair[0]).val(pair[1]);
			}				
			
		});
	});
	
	
	
</script>

<?php } else { ?>
			<div class="panel-body">
				<strong>Save first to generate product id</strong>
				</div>
				<?php } ?> 
		   </div>
			</div>		
			
			
			
			
			
			
			
			
			
			
			
						
			
			<tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          
<tr>
            <td colspan="4"><?php 
		
			$attribute_combinations_query = "select pa.* from products_attribute_combinations pa, products_description pd WHERE pa.products_id = pd.products_id and pd.language_id = '" . (int)$languages_id . "' and pa.products_id = '" . $pInfo->products_id . "' order by pd.products_name";
			//$attribute_combinations = "select pa.* from products_attribute_combinations pa left join products_description pd on pa.products_id = pd.products_id and pd.language_id = '" . (int)$languages_id . "' and pa.products_id = '" . $pInfo->products_id . "' order by pd.products_name";
			echo $attribute_combinations;
			?>
			</td>
          </tr>
		        <tr>
            <td colspan="4"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
		  
          
<?php



    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) { ?>
         <tr>
            <td class="main" valign="top"><?php
        echo tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']);
        
        if ($i == 0)
            echo TEXT_PRODUCTS_DESCRIPTION; ?></td></tr><tr>
            <td colspan=4><table border="0" cellspacing="0" cellpadding="0" style="width:1000px">
              <tr>
                <td class="main" valign="top">
      </td>
                <td class="main">
        
  <?php
        echo tep_draw_textarea_field_ckeditor('products_description[' . $languages[$i]['id'] . ']', 'soft', '117', '40', (empty($pInfo->products_id) ? '' : stripslashes(tep_get_products_description($pInfo->products_id, $languages[$i]['id'])))); ?></td>
              </tr>
            </table></td>
          </tr>
<?php
    } ?>
         <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <tr>
            <td class="main"><?php echo TEXT_PRODUCTS_QUANTITY; ?></td>
            <td class="main"><?php echo tep_draw_input_field('products_quantity', $pInfo->products_quantity); ?></td>
          </tr>
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>

          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
        
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <?php
    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) { ?>
         <tr bgcolor="#eeeeee">
            <td class="main" valign="top"><?php
        if ($i == 0)
            echo TEXT_PRODUCTS_SEO_DESCRIPTION; ?></td>
            <td colspan=3><table border="0" cellspacing="0" cellpadding="0">
              <tr>
                <td class="main" valign="top"><?php
        echo tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']); ?>&nbsp;</td>
                <td class="main"><?php
        echo tep_draw_textarea_field('products_seo_description[' . $languages[$i]['id'] . ']', 'soft', '70', '15', (empty($pInfo->products_id) ? '' : tep_get_products_seo_description($pInfo->products_id, $languages[$i]['id']))); ?></td>
              </tr>
            </table></td>
          </tr><tr>
            <td class="main"><?php
        echo 'More Information:'; ?></td>
            <td class="main" colspan=3><?php
        echo tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']) . '&nbsp;' . tep_draw_input_field('products_url[' . $languages[$i]['id'] . ']', tep_get_products_url($pInfo->products_id, $languages[$i]['id']), 'size="100" '); ?></td>
          </tr>
<?php
    } ?>
 

          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>

          <?php
    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) { ?>
         <tr bgcolor="#eeeeee">
            <td class="main" valign="top"><?php
        if ($i == 0)
            echo TEXT_PRODUCTS_SEO_KEYWORDS; ?></td>
            <td colspan=3><table border="0" cellspacing="0" cellpadding="0">
              <tr>
                <td class="main" valign="top"><?php
        echo tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']); ?>&nbsp;</td>
                <td class="main" valign="top"><?php
        echo tep_draw_input_field('products_seo_keywords[' . $languages[$i]['id'] . ']', tep_get_products_seo_keywords($pInfo->products_id, $languages[$i]['id']), 'placeholder="' . PLACEHOLDER_COMMA_SEPARATION . '" style="width: 300px;"'); ?></td>
              </tr>
            </table></td>
          </tr>
	<?php
	}
	?>
             <tr>
            <td class="main"><?php echo TEXT_PRODUCTS_WEIGHT; ?></td>
            <td class="main"><?php echo tep_draw_input_field('products_weight', $pInfo->products_weight); ?></td>
              </tr>
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <tr>
            <td class="main"><?php echo TEXT_EDIT_SORT_ORDER; ?></td>
            <td class="main"><?php echo tep_draw_input_field('products_sort_order', $pInfo->products_sort_order, 'size="2"'); ?></td>
          </tr>
		  
		       <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
		  	  
             <tr>
       <?php //EPP: Related Script	 
       ?>   
            <td class="main"><?php echo 'Related Products:'; ?></td><td>
	
		  
		<style>
		#relatedProductsModuleContainer {
			border: dotted 2px lightgrey;
			background-color: darkgrey;
		}
		.listsOptionsDelete {
			color:red;
			text-align:center;
			font-weight:bold;
			font-family:OCR A Std, monospace;
			font-size:1.5em;
			cursor: pointer;
		}
		.listsOptionsEdit{
			color:Green;
			text-align:center;
			font-weight:bold;
			font-family:OCR A Std, monospace;
			font-size:1.5em;
			cursor: pointer;
		}
		.relatedProductsRow {
			display:none;
		}
		.listsSOtd {			
			text-align: center;
		}
		#attributes_options_id_cont {
			visibility: hidden;
		}
		</style>
		
		<script>
		
	$(document).ready(function(){
		var apiAction = null;
		var fixHelperModified = function(e, tr) {
			var $originals = tr.children();
			var $helper = tr.clone();
			$helper.children().each(function(index) {
				$(this).width($originals.eq(index).width())
			});
			return $helper;
		},
		updateIndex = function(e, ui) {
			var updatedData = {};
			// Make an HTTP GET request with the JSON data
			
			if ($(this).find('.attributesSOinput').length > 0){
				apiAction = 'product_attributes_updateSortOrder';
				$('.indexSO.attributesSOinput', ui.item.parent()).each(function (i) {	
					$(this).val(i).trigger("change");			
					// Get the value of the input field
					var updatedValue = $(this).val();
					// Get the data-attributesid of the parent row
					var attributesId = $(this).closest('tr').data('attributesid');
					// Store the updated value in the object using attributesId as the key
					updatedData[attributesId] = updatedValue;	
				});
			} else if ($(this).find('.variationsSOinput').length > 0){
				apiAction = 'product_variations_updateSortOrder';
				$('.indexSO.variationsSOinput', ui.item.parent()).each(function (i) {
					$(this).val(i).trigger("change");			
					// Get the value of the input field
					var updatedValue = $(this).val();
					// Get the data-attributesid of the parent row
					var attributesId = $(this).closest('tr').data('variationsid');
					// Store the updated value in the object using attributesId as the key
					updatedData[attributesId] = updatedValue;	
				});
			} else if ($(this).find('.relatedSOinput').length > 0){
				apiAction = 'related_products_updateSortOrder';								
				$('.indexSO.relatedSOinput', ui.item.parent()).each(function (i) {	
					$(this).val(i).trigger("change");			
					// Get the value of the input field
					var updatedValue = $(this).val();
					// Get the data-attributesid of the parent row
					var attributesId = $(this).closest('tr').data('relatedid');
					// Store the updated value in the object using attributesId as the key
					updatedData[attributesId] = updatedValue;	
				});


			}
			
			
			var jsonData = JSON.stringify(updatedData);
			
			if (apiAction != null){
				$.get("api.php?action=" + apiAction, { jsonData: jsonData }, function(response) {
					console.log(response);
				});
			} else {
				console.log('Failed to find classname');
			}
			
		};

		$(".table-draggable tbody").sortable({
			helper: fixHelperModified,
			stop: updateIndex
		}).disableSelection();
		
	$(".table-draggable tbody").sortable({
		distance: 5,
		delay: 100,
		opacity: 0.6,
		cursor: 'move',
		connectWith: '.connectedSortable',
		update: function() {}
	});

	function relatedProductsBuildEmptyTable(relatedTableID){		
		return	$("#relatedProducts").append(''
			+ '<table id="relatedProductsTable-' + relatedTableID + '" width="100%" cellspacing="3" cellpadding="3" class="table-draggable table table-striped table-hover table-condensed small" data-tableId="' + relatedTableID + '">'
			+ '		<thead class="thead-dark">'
            + '		    <tr>'
            + '		        <th>#</th>'
            + '		        <th>Product</th>'
            + '		        <th class="text-right">Action</th>'
			+ '		    </tr>'
			+ '		 </thead>'
			+ '   <tbody id="relatedProductsTable-' + relatedTableID + '" class="connectedSortable">'
			+ '	 </tbody>'
			+ '</table>'
		).fadeIn("slow");
	}
	
	function relatedProductsBuildAddProductRow(relatedId,relatedTableID,productIdNum,productModel,productName,relatedSortOrder) {
		return $("#relatedProductsTable-" + relatedTableID).append(
			$('<tr class="table-active" id="relatedProductsRow' + productIdNum + '" data-id="' + productIdNum + '" data-relatedId="' + relatedId +'">'
				+ '		<td style="width:5%" class="relatedProductsSOtd">'
				+ '			<input class="indexSO relatedProductSOinput" type="text" name="" size="1" maxlength="4" value="' + relatedSortOrder + '"></td>'
				+ '		<td style="width:15%">' + productModel + ' </td>'
				+ '		<td style="width:80%">' + productName + ' </td>'
				+ '		<td class="relatedProductsDelete listsOptionsDelete">x</td>'
				+ '</tr>'
			).fadeIn("slow")
		);
	}
	$(document).on("click","#relatedProductsAddProduct",function(e) {
		relatedTableID = $('#related_products_table_select').val();
		if (relatedTableID == "999") {
			lastTableID = $("#relatedProducts").children().last().attr('data-tableid');							
			relatedTableID = parseInt(lastTableID) + 1;						
		}
		if ($("#relatedProductsTable-" + relatedTableID + " tbody").children().length > 0) {
				relatedSortOrder = $("#relatedProductsTable-" + relatedTableID + " tbody").children().length;
			} else {					
				relatedSortOrder = 0;
			}
		$.get("api.php?action=related_products_addRelatedProduct&products_id=<?php echo $pInfo->products_id ?>&related_products_id=" + $('#related_products_select').val() + "&related_table_id=" + relatedTableID + "&relatedSortOrder=" + relatedSortOrder, function(data, status){
			if (status == "success") {
				if (data["Complete"] > 1) {
					if (data["products"][0]) {
						productIdNum = data["products"][0]["id"];
						theElementID = "#relatedProductsRow" + productIdNum;
						productName = data["products"][0]["title"];
						productModel = data["products"][0]["model"];
						relatedId = data["products"][0]["relatedId"];
						relatedTableID = $('#related_products_table_select').val();
						if (relatedTableID == "999") {
							if ($("#relatedProducts").children().length > 0) {
							lastTableID = $("#relatedProducts").children().last().attr('data-tableid');						
							relatedTableID = parseInt(lastTableID) + 1;
							} else {
								relatedTableID = 0;					
							}
							relatedProductsBuildEmptyTable(relatedTableID);						
							$("#relatedProductsTable-"  + relatedTableID + " tbody").sortable();
							$("#related_products_table_select").append($('<option>', {
								value: relatedTableID,
								text: relatedTableID
							}));
							$("#related_products_table_select").val(relatedTableID);	
						}
						if ($("#relatedProductsTable-" + relatedTableID + " tbody").children().length > 0) {
							relatedSortOrder = $("#relatedProductsTable-" + relatedTableID + " tbody").children().length;
						} else {					
							relatedSortOrder = 0;
						}
						relatedProductsBuildAddProductRow(relatedId,relatedTableID,productIdNum,productModel,productName,relatedSortOrder);
						temp = $("#relatedProductsTable-"  + relatedTableID + " tbody");
						$("#relatedProductsTable-"  + relatedTableID + " tbody").trigger("stop");
						$("#relatedProductsTable-"  + relatedTableID + " tbody").sortable( "refresh" );		
					}
					e.stopPropagation();					
				} else {
					var searchTerm = data["products"][0]["model"].toLowerCase();
					$('.relatedProductsRow').each(function() {
						  var product = $(this).text().toLowerCase();
						  if (product.indexOf(searchTerm) >= 0) {
							$(this).addClass('danger');
							$(this).popover({
							  title: 'Product found',						  
							  content: '<img src="images/friends-you-idiot.gif">',
							  html: true,
							  boundary: 'viewport',
							  boundaryPadding: 10,
							  flip: false,
							  fallbackPlacement: 'none',
							  placement: 'top',
							  trigger: 'focus'
							}).on('click', function() {
								$(this).removeClass('danger');
								$(this).popover('destroy');						  
							}).on('shown.bs.popover', function () {
								var popover = $(this).data('bs.popover').tip();
								var popoverTop = popover.offset().top;
								var scrollTop = $(window).scrollTop();
								var topOffset = popoverTop - scrollTop;
	
								if (topOffset < 0) {
									$('html, body').animate({scrollTop: scrollTop + topOffset}, 500);
								}
							});
							
							$(this).popover('show');					  
						  }
					});
					
				}
			}
		});		
	});
	
	$(document).on("click","#copyRelatedListFrom",function(e) {
		$.get("api.php?action=related_products_copyRelatedListFrom&products_id=<?php echo $pInfo->products_id ?>&source_id=" + $('#related_products_select').val() + "", function(data, status){
			if (status == "success") {
				if (data["Complete"] > 1) {
					lastTable = -1;
					$("#relatedProducts").empty();
					for (x in data["products"]) {
						productIdNum = data["products"][x]["id"];
						productName = data["products"][x]["title"];
						productModel = data["products"][x]["model"];
						productPrice = data["products"][x]["price"];
						relatedSortOrder = data["products"][x]["sortOrder"];
						relatedId = data["products"][x]["relatedId"];
						relatedTableID = data["products"][x]["relatedTableID"];
						if (lastTable != relatedTableID) {							
							relatedProductsBuildEmptyTable(relatedTableID);
							$("#related_products_table_select").children().last().before('<option value="' + relatedTableID + '">' + relatedTableID + '</option>');
							lastTable = relatedTableID;
						}
						relatedProductsBuildAddProductRow(relatedId,relatedTableID,productIdNum,productModel,productName,relatedSortOrder);
						$("#relatedProductsTable-"  + relatedTableID + "tbody").trigger("stop");
						$("#relatedProductsTable-"  + relatedTableID + "tbody").sortable( "refresh" );					
						e.stopPropagation();
					}
					$("#table-draggable2 tbody").trigger("stop");
				}
			}
		});			
	});
	
	$(document).on("click","#relatedProductsSearchButton",function(e) {
		$.get("api.php?action=related_products_searchRelatedProduct&search_term=" + $('#relatedProductSearchinput').val() + "", function(data, status){
			if (status == "success") {				
				$("#related_products_select").empty();				
				for (x in data["products"]) {
					theID = data["products"][x]["id"];					
					productName = data["products"][x]["title"];
					$("#related_products_select").append($("<option>").attr('value',theID).text(productName));
				}				
			}
		});			
	});	

	$("#relatedProducts").on("click",".relatedProductsDelete",function(e) {
		var that = this;
		$.get("api.php?action=related_products_removeRelatedProduct&products_id=<?php echo $pInfo->products_id ?>&products_related_id=" + $(that).parent().attr("data-relatedId") + "", function(data, status){
			if (status == "success") {
				e.stopPropagation();
				$(that).parent().fadeOut("slow",function(){
					this.remove();
				});
			}
		});	
	});
	
	
	//$("#relatedProducts").on("change",".relatedProductSOinput",function(e) {
	//	var that = this;			
	//	$.get("api.php?action=related_products_updateSortOrder&products_id=<?php echo $pInfo->products_id ?>&products_related_id=" + $(that).parent().parent().attr("data-relatedId") + "&related_product_sort_order=" + $(that).val(), function(data, status){
	//		if (status == "success") {}
	//	});	
	//});	
	<?php if (isset($_GET['pID'])) { ?>
		$("#applyButton").click(function(e) {
			
			for ( instance in CKEDITOR.instances )
				CKEDITOR.instances[instance].updateElement();

			var form = $('#new_product_form')[0];
			var data = new FormData(form);
			$.ajax({
			  type: "POST",
			  enctype: 'multipart/form-data',
			  url: "categories.php?cPath=<?php echo $cPath . "&pID=" . $_GET['pID'] ?>&action=update_product",
			  data: data,
			  contentType: false,
			  processData: false

			});
		});	
	<?php } else { ?>
		$("#applyButton").disable();
	<?php } ?>
});
</script>
<?php //EPP: -- Related table	 
?>   
	<tr>
         <td id="relatedProducts" colspan=5 class="main"> 
    <?php  	
		$lastTable = -1;
		$get_related_products_query = tep_db_query("SELECT * FROM products as p, products_related as r, products_description as d WHERE r.products_related_related_products_id=p.products_id AND d.products_id=p.products_id AND r.products_related_products_id=" . (int) $_GET['pID'] . " ORDER BY r.products_related_table_id, r.products_related_sort_order");
        while ($products_related = tep_db_fetch_array($get_related_products_query)) {
			if ($lastTable != $products_related['products_related_table_id']) {
				if ($products_related['products_related_table_id'] > 0) {?>					
					</tbody></table>				
				<?php } 
					++$lastTable;?>
					<span><strong>Table <?php echo $lastTable ?></strong> </span> <span class="pull-right"> Code: <input class="relatedProductCodeInput" type="text" name="" size="30" maxlength="30" value="<?php echo "relatedProductsModuleContainer-" . $lastTable;?>"></span>
				<table id="relatedProductsTable-<?php echo $products_related['products_related_table_id'] ?>" cellspacing="3" cellpadding="3" class="table-draggable table table-striped table-hover table-condensed" width="100%" data-tableid="<?php echo $products_related['products_related_table_id'] ?>">
				  <thead class="thead-dark">
					<tr>
					  <th>#</th>
					  <th>Product</th>              
					  <th class="text-right">Action</th>
								  </tr>
				  </thead>
				  <tbody id="relatedProductsTable-<?php echo $products_related['products_related_table_id'] ?>" class="connectedSortable">
		  
		  
		<?php }	?>
		
			<tr class="table-active small" id="relatedProductsRow<?php echo $products_related['products_id'];?>" data-id="<?php echo $products_related['products_id'] ;?>" data-relatedId="<?php echo $products_related['products_related_id'] ;?>">
				<td style="width:5%"class="relatedProductsSOtd" style="width:10px"><input class="indexSO relatedProductSOinput" type="text" name="" size="1" maxlength="4" value="<?php echo $products_related['products_related_sort_order'];?>"></td>
				<td style="width:15%"> <?php echo $products_related['products_model'];?> </td>
				<td style="width:80%"> <?php echo $products_related['products_name'];?> </td>
				<td class="relatedProductsDelete listsOptionsDelete">x</td>
			</tr>
		<?php }?>
		 </tbody></table>
		
            </td></tr> 
		<tr>
			<td id="relatedProductsToolbar" colspan="5" class="main">
				<table cellspacing="3" cellpadding="3" class="table ">          
					<tbody>  
						<tr> 
							<td align="left" class="text-left">   
								&nbsp;<input  id="relatedProductSearchinput" type="text" name="" size="20" maxlength="100" value="">
								&nbsp;<button id="relatedProductsSearchButton" class="btn btn-primary" type="button">Search</button>&nbsp;
								&nbsp;<select id="related_products_select" style="width: 400px;"></select>&nbsp;
								&nbsp;Table:&nbsp;<select id="related_products_table_select" style="">
								<?php 
								for ($i=0;$i <= $lastTable;$i++) {
									echo '<option value="' .  $i . '">' . $i . '</option>';										
								}
								?>
								<option value="999">Add New...</option></select>&nbsp;
								&nbsp;<button id="relatedProductsAddProduct" class="btn btn-primary" type="button">Insert</button>&nbsp;
								&nbsp;<button id="copyRelatedListFrom" class="btn btn-primary" type="button">Get Product List</button>&nbsp;
							<?php // &nbsp;<button id="linkRelatedListFrom" class="btn btn-primary" type="button">Link</button>&nbsp; ?>
							<?php // 	&nbsp;<button id="relatedAddTable" class="btn btn-primary" type="button">Add Table</button>&nbsp;?>
							</td>
						</tr>
					</tbody>
				 </table>
			 </td>
		 </tr>
		 <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
            <tr>
            <?php echo TEXT_PRODUCTS_STATUS; ?></td>
            <td class="main"><?php echo tep_draw_radio_field('products_status', '1', $in_status) . '&nbsp;' . TEXT_PRODUCT_AVAILABLE . '&nbsp;' . tep_draw_radio_field('products_status', '0', $out_status) . '&nbsp;' . TEXT_PRODUCT_NOT_AVAILABLE; ?></td>
				<td class="main">
            
            
            
            <?php echo 'Google Status:'; ?></td>
            <td class="main"><?php echo tep_draw_radio_field('products_google_status', '1', $in_google_status) . '&nbsp;' . 'List' . '&nbsp;' . tep_draw_radio_field('products_google_status', '0', $out_google_status) . '&nbsp;' . 'Do not list'; ?></td>
      </tr>
      <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <tr>
            <td class="main"><?php echo TEXT_PRODUCTS_DATE_AVAILABLE; ?></td>
            <td class="main"><?php echo tep_draw_input_field('products_date_available', $pInfo->products_date_available, 'id="products_date_available"') . ' <small>(YYYY-MM-DD)</small>'; ?></td>
          </tr>
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
      <tr>
        <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      </tr>
      <tr>
        <td class="smallText" align="right"><?php echo tep_draw_hidden_field('products_date_added', (@tep_not_null($pInfo->products_date_added) ? $pInfo->products_date_added : date('Y-m-d'))) . tep_draw_button(IMAGE_SAVE, 'disk', null, 'primary') . tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('categories.php', 'cPath=' . $cPath . (isset($_GET['pID']) ? '&pID=' . $_GET['pID'] : ''))); ?></td>
      </tr>
    </table></td></tr>
    </table>

<script type="text/javascript">
$('#products_date_available').datepicker({
  dateFormat: 'yy-mm-dd'
});
</script>

  
<nav class="navbar navbar-default navbar-fixed-bottom form-inline">
    <div class="navbar-header">
	<div class="btn-group navbar-form" role="group">
		  <?php 
		  echo tep_draw_button(IMAGE_SAVE, 'disk', null, 'primary');
		  echo tep_draw_hidden_field('products_date_added', (@tep_not_null($pInfo->products_date_added) ? $pInfo->products_date_added : date('Y-m-d')));?>
		  <button type="button" class="btn btn-default" id="applyButton">Apply</button>
		</div> 
       
	</div>
		
	
	
		<?php
    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) { ?>
         <div class="navbar-form navbar-left" role="group" ><label><?php
        if ($i == 0)
            echo TEXT_PRODUCTS_NAME; ?></label>
            <?php
        echo tep_draw_input_field('products_name[' . $languages[$i]['id'] . ']', (empty($pInfo->products_id) ? '' : tep_get_products_name($pInfo->products_id, $languages[$i]['id'])), 'size="65" class="form-control" '); ?>
		<?php echo '<a href="'. HTTPS_SERVER . '\\-p-' .  $pInfo->products_id . '.html" class="btn btn-default" id="publicButton"  target="_blank">Open in website</a>';?>
		
		</div>
            
        <?php
    } ?>
	<div class="btn-group navbar-right navbar-form" role="group">
		  <?php echo tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('categories.php', 'cPath=' . $cPath . (isset($_GET['pID']) ? '&pID=' . $_GET['pID'] : ''))); ?>
	</div>
</nav>  
</form>
<?php
} elseif ($action == 'new_product_preview') {
    $product_query = tep_db_query("select p.products_id, pd.language_id, pd.products_name, pd.products_description, pd.products_url, pd.products_video_url, p.products_quantity, p.products_model, p.products_gtin, p.products_image, p.products_price, p.products_weight, p.products_date_added, p.products_last_modified, p.products_date_available, p.products_status, p.manufacturers_id, p.products_request_quote, pd.products_seo_title, p.products_gtin, pd.products_seo_description, pd.products_seo_keywords from products p, products_description pd where p.products_id = pd.products_id and p.products_id = '" . (int) $_GET['pID'] . "'");
    $product       = tep_db_fetch_array($product_query);
    
    $pInfo               = new objectInfo($product);
    $products_image_name = $pInfo->products_image;
    
    // start indvship
    $products_shipping_query = tep_db_query("SELECT * FROM products_shipping WHERE products_id=" . (int) $_GET['pID']);
    while ($products_shipping = tep_db_fetch_array($products_shipping_query)) {
        $products_ship_methods_id = $products_shipping['products_ship_methods_id'];
        $products_ship_key        = $products_shipping['products_ship_key'];
        $products_ship_price      = $products_shipping['products_ship_price'];
        $products_ship_qty        = $products_shipping['products_ship_qty'];
        $products_ship_flags      = $products_shipping['products_ship_flags'];
    }
    $shipping = array(
        'products_ship_methods_id' => $products_ship_methods_id,
        'products_ship_key' => $products_ship_key,
        'products_ship_price' => $products_ship_price,
        'products_ship_qty' => $products_ship_qty,
        'products_ship_flags' => $products_ship_flags
    );
    $pInfo->objectInfo($shipping);
    // end indvship  
    $languages = tep_get_languages();
    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
        $pInfo->products_name            = tep_get_products_name($pInfo->products_id, $languages[$i]['id']);
        $pInfo->products_description     = tep_get_products_description($pInfo->products_id, $languages[$i]['id']);
        $pInfo->products_url             = tep_get_products_url($pInfo->products_id, $languages[$i]['id']);
        $pInfo->products_seo_description = tep_get_products_seo_description($pInfo->products_id, $languages[$i]['id']);
        $pInfo->products_seo_keywords    = tep_get_products_seo_keywords($pInfo->products_id, $languages[$i]['id']);
        $pInfo->products_seo_title       = tep_get_products_seo_title($pInfo->products_id, $languages[$i]['id']);
        $pInfo->products_video_url       = tep_get_products_video_url($pInfo->products_id, $languages[$i]['id']); ?>
   
<table border="0" width="100%" cellspacing="0" cellpadding="2">
      <tr>
        <td colspan="4"><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td class="pageHeading" colspan="4"><?php
        echo tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']) . '&nbsp;' . $pInfo->products_name; ?></td>
            <td class="pageHeading" align="right"><?php
        echo $currencies->format($pInfo->products_price); ?></td>
          </tr>
          
          
          <?php
        if ($pInfo->products_video_url) { ?>
     <tr>
        <td><?php
            echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      </tr>
      <tr>
        <td class="main"><div style="width:200px"><?php
            
            echo $pInfo->products_video_url; ?></div></td>
      </tr>
<?php
        } ?>
         
          
          
          
        </table></td>
      </tr>
      <tr>
        <td><?php
        echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      </tr>
      <tr>
        <td class="main"><?php
        echo tep_image(HTTP_CATALOG_SERVER . DIR_WS_CATALOG_IMAGES . $products_image_name, $pInfo->products_name, SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT, 'align="right" hspace="5" vspace="5"') . $pInfo->products_description; ?></td>
      </tr>
<?php
        if ($pInfo->products_url) { ?>
     <tr>
        <td><?php
            echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      </tr>
      <tr>
        <td class="main"><?php
            echo sprintf(TEXT_PRODUCT_MORE_INFORMATION, $pInfo->products_url); ?></td>
      </tr>
<?php
        } ?>
     <tr>
        <td><?php
        echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      </tr>
<?php
        if ($pInfo->products_date_available > date('Y-m-d')) { ?>
     <tr>
        <td align="center" class="smallText"><?php
            echo sprintf(TEXT_PRODUCT_DATE_AVAILABLE, tep_date_long($pInfo->products_date_available)); ?></td>
      </tr>
<?php
        } else { ?>
     <tr>
        <td align="center" class="smallText"><?php
            echo sprintf(TEXT_PRODUCT_DATE_ADDED, tep_date_long($pInfo->products_date_added)); ?></td>
      </tr>
<?php
        } ?>
     <tr>
        <td><?php
        echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      </tr>
<?php
    }
    
    if (isset($_GET['origin'])) {
        $pos_params = strpos($_GET['origin'], '?', 0);
        if ($pos_params != false) {
            $back_url        = substr($_GET['origin'], 0, $pos_params);
            $back_url_params = substr($_GET['origin'], $pos_params + 1);
        } else {
            $back_url        = $_GET['origin'];
            $back_url_params = '';
        }
    } else {
        $back_url        = 'categories.php';
        $back_url_params = 'cPath=' . $cPath . '&pID=' . $pInfo->products_id;
    } ?>
     <tr>
        <td align="right" class="smallText"><?php echo tep_draw_button(IMAGE_BACK, 'triangle-1-w', tep_href_link($back_url, $back_url_params)); ?></td>
      </tr>
    </table>
<?php
} else { ?>
   <table border="0" width="100%" cellspacing="0" cellpadding="2">
      <tr>
        <td colspan="4"><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td class="pageHeading" colspan="4"><?php echo HEADING_TITLE; ?></td>
            <td class="pageHeading" align="right"><?php echo tep_draw_separator('pixel_trans.gif', 1, HEADING_IMAGE_HEIGHT); ?></td>
            <td align="right"><table border="0" width="100%" cellspacing="0" cellpadding="0">
              <tr>
                <td class="smallText" align="right">
<?php echo tep_draw_form('search', 'categories.php', '', 'get');
    echo HEADING_TITLE_SEARCH . ' ' . tep_draw_input_field('search');
    echo tep_hide_session_id() . '</form>'; ?>
               </td>
              </tr>
              <tr>
                <td class="smallText" align="right">
<?php echo tep_draw_form('goto', 'categories.php', '', 'get');
    echo HEADING_TITLE_GOTO . ' ' . tep_draw_pull_down_menu('cPath', tep_get_category_tree(), $current_category_id, 'onchange="this.form.submit();"');
    echo tep_hide_session_id() . '</form>'; ?>
               </td>
              </tr>
            </table></td>
          </tr>
        </table></td>
      </tr>
      <tr>
        <td><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
          <!--sort order//-->
            <td valign="top">

                <table class="table table-condensed table-hover table-striped small" border="0" width="100%" cellspacing="0" cellpadding="2"><?php echo tep_draw_form('setsortorder', 'categories.php', 'action=setsortorder'); ?><input type="hidden" name="cPath" value="<?php echo $cPath; ?>"><?php  if (isset($_GET['search'])){?><input type="hidden" name="search" value="<?php echo $_GET['search']; ?>"><?php }?>
              <thead>
			  <tr>
<!--end sort order//-->
              <!--BOF - Added code for Admin Sort by products model---->
<th><?php echo 'Model'; ?></th>
<!--EOF - Added code for Admin Sort by products model---->
                <th><?php echo TABLE_HEADING_CATEGORIES_PRODUCTS; ?></th>
                <th align="center"><?php echo TABLE_HEADING_STATUS; ?></th>
 <th align="center"><?php echo 'Google'; ?></td>
                <th align="center"><?php echo 'Price'; ?>&nbsp;</th>
                <th align="center"><?php echo 'Sort'; ?>&nbsp;</th>
              </tr></thead><tbody>
<?php
    $categories_count = 0;
    $rows             = 0;
    if (isset($_GET['search'])) {
        $search = tep_db_prepare_input($_GET['search']);
        $categories_query = tep_db_query("select c.categories_id, cd.categories_name, cd.categories_description, cd.categories_seo_description, cd.categories_seo_keywords, cd.categories_seo_title, c.categories_image, c.parent_id, c.sort_order, c.date_added, c.last_modified, c.google_category, c.google_category_baseline from categories c, categories_description cd where c.categories_id = cd.categories_id and cd.language_id = '" . (int) $languages_id . "' and cd.categories_name like '%" . tep_db_input($search) . "%' order by c.sort_order, cd.categories_name");
    } else {
        $categories_query = tep_db_query("select c.categories_id, cd.categories_name, cd.categories_description, cd.categories_seo_description, cd.categories_seo_keywords, cd.categories_seo_title, c.categories_image, c.parent_id, c.sort_order, c.date_added, c.last_modified, c.google_category, c.google_category_baseline from categories c, categories_description cd where c.parent_id = '" . (int) $current_category_id . "' and c.categories_id = cd.categories_id and cd.language_id = '" . (int) $languages_id . "' order by c.sort_order, cd.categories_name");
    }
    while ($categories = tep_db_fetch_array($categories_query)) {
        $categories_count++;
        $rows++;
        
        // Get parent_id for subcategories if search
        if (isset($_GET['search']))
            $cPath = $categories['parent_id'];
        
        if ((!isset($_GET['cID']) && !isset($_GET['pID']) || (isset($_GET['cID']) && ($_GET['cID'] == $categories['categories_id']))) && !isset($cInfo) && (substr($action, 0, 3) != 'new')) {
            $category_childs   = array(
                'childs_count' => tep_childs_in_category_count($categories['categories_id'])
            );
            $category_products = array(
                'products_count' => tep_products_in_category_count($categories['categories_id'])
            );
            
            $cInfo_array = array_merge($categories, $category_childs, $category_products);
            $cInfo       = new objectInfo($cInfo_array);
        }
        
        if (isset($cInfo) && is_object($cInfo) && ($categories['categories_id'] == $cInfo->categories_id)) {
            echo '              <tr id="defaultSelected" class="dataTableRowSelected success" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link('categories.php', tep_get_path($categories['categories_id'])) . '\'">' . "\n";
        } else {
            echo '              <tr class="dataTableRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link('categories.php', 'cPath=' . $cPath . '&cID=' . $categories['categories_id']) . '\'">' . "\n";
        } ?>
			<!--BOF - Added code for Admin Sort by products model---->
			<td class="dataTableContent" width="5%" nowrap></td>
			<!--EOF - Added code for Admin Sort by products model---->
                <td class="dataTableContent"><?php
        echo '<a href="' . tep_href_link('categories.php', tep_get_path($categories['categories_id'])) . '">' . tep_image('images/icons/folder.gif', ICON_FOLDER) . '</a>&nbsp;<strong>' . $categories['categories_name'] . '</strong>'; ?></td>
                <td class="dataTableContent" align="center">&nbsp;</td> <td class="dataTableContent" align="center">&nbsp;</td> <td class="dataTableContent" align="center">&nbsp;</td>
                <td class="dataTableContent" align="right"><?php
					if (isset($cInfo) && is_object($cInfo) && ($categories['categories_id'] == $cInfo->categories_id)) {
						echo tep_image('images/icon_arrow_right.gif', '');
					} else {
						echo '<a href="' . tep_href_link('categories.php', 'cPath=' . $cPath . '&cID=' . $categories['categories_id']) . '">' . tep_image('images/icon_info.gif', IMAGE_ICON_INFO) . '</a>';
					}
					?>&nbsp;
					</td>
				</tr>
<?php
    }
    
    $products_count = 0;
    
    /*
    BOF search by model number
    */
    
    
    if (isset($_GET['search'])) {
        // $products_query = tep_db_query("select p.products_id, pd.products_name, p.products_model, p.products_gtin, p.products_quantity, p.products_image, p.products_price, p.products_date_added, p.products_last_modified, p.products_date_available, p.products_status, p2c.categories_id from products p, products_description pd, products_to_categories p2c where p.products_id = pd.products_id and pd.language_id = '" . (int)$languages_id . "' and p.products_id = p2c.products_id and((pd.products_name like '%" . tep_db_input($search) . "%') || (p.products_model like '%" . tep_db_input($search) . "%') ||  (p.products_gtin like '%" . tep_db_input($search) . "%')) order by pd.products_name");
        //  } else {
        // $products_query = tep_db_query("select p.products_id, pd.products_name, p.products_quantity, p.products_image, p.products_price, p.products_date_added, p.products_last_modified, p.products_date_available, p.products_status from products p, products_description pd, products_to_categories p2c where p.products_id = pd.products_id and pd.language_id = '" . (int)$languages_id . "' and p.products_id = p2c.products_id and p2c.categories_id = '" . (int)$current_category_id . "' order by pd.products_name");
        
		//$where_str = " where p.products_status = '1' and p.products_id = pd.products_id and pd.language_id = '" . (int)$languages_id . "' and p.products_id = p2c.products_id and p2c.categories_id = c.categories_id ";

		$search_keywords = explode(' ',$_GET['search']);
		
		if (isset($search_keywords) && (sizeof($search_keywords) > 0)) {
			$where_str .= "and (";
			for ($i=0, $n=sizeof($search_keywords); $i<$n; $i++ ) {			  
			  switch ($search_keywords[$i]) {
				case '(':
				case ')':
				case 'and':
				case 'or':
				  $where_str .= " " . $search_keywords[$i] . " ";
				  break;
				default:
				  $keyword = tep_db_prepare_input($search_keywords[$i]);
				  if (!$i == 0) $where_str .= " and ";
				  $where_str .= "(";
				  $where_str .= "pd.products_name like '%" . tep_db_input($keyword) . "%' or p.products_model like '%" . tep_db_input($keyword) . "%' or m.manufacturers_name like '%" . tep_db_input($keyword) . "%'";
				
				  $where_str .= ')';
				  break;
			  }
			}
			$where_str .= " )";
		}
		//echo "where: " . $where_str;
		
		//$products_query = tep_db_query("select p.products_id, pd.products_name, p.products_model, p.products_gtin, p.products_quantity, p.products_image, p.products_price, p.products_date_added, p.products_last_modified, p.products_date_available, p.products_status, p.products_google_status,  p2c.categories_id, p.products_sort_order, p.products_request_quote  from products p, products_description pd, products_to_categories p2c where p.products_id = pd.products_id and pd.language_id = '" . (int) $languages_id . "' and p.products_id = p2c.products_id and ((pd.products_name like '%" . tep_db_input($search) . "%') || (p.products_model like '%" . tep_db_input($search) . "%') ||  (p.products_gtin like '%" . tep_db_input($search) . "%')) group by p.products_id order by p.products_sort_order ");
		$products_query = tep_db_query("select p.products_id, pd.products_name, p.products_model, p.products_gtin, p.products_quantity, p.products_image, p.products_price, p.products_date_added, p.products_last_modified, p.products_date_available, p.products_status, p.products_google_status,  p2c.categories_id, p.products_sort_order, p.products_request_quote  from products p, products_description pd, products_to_categories p2c, manufacturers m where p.products_id = pd.products_id and pd.language_id = '" . (int) $languages_id . "' and p.products_id = p2c.products_id " . $where_str . " group by p.products_id order by p.products_sort_order ");
	
	} else {
        $products_query = tep_db_query("select p.products_id, pd.products_name, p.products_model, p.products_quantity, p.products_image, p.products_price, p.products_date_added, p.products_last_modified, p.products_date_available, p.products_status, p.products_google_status, p.products_sort_order, p.products_request_quote from products p, products_description pd, products_to_categories p2c where p.products_id = pd.products_id and pd.language_id = '" . (int) $languages_id . "' and p.products_id = p2c.products_id and p2c.categories_id = '" . (int) $current_category_id . "' order by p.products_sort_order");
   }
    // Start Mark - Google category calculation functions
    $catInfo = "
SELECT
categories.categories_id AS curCatID,
categories.parent_id AS parentCatID,
categories_description.categories_name AS catName,
categories.google_category AS catGoog,
categories.google_category_baseline AS catGoogBase
FROM
categories,
categories_description
WHERE categories.categories_id = categories_description.categories_id
";
    
    
    
    
    
    $catIndex    = array();
    $catTempDes  = array();
    $catTempPar  = array();
    $catTempBase = array();
    $processCat  = tep_db_query($catInfo);
    
    while ($catRow = tep_db_fetch_array($processCat)) {
        /*if($catRow['curCatID']==663 || $catRow['curCatID']==473 ){
        
        echo '
        $catKey = ' . $catRow['curCatID'] . '<br>
        $catName = ' . $catRow['catName'] . '<br>
        $catParID = ' . $catRow['parentCatID'] . '<br> 
        $catGoogTxt = ' . $catRow['catGoog'] . '<br>
        $catBaseLine = ' . $catRow['catGoogBase'];
        }*/
        $catKey      = $catRow['curCatID'];
        $catName     = $catRow['catName'];
        $catParID    = $catRow['parentCatID'];
        $catGoogTxt  = $catRow['catGoog'];
        $catBaseLine = $catRow['catGoogBase'];
        if ($catName != "") {
            $catTempDes[$catKey]  = $catName;
            /*if($catRow['curCatID'] == 663 || $catRow['curCatID'] == 473 ){
            echo '$catTempPar[' . $catKey . ']=' . $catTempPar[$catKey] . '<br>';
            }*/
            $catTempPar[$catKey]  = $catParID;
            $catTempBase[$catKey] = $catBaseLine;
            $catGoog[$catKey]     = $catGoogTxt;
            /*if($catRow['curCatID']==663 || $catRow['curCatID']==473 ){
            echo '$catTempPar[' . $catKey . ']=' . $catTempPar[$catKey] . '<br>';
            }*/
        }
      
    }
    /*
    end Mark - Google category calculation functions
    */
    
    while ($products = tep_db_fetch_array($products_query)) {
        $products_count++;
        $rows++;
        
        // Get categories_id for product if search
        if (isset($_GET['search']))
            $cPath = $products['categories_id'];
        
        if ((!isset($_GET['pID']) && !isset($_GET['cID']) || (isset($_GET['pID']) && ($_GET['pID'] == $products['products_id']))) && !isset($pInfo) && !isset($cInfo) && (substr($action, 0, 3) != 'new')) {
            // find out the rating average from customer reviews
            $reviews_query = tep_db_query("select (avg(reviews_rating) / 5 * 100) as average_rating from reviews where products_id = '" . (int) $products['products_id'] . "'");
            $reviews       = tep_db_fetch_array($reviews_query);
            $pInfo_array   = array_merge($products, $reviews);
            $pInfo         = new objectInfo($pInfo_array);
        }
        
       /* if (isset($pInfo) && is_object($pInfo) && ($products['products_id'] == $pInfo->products_id)) {
            echo '              <tr id="defaultSelected" class="dataTableRowSelected success" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $products['products_id'] . '&action=new_product_preview') . '\'">' . "\n";
        } else {
            echo '              <tr class="dataTableRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $products['products_id']) . '\'">' . "\n";
        }*/
		
		
//sort order
    if ($products['products_status'] == 0) {
        $products_status_data = '0';
    } else {
        $products_status_data = '1';
    }
      if (!$sorting){      
		  if (isset($pInfo) && is_object($pInfo) && ($products['products_id'] == $pInfo->products_id) ) {
			echo '              <tr id="defaultSelected" data-status=' .  $products_status_data . ' class="dataTableRowSelected success" data-id="' . $products['products_id'] . '" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link('product_edit.php', 'cPath=' . $cPath . '&pID=' . $products['products_id'] . '&action=new_product_preview') . '\'">' . "\n";
		  } else {
			echo '              <tr class="dataTableRow" data-status=' .  $products_status_data . ' onmouseover="rowOverEffect(this)" data-id="' . $products['products_id'] . '" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $products['products_id']) . '\'">' . "\n";
		  }
      }else{
			if (isset($pInfo) && is_object($pInfo) && ($products['products_id'] == $pInfo->products_id) ) {
			  echo '              <tr id="defaultSelected" data-status=' .  $products_status_data . ' class="dataTableRowSelected success">' . "\n";
			} else {
			  echo '              <tr class="dataTableRow" data-status=' .  $products_status_data . '>' . "\n";
			}
}

//end sort order ?>
<!--BOF - Added code for Admin Sort by products model---->
<td class="dataTableContent" width="5%" nowrap><?php
        echo '&nbsp;' . $products['products_model']; ?></td>
<!--EOF - Added code for Admin Sort by products model---->
                <td class="dataTableContent"><?php
        echo '<a href="' . tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $products['products_id'] . '&action=new_product_preview') . '">' . tep_image('images/icons/preview.gif', ICON_PREVIEW) . '</a>&nbsp;' . $products['products_name']; ?>&nbsp;<?php if (autodesk_products_getislinked($products['products_id'])) echo '<i class="fa fa-link"></i>'; ?></td>
                <td class="dataTableContent" align="center">
<?php
		if (isset($_GET['search'])){ 
			$setFlagRedirect = "&search="  . $_GET['search'];
		} else {
			$setFlagRedirect = '&cPath=' . $cPath;
		}
        if ($products['products_status'] == '1') {
            echo '<a href="' . tep_href_link('categories.php', 'action=setflag&flag=0&pID=' . $products['products_id'] . $setFlagRedirect) . '">' . tep_image('images/icon_status_green.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10) . '</a>';
        } else {
            echo '<a href="' . tep_href_link('categories.php', 'action=setflag&flag=1&pID=' . $products['products_id'] . $setFlagRedirect) . '">' . tep_image('images/icon_status_red_light.gif', IMAGE_ICON_STATUS_RED, 10, 10) . '</a>';
        } ?></td>
 <td class="dataTableContent" align="center">
<?php
        if ($products['products_google_status'] == '1') {
           echo '<a href="' . tep_href_link('categories.php', 'action=setgflag&flag=0&pID=' . $products['products_id'] . '&cPath=' . $cPath) . '">' . tep_image('images/icon_status_green.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10) . '</a>';
        } else {
            echo '<a href="' . tep_href_link('categories.php', 'action=setgflag&flag=1&pID=' . $products['products_id'] . '&cPath=' . $cPath) . '">' . tep_image('images/icon_status_red_light.gif', IMAGE_ICON_STATUS_RED, 10, 10) . '</a>';
	    } ?></td>
<!--price edit//-->
                <td class="dataTableContent" align="center">
                <?php
                if (!$sorting){
					if ($products['products_request_quote'] == 1) {
						echo 'RQ';
					}else{
						echo $currencies->format($products['products_price']);
					}
				}else{
					
					echo tep_draw_input_field('productsprice[]', number_format((float)$products['products_price'], 2, '.', ''),  'SIZE=5');
                }
                ?>
				</td>
<!--end sort order//-->
    
   
     <?php
/*       <td class="dataTableContent" align="right"><?php
        if (isset($pInfo) && is_object($pInfo) && ($products['products_id'] == $pInfo->products_id)) {
            echo tep_image('images/icon_arrow_right.gif', '');
        } else {
            echo '<a href="' . tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $products['products_id']) . '">' . tep_image('images/icon_info.gif', IMAGE_ICON_INFO) . '</a>';
        }
	//-->*/
?>

		<!-- sort order//-->
                <td class="dataTableContent" align="center">
                <?php
                if (!$sorting){
                  echo $products['products_sort_order'];
                }else{
                  echo tep_draw_input_field('sortorder[]', $products['products_sort_order'],  'SIZE=3') . tep_draw_hidden_field('products_id[]', $products['products_id']);
                }
                ?>
				</td>
		<!--end sort order//-->
              </tr>
<?php
    }
    
    $cPath_back = '';
    if (is_array($cPath_array) && sizeof($cPath_array) > 0) {
        for ($i = 0, $n = sizeof($cPath_array) - 1; $i < $n; $i++) {
            if (empty($cPath_back)) {
                $cPath_back .= $cPath_array[$i];
            } else {
                $cPath_back .= '_' . $cPath_array[$i];
            }
        }
    }
    
    $cPath_back = (@tep_not_null($cPath_back)) ? 'cPath=' . $cPath_back . '&' : ''; ?>
             <tr>
               <?php
/* <td colspan="3"><table border="0" width="100%" cellspacing="0" cellpadding="2">
                  <tr>
                    <td class="smallText"><?php echo TEXT_CATEGORIES . '&nbsp;' . $categories_count . '<br />' . TEXT_PRODUCTS . '&nbsp;' . $products_count; ?></td>
                    <td align="right" class="smallText"><?php
    if (sizeof($cPath_array) > 0)
        echo tep_draw_button(IMAGE_BACK, 'triangle-1-w', tep_href_link('categories.php', $cPath_back . 'cID=' . $current_category_id));
    if (!isset($_GET['search']))
        echo tep_draw_button(IMAGE_NEW_CATEGORY, 'plus', tep_href_link('categories.php', 'cPath=' . $cPath . '&action=new_category')) . tep_draw_button(IMAGE_NEW_PRODUCT, 'plus', tep_href_link('categories.php', 'cPath=' . $cPath . '&action=new_product')) . tep_draw_button('Bulk Edit', 'note', tep_href_link('products_multi.php', 'cPath=' . $cPath . '')); ?>&nbsp;</td>
                  </tr>
                </table></td>
              </tr>
            </table></td>*/
?>
			
			<!--sort order//-->
                <td colspan="4"><table border="0" width="100%" cellspacing="0" cellpadding="2">
                  <tr>
                    <td class="smallText"><?php echo TEXT_CATEGORIES . '&nbsp;' . $categories_count . '<br>' . TEXT_PRODUCTS . '&nbsp;' . $products_count; ?></td>
                    <td align="right" class="smallText">
                    <?php
                    if ($sorting){
						if (is_array($cPath_array)) if (sizeof($cPath_array) > 0) echo '<a href="' . tep_href_link('categories.php', $cPath_back . 'cPath=' . $cPath) . '">' . tep_image_button('button_back.gif', IMAGE_BACK) . '</a>&nbsp;'; echo tep_image_submit('button_save_sort.gif', IMAGE_SAVE_SORT) . '&nbsp;</td>';
							}else{
//						if (sizeof($cPath_array) > 0) echo '<a href="' . tep_href_link('categories.php', $cPath_back . 'cID=' . $current_category_id) . '">' . tep_image_button('button_back.gif', IMAGE_BACK) . '</a>&nbsp;'; if (!isset($_GET['search'])) echo '<a href="' . tep_href_link('categories.php', 'cPath=' . $cPath . '&action=new_category') . '">' . tep_image_button('button_new_category.gif', IMAGE_NEW_CATEGORY) . '</a>&nbsp;<a href="' . tep_href_link('categories.php', 'cPath=' . $cPath . '&action=new_product') . '">' . tep_image_button('button_new_product.gif', IMAGE_NEW_PRODUCT) . '</a>&nbsp;'; if(isset($_GET['search'])){echo '<a href="' . tep_href_link('categories.php', 'search=' . $_GET['search'] . '&action=beginsort') . '">' . tep_image_button('button_sort_order.gif', IMAGE_SORT_ORDER) . '</a>&nbsp;</td>' ;} else { echo '<a href="' . tep_href_link('categories.php', 'cPath=' . $cPath . '&action=beginsort') . '">' . tep_image_button('button_sort_order.gif', IMAGE_SORT_ORDER) . '</a>&nbsp;</td>';  }}
						if (is_array($cPath_array)) if (sizeof($cPath_array) > 0) echo '<a href="' . tep_href_link('categories.php', $cPath_back . 'cID=' . $current_category_id) . '">' . tep_image_button('button_back.gif', IMAGE_BACK) . '</a>&nbsp;'; if (!isset($_GET['search'])) echo '<a href="' . tep_href_link('categories.php', 'cPath=' . $cPath . '&action=new_category') . '">' . tep_image_button('button_new_category.gif', IMAGE_NEW_CATEGORY) . '</a>&nbsp;<a href="' . tep_href_link('product_edit.php', 'cPath=' . $cPath . '&action=new_product') . '">' . tep_image_button('button_new_product.gif', IMAGE_NEW_PRODUCT) . '</a>&nbsp;'; if(isset($_GET['search'])){echo '<a href="' . tep_href_link('categories.php', 'search=' . $_GET['search'] . '&action=beginsort') . '">' . tep_image_button('button_sort_order.gif', IMAGE_SORT_ORDER) . '</a>&nbsp;' ;} else { echo '<a href="' . tep_href_link('categories.php', 'cPath=' . $cPath . '&action=beginsort') . '">' . tep_image_button('button_sort_order.gif', IMAGE_SORT_ORDER) . '</a>&nbsp;';  } if(isset($_GET['search'])){} else { echo tep_draw_button('Batch Products', '', tep_href_link('products_multi.php', 'cPath=' . $cPath), 'primary') . '</a>&nbsp;</td>';  }}
                    ?>
                 <td>           <style>
        .pill-switch {
            float: right;
            width: 30px;
            height: 15px;
            background-color: #ddd;
            border-radius: 7px;
            position: relative;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .pill-switch:before {
            content: '';
            width: 15px;
            height: 15px;
            background-color: #fff;
            border-radius: 50%;
            position: absolute;
            top: 0;
            left: 0;
            transition: left 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .pill-switch.on {
            background-color: #5cb85c;
        }

        .pill-switch.on:before {
            left: 15px;
        }
    </style>



    <div><span>hide disabled: </span><div id="pillSwitch" class="pill-switch"></div></div>


    <script>
    $(document).ready(function() {
        $(document).ready(function() {
        // Check localStorage for the switch state
        if (localStorage.getItem('pillSwitchState') === 'on') {
            $('#pillSwitch').addClass('on');
            $('tr[data-status="0"]').hide();
        }

        $('#pillSwitch').click(function() {
            $(this).toggleClass('on');
            if ($(this).hasClass('on')) {
                $('tr[data-status="0"]').hide();
                localStorage.setItem('pillSwitchState', 'on'); // Save state as 'on'
            } else {
                $('tr[data-status="0"]').show();
                localStorage.setItem('pillSwitchState', 'off'); // Save state as 'off'
            }
        });
    });
    });
</script></td>
                </tr></tbody>
                </table></td>
              </tr>
            </table></form></td>
<!--end sort order//-->
			
<?php
    $heading  = array();
    $contents = array();
    switch ($action) {
        case 'new_category':
            $heading[] = array(
                'text' => '<strong>' . TEXT_INFO_HEADING_NEW_CATEGORY . '</strong>'
            );
            
            
            $contents   = array(
                'form' => tep_draw_form('newcategory', 'categories.php', 'action=insert_category&cPath=' . $cPath, 'post', 'enctype="multipart/form-data"')
            );
            $contents[] = array(
                'text' => TEXT_NEW_CATEGORY_INTRO
            );
            
            $category_inputs_string = $category_description_string = $category_seo_description_string = $category_seo_keywords_string = $category_seo_title_string = '';
            $languages              = tep_get_languages();
            for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                $category_inputs_string .= '<br />' . tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']) . '&nbsp;' . tep_draw_input_field('categories_name[' . $languages[$i]['id'] . ']');
                $category_description_string .= '<br />' . tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name'], '', '', 'style="vertical-align: top;"') . '&nbsp;' . tep_draw_textarea_field('categories_description[' . $languages[$i]['id'] . ']', 'soft', '80', '10');
                $category_seo_description_string .= '<br />' . tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name'], '', '', 'style="vertical-align: top;"') . '&nbsp;' . tep_draw_textarea_field('categories_seo_description[' . $languages[$i]['id'] . ']', 'soft', '80', '10');
                $category_seo_keywords_string .= '<br />' . tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']) . '&nbsp;' . tep_draw_input_field('categories_seo_keywords[' . $languages[$i]['id'] . ']', NULL, 'style="width: 300px;" placeholder="' . PLACEHOLDER_COMMA_SEPARATION . '"');
                $category_seo_title_string .= '<br />' . tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']) . '&nbsp;' . tep_draw_input_field('categories_seo_title[' . $languages[$i]['id'] . ']');
            }
            
            $contents[] = array(
                'text' => '<br />' . TEXT_CATEGORIES_NAME . $category_inputs_string
            );
            $contents[] = array(
                'text' => '<br />' . TEXT_CATEGORIES_SEO_TITLE . $category_seo_title_string
            );
            $contents[] = array(
                'text' => '<br />' . TEXT_CATEGORIES_DESCRIPTION . $category_description_string
            );
            $contents[] = array(
                'text' => '<br />' . TEXT_CATEGORIES_SEO_DESCRIPTION . $category_seo_description_string
            );
            $contents[] = array(
                'text' => '<br />' . TEXT_CATEGORIES_SEO_KEYWORDS . $category_seo_keywords_string
            );
            $contents[] = array(
                'text' => '<br />' . TEXT_CATEGORIES_IMAGE . '<br />' . tep_draw_file_field('categories_image')
            );
            $contents[] = array(
                'text' => '<br />' . TEXT_SORT_ORDER . '<br />' . tep_draw_input_field('sort_order', '', 'size="2"')
            );
            $contents[] = array(
                'align' => 'center',
                'text' => '<br />' . tep_draw_button(IMAGE_SAVE, 'disk', null, 'primary') . tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('categories.php', 'cPath=' . $cPath))
            );
            break;
        case 'edit_category':
            /* Mark Google category functions
            $catTempDes[$row->prodCatID];
            $catTempPar[$row->prodCatID];
            $catGoog[$row->prodCatID];
            */
            
            $heading[] = array(
                'text' => '<strong>' . TEXT_INFO_HEADING_EDIT_CATEGORY . '</strong>'
            );
            
            
            $contents   = array(
                'form' => tep_draw_form('categories', 'categories.php', 'action=update_category&cPath=' . $cPath, 'post', 'enctype="multipart/form-data"') . tep_draw_hidden_field('categories_id', $cInfo->categories_id)
            );
            $contents[] = array(
                'text' => TEXT_EDIT_INTRO
            );
            
            $category_inputs_string = $category_description_string = $category_seo_description_string = $category_seo_keywords_string = $category_seo_title_string = '';
            $languages              = tep_get_languages();
            for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                $category_inputs_string .= '<br />' . tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']) . '&nbsp;' . tep_draw_input_field('categories_name[' . $languages[$i]['id'] . ']', tep_get_category_name($cInfo->categories_id, $languages[$i]['id']));
                $category_seo_title_string .= '<br />' . tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']) . '&nbsp;' . tep_draw_input_field('categories_seo_title[' . $languages[$i]['id'] . ']', tep_get_category_seo_title($cInfo->categories_id, $languages[$i]['id']));
                $category_description_string .= '<br />' . tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name'], '', '', 'style="vertical-align: top;"') . '&nbsp;' . tep_draw_textarea_field('categories_description[' . $languages[$i]['id'] . ']', 'soft', '80', '10', tep_get_category_description($cInfo->categories_id, $languages[$i]['id']));
                $category_seo_description_string .= '<br />' . tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name'], '', '', 'style="vertical-align: top;"') . '&nbsp;' . tep_draw_textarea_field('categories_seo_description[' . $languages[$i]['id'] . ']', 'soft', '80', '10', tep_get_category_seo_description($cInfo->categories_id, $languages[$i]['id']));
                $category_seo_keywords_string .= '<br />' . tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']) . '&nbsp;' . tep_draw_input_field('categories_seo_keywords[' . $languages[$i]['id'] . ']', tep_get_category_seo_keywords($cInfo->categories_id, $languages[$i]['id']), 'style="width: 300px;" placeholder="' . PLACEHOLDER_COMMA_SEPARATION . '"');
            }
            
            $contents[]               = array(
                'text' => '<br />' . TEXT_EDIT_CATEGORIES_NAME . $category_inputs_string
            );
            $contents[]               = array(
                'text' => '<br />' . TEXT_EDIT_CATEGORIES_SEO_TITLE . $category_seo_title_string
            );
            $contents[]               = array(
                'text' => '<br />' . TEXT_EDIT_CATEGORIES_DESCRIPTION . $category_description_string
            );
            $contents[]               = array(
                'text' => '<br />' . TEXT_EDIT_CATEGORIES_SEO_DESCRIPTION . $category_seo_description_string
            );
            $contents[]               = array(
                'text' => '<br />' . TEXT_EDIT_CATEGORIES_SEO_KEYWORDS . $category_seo_keywords_string
            );
            $contents[]               = array(
                'text' => '<br />' . tep_image(HTTP_CATALOG_SERVER . DIR_WS_CATALOG_IMAGES . $cInfo->categories_image, $cInfo->categories_name) . '<br />' . DIR_WS_CATALOG_IMAGES . '<br /><strong>' . $cInfo->categories_image . '</strong>'
            );
            $contents[]               = array(
                'text' => '<br />' . TEXT_EDIT_CATEGORIES_IMAGE . '<br />' . tep_draw_file_field('categories_image')
            );
            $contents[]               = array(
                'text' => '<br />' . TEXT_EDIT_SORT_ORDER . '<br />' . tep_draw_input_field('sort_order', $cInfo->sort_order, 'size="2"')
            );
            //start mark google category code 
            $contents[]               = array(
                'text' => '<br />Google feed category (see <a href="http://support.google.com/merchants/bin/answer.py?hl=en&answer=1705911" target="_blank">here</a>)<br />Note:The first category should have the default categories up to the baseline and all categories below should be just one category' . tep_draw_input_field('google_category', $cInfo->google_category, 'size="50"')
            );
            $google_category_baseline = '';
            if ($cInfo->google_category_baseline == 1) {
                $google_category_baseline = 'checked';
            };
            $contents[] = array(
                'text' => '<br />New Baseline<br />Note: Only tick this box if you need to override a parent category baseline (root categories ignore this field as it is required in that case. ' . $cInfo->google_category_baseline . ' ' . tep_draw_checkbox_field('google_category_baseline', 1, $google_category_baseline)
            );
            
            /*echo 'bf $catTempPar[663]=' . $catTempPar[663] . '<br>';
            echo 'bf $catTempPar[473]=' . $catTempPar[473] . '<br>';*/
            //     'text' => '<br />Current Calculated Category: ' . generateGoogleCategory($cInfo->categories_id, $cInfo->google_category_baseline, $cInfo->parent_id, $catTempBase, $catGoog, $catTempPar)
           	echo '<br>generateGoogleCategory: ' . $cInfo->categories_id . ' ' . generateGoogleCategory($cInfo->categories_id,null,null,null,0) . '<br>';
            $contents[] = array(
			                'text' => '<br />Current Calculated Category: ' . generateGoogleCategory($cInfo->categories_id,null,null,null,1)
            );
            //end google category code
            $contents[] = array(
                'align' => 'center',
                'text' => '<br />' . tep_draw_button(IMAGE_SAVE, 'disk', null, 'primary') . tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('categories.php', 'cPath=' . $cPath . '&cID=' . $cInfo->categories_id))
            );
            break;
        case 'delete_category':
            $heading[] = array(
                'text' => '<strong>' . TEXT_INFO_HEADING_DELETE_CATEGORY . '</strong>'
            );
            
            $contents   = array(
                'form' => tep_draw_form('categories', 'categories.php', 'action=delete_category_confirm&cPath=' . $cPath) . tep_draw_hidden_field('categories_id', $cInfo->categories_id)
            );
            $contents[] = array(
                'text' => TEXT_DELETE_CATEGORY_INTRO
            );
            $contents[] = array(
                'text' => '<br /><strong>' . $cInfo->categories_name . '</strong>'
            );
            if ($cInfo->childs_count > 0)
                $contents[] = array(
                    'text' => '<br />' . sprintf(TEXT_DELETE_WARNING_CHILDS, $cInfo->childs_count)
                );
            if ($cInfo->products_count > 0)
                $contents[] = array(
                    'text' => '<br />' . sprintf(TEXT_DELETE_WARNING_PRODUCTS, $cInfo->products_count)
                );
            $contents[] = array(
                'align' => 'center',
                'text' => '<br />' . tep_draw_button(IMAGE_DELETE, 'trash', null, 'primary') . tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('categories.php', 'cPath=' . $cPath . '&cID=' . $cInfo->categories_id))
            );
            break;
        case 'move_category':
            $heading[] = array(
                'text' => '<strong>' . TEXT_INFO_HEADING_MOVE_CATEGORY . '</strong>'
            );
            
            
            $contents   = array(
                'form' => tep_draw_form('categories', 'categories.php', 'action=move_category_confirm&cPath=' . $cPath) . tep_draw_hidden_field('categories_id', $cInfo->categories_id)
            );
            $contents[] = array(
                'text' => sprintf(TEXT_MOVE_CATEGORIES_INTRO, $cInfo->categories_name)
            );
            $contents[] = array(
                'text' => '<br />' . sprintf(TEXT_MOVE, $cInfo->categories_name) . '<br />' . tep_draw_pull_down_menu('move_to_category_id', tep_get_category_tree(), $current_category_id)
            );
            $contents[] = array(
                'align' => 'center',
                'text' => '<br />' . tep_draw_button(IMAGE_MOVE, 'arrow-4', null, 'primary') . tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('categories.php', 'cPath=' . $cPath . '&cID=' . $cInfo->categories_id))
            );
            break;
        case 'delete_product':
            $heading[] = array(
                'text' => '<strong>' . TEXT_INFO_HEADING_DELETE_PRODUCT . '</strong>'
            );
            
            
            $contents   = array(
                'form' => tep_draw_form('products', 'categories.php', 'action=delete_product_confirm&cPath=' . $cPath) . tep_draw_hidden_field('products_id', $pInfo->products_id)
            );
            $contents[] = array(
                'text' => TEXT_DELETE_PRODUCT_INTRO
            );
            $contents[] = array(
                'text' => '<br /><strong>' . $pInfo->products_name . '</strong>'
            );
            
            $product_categories_string = '';
            $product_categories        = tep_generate_category_path($pInfo->products_id, 'product');
            for ($i = 0, $n = sizeof($product_categories); $i < $n; $i++) {
                $category_path = '';
                for ($j = 0, $k = sizeof($product_categories[$i]); $j < $k; $j++) {
                    $category_path .= $product_categories[$i][$j]['text'] . '&nbsp;&gt;&nbsp;';
                }
                $category_path = substr($category_path, 0, -16);
                $product_categories_string .= tep_draw_checkbox_field('product_categories[]', $product_categories[$i][sizeof($product_categories[$i]) - 1]['id'], true) . '&nbsp;' . $category_path . '<br />';
            }
            $product_categories_string = substr($product_categories_string, 0, -4);
            
            $contents[] = array(
                'text' => '<br />' . $product_categories_string
            );
            $contents[] = array(
                'align' => 'center',
                'text' => '<br />' . tep_draw_button(IMAGE_DELETE, 'trash', null, 'primary') . tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $pInfo->products_id))
            );
            break;
        case 'move_product':
            $heading[] = array(
                'text' => '<strong>' . TEXT_INFO_HEADING_MOVE_PRODUCT . '</strong>'
            );
            
            
            $contents   = array(
                'form' => tep_draw_form('products', 'categories.php', 'action=move_product_confirm&cPath=' . $cPath) . tep_draw_hidden_field('products_id', $pInfo->products_id)
            );
            $contents[] = array(
                'text' => sprintf(TEXT_MOVE_PRODUCTS_INTRO, $pInfo->products_name)
            );
            $contents[] = array(
                'text' => '<br />' . TEXT_INFO_CURRENT_CATEGORIES . '<br /><strong>' . tep_output_generated_category_path($pInfo->products_id, 'product') . '</strong>'
            );
            $contents[] = array(
                'text' => '<br />' . sprintf(TEXT_MOVE, $pInfo->products_name) . '<br />' . tep_draw_pull_down_menu('move_to_category_id', tep_get_category_tree(), $current_category_id)
            );
            $contents[] = array(
                'align' => 'center',
                'text' => '<br />' . tep_draw_button(IMAGE_MOVE, 'arrow-4', null, 'primary') . tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $pInfo->products_id))
            );
            break;
        case 'copy_to':
            $heading[] = array(
                'text' => '<strong>' . TEXT_INFO_HEADING_COPY_TO . '</strong>'
            );
            
            
            $contents   = array(
                'form' => tep_draw_form('copy_to', 'categories.php', 'action=copy_to_confirm&cPath=' . $cPath) . tep_draw_hidden_field('products_id', $pInfo->products_id)
            );
            $contents[] = array(
                'text' => TEXT_INFO_COPY_TO_INTRO
            );
            //Mark code to out put current cegories product is in
            $contents[] = array(
                'text' => 'Product Id: ' . $pInfo->products_id . '<br />' . TEXT_INFO_CURRENT_CATEGORIES . '<br /><strong>' . tep_output_generated_category_path($pInfo->products_id, 'product') . '</strong>'
            );
            $contents[] = array(
                'text' => '<br />' . TEXT_CATEGORIES . '<br />' . tep_draw_pull_down_menu('categories_id', tep_get_category_tree(), $current_category_id)
            );
            
            $options_output =   '<div id="linkWithAttributesOptions" style="clear:both;display:none;">';
            $products_options_name_query = tep_db_query("select distinct popt.products_options_id, popt.products_options_name from products_options popt, products_attributes patrib where patrib.products_id='" . (int)$_GET['pID'] . "' and patrib.options_id = popt.products_options_id and popt.language_id = '" . (int)$languages_id . "' order by popt.products_options_name");
            $hasAttributes = 0;
            if (tep_db_num_rows($products_options_name_query)) {
                while ($products_options_name = tep_db_fetch_array($products_options_name_query)) {
                    $products_options_array = array();
/*
                    $fr_input = $fr_required = $fr_feedback = null;
                    //if (MODULE_CONTENT_PI_OA_ENFORCE == 'True') {
                        $fr_input    = FORM_REQUIRED_INPUT;
                        $fr_required = 'required aria-required="true" '; 
                        $fr_feedback = ' has-feedback';
                    //}
                    //if (MODULE_CONTENT_PI_OA_HELPER == 'True') {
                        $products_options_array[] = array('id' => '', 'text' => MODULE_CONTENT_PI_OA_ENFORCE_SELECTION);            
                    //}*/

                    $products_options_query = tep_db_query("select pov.products_options_values_id, pov.products_options_values_name, pa.options_values_price, pa.price_prefix from products_attributes pa, products_options_values pov where pa.products_id = '" . (int)$_GET['pID'] . "' and pa.options_id = '" . (int)$products_options_name['products_options_id'] . "' and pa.options_values_id = pov.products_options_values_id and pov.language_id = '" . (int)$languages_id . "'");

                    if (is_string($_GET['pID']) && isset($cart->contents[$_GET['pID']]['attributes'][$products_options_name['products_options_id']])) {
                        $selected_attribute = $cart->contents[$_GET['pID']]['attributes'][$products_options_name['products_options_id']];
                    } else {
                        $selected_attribute = false;
                    }
                    $options_output .= '<div class="form-group' . $fr_feedback . '">
                        <label for="input_' . $products_options_name['products_options_id'] . '" class="control-label col-sm-3">' . $products_options_name['products_options_name'] . '</label>
                            <div class="col-sm-9 attributeWrapper">
                                <select name="id[' . $products_options_name['products_options_id'] . ']" data-productid="' . $_GET['pID'] . '" data-optionsid="' . $products_options_name['products_options_id'] . '" id="input_' . $products_options_name['products_options_id'] . '" class="form-control attributeSelect">';	
                
                    while ($products_options = tep_db_fetch_array($products_options_query)) {
                        $optionsPrice = $currencies->display_price($products_options['options_values_price'], tep_get_tax_rate($product_info['products_tax_class_id']));
                        $options_output .= '<option value="'. $products_options['products_options_values_id'] . '" data-productId="' . $_GET['pID'] . '" data-priceIncrease="'. $products_options['options_values_price'] . '">' . $products_options['products_options_values_name'];
                        if ($products_options['options_values_price'] != '0') {
                            $options_output .= ' (' . $products_options['price_prefix'] . $optionsPrice .') ';
                        }
                        $options_output .= '</option>';
                    }
                    $options_output .=   "</select>";  
					$options_output .= "</div></div>";                    
                }	
                $hasAttributes = 1;
				$options_output .=   "<label for='attribPartNumber' class='control-label col-sm-3'>Part Number</label><div class='col-sm-9'>" . tep_draw_input_field('attribPartNumber',$pInfo->products_model) . "</div><br /></div>";
                    
				$options_output .= "</div><script>					
						$(document).ready(function(e) {		
							$(\"input[name='copy_as']\").on('change',function(e) {
								if( $('#link_attributes').is(':checked') ){
									$('#linkWithAttributesOptions').show();
								} else {
									$('#linkWithAttributesOptions').hide();
								}
							});
						});
						</script>
					";
            } 
	          //$name, $value = '', $parameters = '', $required = false, $type = 'text', $reinsert_value = true) 
			  
            /*$linkWithAttributes = '';
            if ($hasAttributes){
                $linkWithAttributes = tep_draw_radio_field('copy_as', 'link_attributes', false,null, 'id="link_attributes"') . ' Link with attributes' . '<br />' 
                 . $options_output . '<br />';
            }*/
            
            $contents[] = array(
                'text' => '<br />' 
                 . TEXT_HOW_TO_COPY . '<br />' 
                 . tep_draw_radio_field('copy_as', 'link', true) . ' ' 
                 . TEXT_COPY_AS_LINK . '<br />' 
                 . $linkWithAttributes
                 . '<div style="clear:both">' . tep_draw_radio_field('copy_as', 'duplicate')  . ' ' . TEXT_COPY_AS_DUPLICATE . '</div>'
            );
            $contents[] = array(
                'align' => 'center',
                'text' => '<br />' . tep_draw_button(IMAGE_COPY, 'copy', null, 'primary') . tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $pInfo->products_id))
            );
            break;
        default:
            if ($rows > 0) {
                if (isset($cInfo) && is_object($cInfo)) { // category info box contents
                    $category_path_string = '';
                    $category_path        = tep_generate_category_path($cInfo->categories_id);
                    for ($i = (sizeof($category_path[0]) - 1); $i > 0; $i--) {
                        $category_path_string .= $category_path[0][$i]['id'] . '_';
                    }
                    $category_path_string = substr($category_path_string, 0, -1);
                    
                    $heading[] = array(
                        'text' => '<strong>' . $cInfo->categories_name . '</strong>'
                    );
                    
                    
                    $contents[] = array(
                        'align' => 'center',
                        'text' => tep_draw_button(IMAGE_EDIT, 'document', tep_href_link('categories.php', 'cPath=' . $category_path_string . '&cID=' . $cInfo->categories_id . '&action=edit_category')) . tep_draw_button(IMAGE_DELETE, 'trash', tep_href_link('categories.php', 'cPath=' . $category_path_string . '&cID=' . $cInfo->categories_id . '&action=delete_category')) . tep_draw_button(IMAGE_MOVE, 'arrow-4', tep_href_link('categories.php', 'cPath=' . $category_path_string . '&cID=' . $cInfo->categories_id . '&action=move_category'))
                    );
                    $contents[] = array(
                        'text' => '<br />' . TEXT_DATE_ADDED . ' ' . tep_date_short($cInfo->date_added)
                    );
                    if (@tep_not_null($cInfo->last_modified))
                        $contents[] = array(
                            'text' => TEXT_LAST_MODIFIED . ' ' . tep_date_short($cInfo->last_modified)
                        );
                    $contents[] = array(
                        'text' => '<br />' . tep_info_image($cInfo->categories_image, $cInfo->categories_name, HEADING_IMAGE_WIDTH, HEADING_IMAGE_HEIGHT) . '<br />' . $cInfo->categories_image
                    );
                    $contents[] = array(
                        'text' => '<br />' . TEXT_SUBCATEGORIES . ' ' . $cInfo->childs_count . '<br />' . TEXT_PRODUCTS . ' ' . $cInfo->products_count
                    );
                } elseif (isset($pInfo) && is_object($pInfo)) { // product info box contents
                    $heading[] = array(
                        'text' => '<strong>' . tep_get_products_name($pInfo->products_id, $languages_id) . '</strong>'
                    );
                    
                    
                    $contents[] = array(
                        'align' => 'center',
                        'text' => tep_draw_button(IMAGE_EDIT, 'document', tep_href_link('product_edit.php', 'cPath=' . $cPath . '&pID=' . $pInfo->products_id . '&action=new_product')) . tep_draw_button(IMAGE_DELETE, 'trash', tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $pInfo->products_id . '&action=delete_product')) . tep_draw_button(IMAGE_MOVE, 'arrow-4', tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $pInfo->products_id . '&action=move_product')) . tep_draw_button(IMAGE_COPY_TO, 'copy', tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $pInfo->products_id . '&action=copy_to'))
                    );
                    $contents[] = array(
                        'text' => '<br />' . TEXT_DATE_ADDED . ' ' . tep_date_short($pInfo->products_date_added)
                    );
                    if (@tep_not_null($pInfo->products_last_modified))
                        $contents[] = array(
                            'text' => TEXT_LAST_MODIFIED . ' ' . tep_date_short($pInfo->products_last_modified)
                        );
                    if (date('Y-m-d') < $pInfo->products_date_available)
                        $contents[] = array(
                            'text' => TEXT_DATE_AVAILABLE . ' ' . tep_date_short($pInfo->products_date_available)
                        );
                    $contents[] = array(
                        'text' => '<br />' . tep_info_image($pInfo->products_image, $pInfo->products_name, SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT) . '<br />' . $pInfo->products_image
                    );
                    $contents[] = array(
                        'text' => '<br />' . TEXT_PRODUCTS_PRICE_INFO . ' ' . $currencies->format($pInfo->products_price) . '<br />' . TEXT_PRODUCTS_QUANTITY_INFO . ' ' . $pInfo->products_quantity
                    );
                    $contents[] = array(
                        'text' => '<br />' . TEXT_PRODUCTS_AVERAGE_RATING . ' ' . number_format($pInfo->average_rating, 2) . '%'
                    );
                    $contents[] = array(
                        'text' => '<br />Current Calculated Category:' . $currGoogleCategory
                    );
                    $contents[] = array(
                        'text' => 'Product Id: ' . $pInfo->products_id . '<br />' . TEXT_INFO_CURRENT_CATEGORIES . '<br /><strong>' . tep_output_generated_category_path_linked($pInfo->products_id, 'product', 1) . '</strong>'
                    );
                    
                }
            } else { // create category/product info
                $heading[] = array(
                    'text' => '<strong>' . EMPTY_CATEGORY . '</strong>'
                );
                
                
                $contents[] = array(
                    'text' => TEXT_NO_CHILD_CATEGORIES_OR_PRODUCTS
                );
            }
            break;
    }
    
    if ((@tep_not_null($heading)) && (@tep_not_null($contents))) {
        echo '            <td width="30%" valign="top">' . "\n";
        
        $box = new box;
        echo $box->infoBox($heading, $contents);
        
        echo '            </td>' . "\n";
    } ?>
         </tr>
        </table></td>
      </tr>
    </table>
<?php
}


require('includes/template_bottom.php');
require('includes/application_bottom.php'); ?>