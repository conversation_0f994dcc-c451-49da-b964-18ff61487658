<?php

use GuzzleHttp\Client;

use GuzzleHttp\Exception\RequestException;


//use function PHPSTORM_META\type;



define('ADWS_CLIENT_ID', 'yVuCvVbvOuFUr2QBWxv2gNjYfDQQqX1I');
// old F8n4XeeOS4nyXUJmlS1LJ9Z28qPlAt39
define('ADWS_CLIENT_SECRET', 'mIB07tfpksrAhxrA');
// $clientSecret = 'GG9bqyaKINbpscPN';

define('ADWS_CALLBACK_URL', 'www.cadservices.co.uk/adwsapi.php');
define('ADWS_CSN', '5103159758');
define('ADWS_BASE_URL', 'https://enterprise-api.autodesk.com');

define('ADWS_TOKEN_ENDPOINT', ADWS_BASE_URL  . '/v2/oauth/generateaccesstoken');
// $tokenEndpoint = '/v2/oauth/generateaccesstoken';



class AutodeskAPI {


    public AutodeskProducts $products;
    public AutodeskQuote $quote;
    public AutodeskQuotes $quotes;
    public AutodeskSubscription $subscription;
    public AutodeskSubscriptions $subscriptions;
    public AutodeskAuthenticator $auth;

    public AutodeskCustomers $customers;
    public AutodeskOrders $orders;
    public AutodeskCustomerSuccess $customerSuccess;
    public AutodeskAPIInterface $api;
    public bool $debug;

    public function __construct($debug = false) {
        $this->api = new AutodeskAPIInterface();


        $this->debug = $debug;
        $this->auth =            new AutodeskAuthenticator($this->api);
        $this->products =        new AutodeskProducts($this->api);
        $this->customers =       new AutodeskCustomers($this->api);
        $this->orders =          new AutodeskOrders($this->api);
        $this->quote =           new AutodeskQuote($this->api, $this->products, $this->customers);
        $this->quotes =          new AutodeskQuotes($this->api);
        $this->subscription =    new AutodeskSubscription($this->api);
        $this->subscriptions =   new AutodeskSubscriptions($this->api, $this->auth);
        $this->customerSuccess = new AutodeskCustomerSuccess($this->api);
    }




    public static function create_unqiue_hash($columns) {

        $mappedData = [];
        $concatString = implode('', $columns);
        /* Generate the hash and add it to the mapped data*/
        $uniqueHash = hash('crc32', $concatString);
        /* Prepare the SQL insert statement with ON DUPLICATE KEY UPDATE*/
        return $uniqueHash;
    }

    private function get_product_catalog_header_map() {
        return [
            'offeringName' => 'products_autodesk_catalog.offeringName',
            'offeringCode' => 'products_autodesk_catalog.offeringCode',
            'offeringId' => 'products_autodesk_catalog.offeringId',
            'intendedUsage.code' => 'products_autodesk_catalog.intendedUsage_code',
            'intendedUsage.description' => 'products_autodesk_catalog.intendedUsage_description',
            'accessModel.code' => 'products_autodesk_catalog.accessModel_code',
            'accessModel.description' => 'products_autodesk_catalog.accessModel_description',
            'servicePlan.code' => 'products_autodesk_catalog.servicePlan_code',
            'servicePlan.description' => 'products_autodesk_catalog.servicePlan_description',
            'connectivity.code' => 'products_autodesk_catalog.connectivity_code',
            'connectivity.description' => 'products_autodesk_catalog.connectivity_description',
            'term.code' => 'products_autodesk_catalog.term_code',
            'term.description' => 'products_autodesk_catalog.term_description',
            'lifeCycleState' => 'products_autodesk_catalog.lifeCycleState',
            'renewOnlyDate' => 'products_autodesk_catalog.renewOnlyDate',
            'discontinueDate' => 'products_autodesk_catalog.discontinueDate',
            'orderAction' => 'products_autodesk_catalog.orderAction',
            'specialProgramDiscount.code' => 'products_autodesk_catalog.specialProgramDiscount_code',
            'specialProgramDiscount.description' => 'products_autodesk_catalog.specialProgramDiscount_description',
            'fromQty' => 'products_autodesk_catalog.fromQty',
            'toQty' => 'products_autodesk_catalog.toQty',
            'currency' => 'products_autodesk_catalog.currency',
            'SRP' => 'products_autodesk_catalog.SRP',
            'costAfterSpecialProgramDiscount' => 'products_autodesk_catalog.costAfterSpecialProgramDiscount',
            'renewalDiscountPercent' => 'products_autodesk_catalog.renewalDiscountPercent',
            'renewalDiscountAmount' => 'products_autodesk_catalog.renewalDiscountAmount',
            'costAfterRenewalDiscount' => 'products_autodesk_catalog.costAfterRenewalDiscount',
            'transactionVolumeDiscountPercent' => 'products_autodesk_catalog.transactionVolumeDiscountPercent',
            'transactionVolumeDiscountAmount' => 'products_autodesk_catalog.transactionVolumeDiscountAmount',
            'costAfterTransactionVolumeDiscount' => 'products_autodesk_catalog.costAfterTransactionVolumeDiscount',
            'serviceDurationDiscountPercent' => 'products_autodesk_catalog.serviceDurationDiscountPercent',
            'serviceDurationDiscountAmount' => 'products_autodesk_catalog.serviceDurationDiscountAmount',
            'costAfterServiceDurationDiscount' => 'products_autodesk_catalog.costAfterServiceDurationDiscount',
            'effectiveStartDate' => 'products_autodesk_catalog.effectiveStartDate',
            'effectiveEndDate' => 'products_autodesk_catalog.effectiveEndDate',
            'unique_hash' => '<unique_hash>'
        ];
    }

    /**
     * Imports a CSV file into the database. The CSV file should have the same structure as the one provided by the Autodesk API.
     * The function will check that all required columns are present in the CSV and will exit if any are missing.
     * It will also generate a unique hash for each row and add it to the table.
     * The function will insert or update the data in the table based on the unique hash.
     * The function will return a JSON object with the number of inserted, updated and skipped lines as well as the reasons for each.
     * The function will also update the pricing data in the database.
     * @param array $mapping The mapping of CSV headers to table columns.
     *  format is: array('header_name' => 'table_name.table_column_name')
     * @param string $csv_file_path The path to the CSV file.
     * @return array A JSON object with the results of the import.
     */

    public static function import_csv_into_database($mapping, $csv_file_path, $unique_hash = [], $debug = false) {

        $pdo = tep_db_connect();
        $output = "";
        if (($handle = fopen($csv_file_path, "r")) !== FALSE) {
            $count = 0;
            $rows = array_map('str_getcsv', file($csv_file_path));
            $header = array_shift($rows);
            $csv = [];
            //print_rr($header, 'header', false);
            foreach ($rows as $row) {
                $csv = array_combine($header, $row);
                $query_array = [];
                $query_sql = "";
                $count++;
                // if ($debug && $count > 1) {
                //     print_rr($csv, 'csv', false);
                // }
                $insert_ids = [];
                $groupcount = 0;
                foreach ($mapping as $groupName => $groupData) {                    
                    $db_table = $groupData['table'];
                    foreach ($groupData['columns'] as $csv_col => $db_col) {
                        if (isset($csv[$csv_col]) && !empty($csv[$csv_col])) {
                            $query_array[$db_table][$db_col] = var_export($csv[$csv_col],true);
                        }
                    }
                    if (isset($groupData['extra']) && !empty($groupData['extra'])) {
                        foreach ($groupData['extra'] as $db_col => $xtra_col) {
                            $xtra_col_tags = preg_match('/<([^>]*)>([^<]*)/i', $xtra_col, $matches) ? $matches[1] : '';
                            switch ($xtra_col_tags) {
                                case 'unique_hash':
                                    $xtra_col = self::create_unqiue_hash(array_values($csv));
                                    break;
                                case 'group_insert_id':
                                    $xtra_col = $insert_ids[$matches[2]];
                            }
                            $query_array[$db_table][$db_col] = var_export($xtra_col,true);
                        }
                    }
                    
                    foreach ($query_array as $table => $columns) {
                        $groupcount++;
                        $set_strings = '';
                        foreach ($columns as $column => $value) {
                            $set_strings .= " {$column} = {$value},";
                        }
                        $set_strings = substr($set_strings, 0, -1);
                        $query_sql = "INSERT INTO {$table} SET $set_strings ON DUPLICATE KEY UPDATE $set_strings;";
                        $query[$count][$groupName] = tep_db_affected_rows(tep_db_query($query_sql, null, [], $pdo));
                        $insert_ids[$groupName] = tep_db_insert_id($pdo);
                    }
                }

                if ($debug) { 
                    $result = '';
                    foreach ($query[$count] as $groupName => $groupData) {
                        $result .= "{$groupName}: 1 ";
                        $result .= match ($query[$count][$groupName]) {
                            1 => 'inserted, ',
                            2 => 'updated, ',
                            default => 'skipped, '
                        };
                    }
                    
                    
                    $output .= "Line {$count}: {$result}<br>";
                }
            }
            fclose($handle);
        } else {
            die(json_encode(['error' => "Could not open CSV file."]));
        }

        tep_db_close(); /* Output the results in JSON format*/
        return ['message' => 'CSV data import completed.',  'response' => $output];
    }
}












class AutodeskAuthenticator {
    private $client_id;
    private $client_secret;
    private $token_endpoint;
    private $accessToken;
    private $httpClient;
    private $csn;
    private $callback_url;
    private $debug = false;
    public $debugLog = [];
    public AutodeskAPIInterface $api;

    public function __construct(
        $api = null,
        $debug = false
    ) {
        $this->client_id = ADWS_CLIENT_ID;
        $this->client_secret = ADWS_CLIENT_SECRET;
        $this->token_endpoint = ADWS_TOKEN_ENDPOINT;
        $this->csn = ADWS_CSN;
        $this->callback_url = ADWS_CALLBACK_URL;
        $this->httpClient = new Client();
        $this->debug = $debug;
        $this->debugLog = [];
    }

    public function authenticate() {

        /*
        * Create Signature
        */

        $time_stamp = strtotime("now");
        $base_str = $this->callback_url . $this->client_id . $time_stamp;
        $hmacsha256 = hash_hmac('sha256', $base_str, $this->client_secret, true);
        $signature = base64_encode($hmacsha256);

        /* 
        * Create Authorization
        */

        $base_64_message = $this->client_id . ":" . $this->client_secret;

        $base_64_encoded = base64_encode($base_64_message);


        $headers = [
            "Authorization" => "Basic " . $base_64_encoded,
            "cache-control" => "no-cache",
            "signature" => $signature,
            "CSN" => $this->csn,
            "timestamp" => $time_stamp
        ];

        $query = [
            "grant_type" => "client_credentials"
        ];


        $this->debugLog = [
            "Headers" => $headers,
            "Form Params" => $query
        ];


        if ($this->debug) return;

        try {
            $response = $this->httpClient->post($this->token_endpoint, [
                'headers' => $headers,
                'query' => $query
            ]);


            $responseBody = json_decode($response->getBody(), true);


            if (isset($responseBody['access_token'])) {
                $this->accessToken = $responseBody['access_token'];
            } else {
                throw new Exception('Authentication Failed: ' . $responseBody['error']);
            }
        } catch (RequestException $e) {
            throw new Exception('Request Error: ' . $e->getMessage());
        }
    }

    public function getAccessToken() {
        if (!$this->accessToken) {
            $this->authenticate();
        }
        return $this->accessToken;
    }

    public function getSecret() {
        return $this->client_secret;
    }

    public function generateSignature($callbackUrl, $accessToken, $timestamp) {
        $message = $callbackUrl . $accessToken . $timestamp;
        return base64_encode(hash_hmac('sha256', $message, $this->getSecret(), true));
    }

    function decryptFile($encryptedFilePath, $decryptedFilePath, $password) {
        // Temporary path for the decrypted .zip file
        $tempZipFile = sys_get_temp_dir() . '/temp_decrypted_file.zip';

        // Step 1: Decrypt the encrypted file to a temporary .zip file
        if (!file_exists($encryptedFilePath)) {
            throw new Exception("Encrypted file not found: " . $encryptedFilePath);
        }

        $command = sprintf(
            'openssl enc -aes-256-cbc -md sha512 -d -in %s -out %s -k %s',
            escapeshellarg($encryptedFilePath),
            escapeshellarg($tempZipFile),
            escapeshellarg($password)
        );

        exec($command, $output, $returnVar);

        if ($returnVar !== 0) {
            throw new Exception("Decryption failed: " . implode("\n", $output));
        }

        // Step 2: Extract the .zip file and rename the extracted CSV file
        $zip = new ZipArchive;
        if ($zip->open($tempZipFile) === TRUE) {
            // Assuming there is only one file in the ZIP archive
            $zip->extractTo(sys_get_temp_dir());
            $extractedFileName = $zip->getNameIndex(0); // Get the first file's name
            $zip->close();

            // Rename the extracted file to $decryptedFilePath
            $extractedFilePath = sys_get_temp_dir() . '/' . $extractedFileName;
            if (!rename($extractedFilePath, $decryptedFilePath)) {
                throw new Exception("Failed to rename extracted file.");
            }

            // Remove the temporary zip file
            unlink($tempZipFile);
        } else {
            throw new Exception("Failed to open decrypted ZIP file.");
        }

        return $decryptedFilePath;
    }

    /* Usage
try {
    $encryptedFile = '/path/to/your/file.csv.zip.enc';
    $decryptedFile = '/path/to/your/decrypted_file.csv.zip';
    $password = 'your_password_here'; // Set the password from the API response

    decryptFile($encryptedFile, $decryptedFile, $password);
    echo "File decrypted successfully.";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
*/
}

class AutodeskAPIInterface {

    private AutodeskAuthenticator $auth;
    private $httpClient;
    private $debug;
    private $csn;

    public $debugLog;
    public $current_quotes;
    public function __construct() {
        $this->auth = new AutodeskAuthenticator();
        $this->httpClient = new Client();
        $this->csn = ADWS_CSN;
    }

    private function call($connection, $cache = true, $cacheTTL = 3600, $downloadDir = null) {
        // Generate a unique cache key using the serialized connection array
        $cacheKey = md5(serialize($connection));

        // History container to track the request/response history
        $history = [];
        $historyMiddleware = \GuzzleHttp\Middleware::history($history);
        $stack = \GuzzleHttp\HandlerStack::create();
        $stack->push($historyMiddleware);

        // Setup HTTP client with history middleware and redirect options
        $this->httpClient = new \GuzzleHttp\Client([
            'handler' => $stack,
            'allow_redirects' => true,  // Ensure redirects are followed
        ]);

        // Proceed with the API call if no cached response exists
        $endpoint = ADWS_BASE_URL . $connection['endpoint'];
        $method = $connection['method'];
        $timestamp = time();
        $accessToken = $this->auth->getAccessToken();
        $signature = $this->auth->generateSignature(ADWS_CALLBACK_URL, $accessToken, $timestamp);
        $Authorization = "Bearer {$accessToken}";
        $api = substr(str_replace('/', '_', $connection['endpoint']), 1);
        $headers = ['Authorization' => $Authorization, 'timestamp' => $timestamp, 'signature' => $signature, 'CSN' => $this->csn];

        if (isset($connection['headers'])) {
            foreach ($connection['headers'] as $key => $value) {
                $headers[$key] = $value;
            }
        }

        if (isset($connection['Content-Type'])) $headers['Content-Type'] = $connection['Content-Type'];
        $data['headers'] = $headers;
        if (isset($connection['query'])) $data["query"] = $connection['query'];
        if (isset($connection['json'])) $data['json'] = $connection['json'];

        // Debug logging (optional)
        $this->debugLog['log'][$api] = [
            "Headers" => $headers,
            "method" => $method,
            "endpoint" => $endpoint,
            "data" => $data
        ];

        if ($this->debug) return;

        try {
            // Execute the API request

            $response = $this->httpClient->request($method, $endpoint, $data);

            $this->debugLog['log'][$api]['response'] = [
                "status" => $response->getStatusCode(),
                "headers" => $response->getHeaders(),
                "body" => $response->getBody()->getContents()
            ];




            // Retrieve the effective URL using the history
            $effectiveUrl = null;
            if (!empty($history)) {
                $lastRequest = end($history)['request'];
                $effectiveUrl = (string) $lastRequest->getUri(); // This is the effective URL
            }

            // Extract the filename from the URL if downloading a file
            if ($downloadDir && $effectiveUrl) {
                // Extract the filename from the effective URL
                $urlPath = parse_url($effectiveUrl, PHP_URL_PATH);
                $filename = basename($urlPath);

                // If no filename in the URL, use a default name
                if (!$filename) {
                    $filename = 'downloaded_file_' . time();
                }

                // Create full path for saving the file
                $downloadFilePath =  rtrim(DIR_FS_CATALOG, '/')  . '/' . rtrim($downloadDir, '/') . '/' . $filename;

                // Download the file to the specified directory
                $this->httpClient->request($method, $effectiveUrl, [
                    'sink' => $downloadFilePath // Download file to the specified path
                ]);

                return ['status' => 'success', 'message' => 'File downloaded successfully', 'file_path' => $downloadFilePath];
            }

            $responseBody = json_decode($response->getBody(), true);
            $responseHeaders = $response->getHeaders();

            // Cache the response if caching is enabled
            if ($cache) {
                $this->cacheResponse($cacheKey, $responseBody, $cacheTTL);
            }

            return ['status' => 'success', 'headers' => $responseHeaders, 'body' => $responseBody, 'effective_url' => $effectiveUrl];
        } catch (RequestException $e) {
            $this->debugLog['log'][$api]['error'] = $e->getresponse()->getBody()->getContents();
            return ['status' => 'fail', 'failing' => 'api_call', 'response' => $e->getMessage(), 'debug' => $this->debugLog];
        }
    }



    private function database_getCachedResponse($cacheKey) {
        // Check the cache (e.g., database or Redis)
        $query = tep_db_query('SELECT response FROM api_cache WHERE cache_key = :cache_key AND expiry_time > NOW() LIMIT 1', null, [':cache_key' => $cacheKey]);
        $result = tep_db_fetch_all($query);
        return $result ? json_decode($result['response'], true) : null;
    }

    private function database_cacheResponse($cacheKey, $response, $cacheTTL) {
        // Cache the response in the database or Redis
        $expiryTime = time() + $cacheTTL;
        $query = "INSERT INTO api_cache (cache_key, response, expiry_time) VALUES (:cacheKey, :response, FROM_UNIXTIME(:expiryTime)) ON DUPLICATE KEY UPDATE response = VALUES(response), expiry_time = VALUES(expiry_time)";
        tep_db_query($query, null, [':cacheKey' => $cacheKey, ':response' => json_encode($response), ':expiryTime' => $expiryTime]);
    }

    private function cacheResponse($cacheKey, $response, $cacheTTL) {
        $cacheFile = DIR_FS_CACHE . '/' . $cacheKey . '.json';

        // Write the response to the cache file
        file_put_contents($cacheFile, json_encode($response));

        // Optionally, you can use `touch()` to set the file's last modified time if needed.
        touch($cacheFile, time() + $cacheTTL);
    }

    public function get_autodesk_product_catalog() {
        $connection = [
            'endpoint' => '/v1/catalog/export',
            'method' => 'GET',
            'headers' =>
            ['x-adsk-disable-redirect' => 'true']
        ];
        return $this->call($connection);
    }
    public function get_autodesk_promotions() {
        $connection = ['endpoint' => '/v1/promotions/export', 'method' => 'GET', 'headers' => ['x-adsk-disable-redirect' => 'true']];
        return $this->call($connection);
    }
    public function get_opportunity($opportunityNumber = null, $endCustomerCsn = null) {
        if ($opportunityNumber != null) {
            $query = ["opportunityNumber" => $opportunityNumber];
        } else if ($endCustomerCsn != null) {
            $query = ["endCustomerCsn" => $endCustomerCsn];
        } else {
            return ['status' => 'fail', 'message' => 'need opportunityNumber or endCustomerCsn', 'body' => $this->debugLog];
        }
        $connection = ['endpoint' => '/v2/opportunities', 'method' => 'GET', 'query' => $query];
        return $this->call($connection);
    }
    public function quotes_finalize($quoteNumber) {
        $connection = ["endpoint" => '/v1/quotes/finalize', 'method' => 'PUT', 'Content-Type' => 'application/json', "json" => ["quoteNumber" =>  $quoteNumber, "skipDDACheck" =>  true, "agentAccount" =>   ["accountCsn" => ADWS_CSN], "agentContact" =>  ["email" =>  "<EMAIL>"]]];
        return $this->call($connection);
    }
    public function quotes_status($transactionId) {
        $connection = ['endpoint' => '/v1/quotes/status', 'method' => 'GET', 'Content-Type' => null, 'query' => ['transactionId' => $transactionId]];
        return $this->call($connection);
    }
    public function quotes_view($quoteNumber) {
        $connection = [
            'endpoint' => '/v1/quotes',
            'method' => 'GET',
            'Content-Type' => null,
            'query' => ['filter[quoteNumber]' => $quoteNumber]
        ];
        return $this->call($connection);
    }
    public function search_autodesk_customers($criteria) {
        $connection = [
            "endpoint" => '/v1/accounts/search',
            'method' => 'POST',
            'Content-Type' => 'application/json',
            'json' => $criteria
        ];
        return $this->call($connection);
    }
    function send_quote($quoteData) {
        $connection = ["endpoint" => '/v3/quotes', 'method' => 'POST', 'Content-Type' => 'application/json', 'json' => $quoteData];
        return $this->call($connection);
    }

    public function raw_call($requesttype, $endpoint, $params = [], $data = [], $filepath = null) {
        try {
            $this->debugLog['log']['final_requestType'] = $requesttype;
            $this->debugLog['log']['final_endpoint'] = $endpoint;
            $this->debugLog['log']['final_data'] = $data;
            $this->debugLog['log']['final_params'] = $params;
            $this->debugLog['log']['final_filepath'] = $filepath;
            $response = $this->send_raw_call($requesttype, $endpoint, $params, $data, $filepath);
            $this->debugLog['response'] = $response;
        } catch (Exception $e) {
            $this->debugLog['log']['error'] = $e->getMessage();
            return $this->debugLog;
        }
        return $this->debugLog;
    }
    function send_raw_call($requestype, $endpoint, $params = [], $data = [], $filepath = null) {
        $connection = ['endpoint' => $endpoint, 'method' => $requestype];
        if (!empty($data)) {
            $connection['Content-Type'] = 'application/json';
            $connection['json'] = $data;
        }
        if (!empty($params)) {
            $connection['query'] = $params;
        }
        return $this->call($connection, false, 0, $filepath);
    }





    public function get_autodesk_subscriptions() {
        $connection = [
            'endpoint' => '/v2/export/subscriptions',
            'method' => 'POST',
            'json' => [
                'startDateSince' => '2014-01-01',
                'webhook' => [
                    'url' => HTTP_SERVER . '/adws_api_subscription_export.php'
                ]
            ]
        ];
        return $this->call($connection);
    }

    public function search_autodesk_products($criteria) {
        $connection = ["endpoint" => '/v1/subscriptions', 'method' => 'GET', 'query' => $criteria];
        return $this->call($connection);
    }


    public function search_autodesk_subscriptions($criteria) {
        $connection = ["endpoint" => '/v2/subscriptions', 'method' => 'GET', 'query' => $criteria];
        return $this->call($connection);
    }
}


class AutodeskQuote {
    private AutodeskAPIInterface $api;
    public $debugLog;
    public $quote_customer = [];
    public $quote_products = [];
    public $currency = "GBP";
    public $account = ['accountCsn' => ADWS_CSN];
    public $opportunityNumber;
    public $debug = false;
    public $quote_data;
    public $quote_status;
    public $lineItems = [];

    private AutodeskProducts $products;
    private AutodeskCustomers $customers;


    public function __construct($api, $products, $customers) {

        $this->api = $api;
        $this->products = $products;
        $this->customers = $customers;
    }

    private function validate_quote($quote_json) {
    }

    public function create($orders_id, $include_all = false, $quote_products_id = false) {
        error_log("create");
        $order = new Order($orders_id);
        $quote_customer = $order->customer;
        $quote_products = $order->products;
        $orders_autodesk = $this->database_get_orders_autodesk($orders_id);
        if (!empty($orders_autodesk))  $this->opportunityNumber = $orders_autodesk['opportunityNumber'];/*$quote_products_extra = $this->database_get_product_extra($orders_id);*/
        print_rr(i: $quote_products, l: "quote_products", co: false);
        $custData = $this->process_customer_data($quote_customer, $include_all);
        foreach ($quote_products as $key => $product) {
            $variation = $this->get_order_variations($product['id'], $product);
            $quote_products[$key]['autodesk_link'] = $variation['autodesk_link'];
        }
        $productData = $this->process_product_data($quote_products, $include_all, null, $orders_id);
        if ($productData == false || $custData == false) {
            return false;
        }
        $this->quote_customer = $custData;
        $this->quote_products = $productData;
    }

    public function get($quote_id) {
        return $this->database_get_quote($quote_id);
    }

    public function view($quote = null, $orders_id = null) {
        if ($orders_id) {
            $quote_id = $this->database_get_quote(orders_id: $orders_id);
        }
        if (is_string($quote)) {
            $quote_id = $this->database_get_quote($quote['id']);
        }
        $quote = $this->database_get_quote($quote['id']);
        if ($quote['quoteStatus'] == 'Not sent to Autodesk') {
            return $this->create($orders_id);
            // print_rr($quote,$quotes_id);
        } else if ($quote_id != '') {
            $quote_result = $this->api->quotes_view($quote_id);
            $quote_data = $quote_result['body']['results'][0];
            // print_rr($quote_data);
        } else {
            // print_rr($quote,$quotes_id);
            return 'Invalid Quote Number or order id';
        }
    }
    public function update($orderAction, $orders_id, $quote_products_id, $data) {

        $database = $this->database_update_product_extra(orders_id: $orders_id, products_id: $quote_products_id, extras: $data,);
        $out = $this->get_extra($orders_id, $quote_products_id, true);
        return $out['lineItems'][0];
    }
    private function database_get_orderAction($orders_id, $quote_products_id) {
        $query_sql = "SELECT * FROM orders_products_autodesk WHERE orders_id = :orders_id and products_id = :products_id";
        $result = tep_db_query($query_sql, null, [":orders_id" => $orders_id, ":products_id" => $quote_products_id]);
        if (tep_db_num_rows($result) > 0) {
            $row = tep_db_fetch_array($result);
            return $row['orderAction'];
        }
        return null;
    }

    private function database_get_orders_autodesk($orders_id) {
        $query_sql = "SELECT opportunityNumber FROM orders_autodesk WHERE orders_id = :orders_id LIMIT 1";
        $result = tep_db_query($query_sql, null, [":orders_id" => $orders_id]);
        return tep_db_fetch_array($result);
    }
    private function database_get_order_variations($orders_id) {
        $query_sql = "SELECT * FROM orders_products_variations WHERE orders_id = :orders_id";
        $result = tep_db_query($query_sql, null, [":orders_id" => $orders_id]);
        return tep_db_fetch_all($result);
    }

    public function database_addquote_from_json($order, $quote) {
        $query_sql = "SELECT * FROM autodesk_quotes WHERE transactionId = :transaction_id LIMIT 1";
        $result = tep_db_query($query_sql, null, [":transaction_id" => $quote['transactionId']]);
        $params = [':customers_id' =>  '', ':orders_id' => '', ':quoteStatus' =>  $quote['quoteStatus'], ':transactionId' => $quote['transactionId'], ':quoteNumber' =>  $quote['quoteNumber']];
        $set_sql = 'SET customers_id = :customers_id,orders_id = :orders_id,quoteStatus = :quoteStatus,transactionId = :transactionId,quoteNumber = :quoteNumber';
        $where_sql = ' WHERE orders_id = :orders_id';
        if (!tep_db_num_rows($result)) {
            $query_sql = 'INSERT INTO autodesk_quotes' . ' ' .  $set_sql;
        } else {
            $query_sql = 'UPDATE autodesk_quotes' . ' ' . $set_sql . ' ' . $where_sql;
        }
        $response = tep_db_query($query_sql, null, $params);
        return ['response' => 'Updated quote', 'query_sql' => $query_sql];
    }
    public function database_addquote_from_transaction_id($orders_id, $customers_id, $quote) {
        $query_sql = "SELECT * FROM autodesk_quotes WHERE transactionId = :transaction_id LIMIT 1";
        $result = tep_db_query($query_sql, null, [":transaction_id" => $quote['transactionId']]);
        if (!tep_db_num_rows($result)) {
            $query_sql =
                'INSERT INTO autodesk_quotes
             SET `customers_id` = :customers_id,
             `orders_id` = :orders_id,
             `quoteStatus` = :quoteStatus,
             `transactionId` = :transactionId,
             `quoteNumber` = :quoteNumber;
            ';
            $params = [':customers_id' =>  $customers_id, ':orders_id' => $orders_id, ':quoteStatus' =>  $quote['quoteStatus'], ':transactionId' => $quote['transactionId'], ':quoteNumber' => $quote['quoteNumber']];
            $response = tep_db_query($query_sql, null, $params);
            return ['response' => $response, 'query_sql' => $query_sql];
        }
    }
    public function database_addquote($orders_id = null, $customers_id = null, $order = null, $quote = null) {
        if ($customers_id == null) $customers_id = $order->customer['id'];
        if ($orders_id == null) $orders_id = $order->info['id'];
        if ($quote == null) return ['response' => 'Error: No quote supplied', 'query_sql' => ''];
        if ($orders_id) $where_sql = 'orders_id = :orders_id';
        $select_params = [
            ':orders_id' => $orders_id,
            ':transactionId' => $quote['transactionId'],
            ':quoteNumber' =>  $quote['quoteNumber']
        ];
        foreach ($def_select_params as $key => $value) {
            if ($value == null || $value == '' ) {
                $select_sql[] = "{$key} = :{$key}";
                $select_params[":{$key}"] = $value;
            }
        }

        $query_sql = "SELECT * FROM autodesk_quotes WHERE implode(' OR ', $select_sql) LIMIT 1";
        $result = tep_db_query($query_sql, null,  $select_params);

        $set_sql = 'SET customers_id = :customers_id,orders_id = :orders_id,quoteStatus = :quoteStatus,transactionId = :transactionId,quoteNumber = :quoteNumber';

          $params = [
              ':customers_id' =>  $customers_id,
              ':orders_id' => $orders_id,
              ':quoteStatus' =>  $quote['quoteStatus'],
              ':transactionId' => $quote['transactionId'],
              ':quoteNumber' =>  $quote['quoteNumber']
          ];
        foreach ($def_select_params as $key => $value) {
            if ($value == null || $value == '' ) {
                $select_sql[] = "{$key} = :{$key}";
                $select_params[":{$key}"] = $value;
            }
        }

        $where_sql = ' WHERE orders_id = :orders_id';
        if (!tep_db_num_rows($result)) {
            $query_sql = 'INSERT INTO autodesk_quotes' . ' ' .  $set_sql;
        } else {
            $query_sql = 'UPDATE autodesk_quotes' . ' ' . $set_sql . ' ' . $where_sql;
        }
        $response = tep_db_query($query_sql, null, $params);
        return ['response' => 'Updated quote', 'query_sql' => $query_sql];
    }

    public function database_get_quote($quote_id = null, $orders_id = null) {
        if ($orders_id) $where_sql = ' orders_id = :orders_id';
        elseif ($quote_id) $where_sql = ' quote_id = :quote_id';
        else return 'error: No quote_id or orders_id supplied';
        return tcs_db_query("SELECT * FROM autodesk_quotes WHERE $where_sql LIMIT 1", [":orders_id" => $orders_id, ":quote_id" => $quote_id]);
    }
    public function customers_update_quote($orders_id, $quote_products_id, $extras) {
        $query = tep_db_query("select * from orders_products_autodesk where orders_id = :orders_id and products_id = :products_id", null, [":orders_id" => $orders_id, ":products_id" => $quote_products_id]);
        if (tep_db_num_rows($query) < 1)  return ['response' => 'nothing to do'];
        $query_sql = "SELECT * FROM orders_products_autodesk WHERE orders_id = :orders_id and products_id = :products_id";
        $result = tep_db_query($query_sql, null, [":orders_id" => $orders_id, ":products_id" => $quote_products_id]);
        $params = [":orders_id" => $orders_id, ":products_id" => $quote_products_id];
        $set_sql = '';
        foreach ($extras as $key => $extra) {
            $params[':' . $key] = $extra;
            if ($set_sql != '') $set_sql .= ', ';
            $set_sql .= $key . ' = ' . ':' . $key;
        }
        if (!tep_db_num_rows($result)) {
            $query_sql = 'INSERT INTO orders_products_autodesk' . ' SET orders_id = :orders_id, products_id = :products_id, ' .  $set_sql;
        } else {
            $query_sql = 'UPDATE orders_products_autodesk' .  ' SET ' . $set_sql . ' WHERE orders_id = :orders_id and products_id = :products_id';
        }
        $response = tep_db_query($query_sql, null, $params);
        ////print_rr($query_sql, 'database_update_product_extra');
        return ['response' => 'Updated quote', 'query_sql' => $query_sql];
    }

    public function create_from_checkout($orders_id, $customers_id, $order) {
        $this->quote_status = 'Not sent to Autodesk';
        $quote = $this->create($orders_id);
        $database_response = $this->database_addquote($orders_id, $customers_id, $order, $quote);
        return $database_response;
    }

    private function get_order_variations($quote_products_id, $product) {
        $attributes = new tcs_product_attributes($quote_products_id, 1, $product['attributes']);
        $variation = $attributes->get_current_selected_variation();
        if (empty($variation)) return false;
        return $variation;
    }

    public function send($orders_id) {
        try {
            $this->debugLog['log']['final_quote_data'] = $this;
            $response = $this->api->send_quote($this->quote_data);
            $quote = $response['body'];
            if ($quote['status'] == 'fail') return $this->debugLog;
            $status_response = $this->api->quotes_status($quote['transactionId']);
            $quote_status = $status_response['body'];
        } catch (Exception $e) {
            $this->debugLog['log']['error'] = $e->getMessage();
            return $this->debugLog;
        }
        if ($quote_status['status'] == 'fail') return $this->debugLog;/*$quote = $quote_status['body'];*/

        $database_response = $this->database_addquote($orders_id, null, null, $quote_status);
        $this->debugLog['log']['database_query'] = $database_response['query_sql'];
        $this->debugLog['log']['database_reponse'] = $database_response['response'];
        return $this->debugLog;
    }
    public function finalize($quoteNumber) {
        return $this->api->quotes_finalize($quoteNumber);
    }



    private function process_customer_data($quote_customer, $include_all = false) {
        $endCustomer = [];/*search for customer account*/
        $cust_search = $this->api->search_autodesk_customers($quote_customer['id'], $quote_customer['email_address']);/* $endCustomer = $cust_search;////print_rr($cust_search);////print_rr($quote_customer);    */
        $resultsExist = false;
        if ($resultsExist) $endCustomer['accountCsn'] = $cust_search['accountCsn'];
        $endCustomer = array_merge($endCustomer, ['isIndividual' => empty($quote_customer['company']) ? "true" : "false", 'addressLine1' => $quote_customer['street_address'], 'addressLine2' => $quote_customer['suburb'], 'city' => $quote_customer['city'], 'stateProvinceCode' => $quote_customer['state'], 'postalCode' => $quote_customer['postcode'], 'countryCode' => is_string($quote_customer['country']) ? "GB" : $quote_customer['country']['iso_code_2']]);/* Only add 'name' if $quote_customer['company'] is not empty*/
        if (!empty($quote_customer['company'])) {
            $endCustomer['name'] = $quote_customer['company'];
        }
        return ['agentContact' => ['email' => '<EMAIL>'], 'endCustomer' => $endCustomer, 'quoteContact' => ['email' => $quote_customer['email_address'], 'firstName' => $quote_customer['firstname'], 'lastName' => $quote_customer['lastname'], 'phone' => $quote_customer['telephone'], 'preferredLanguage' => 'en']];
    }
    private function process_product_data($quote_products, $include_all = false, $quote_products_id = null, $orders_id = null) {
        $lineItems = [];
        foreach ($quote_products as $product) {
            if ($quote_products_id != null && $quote_products_id != $product['id']) {
                continue;
            }
            $autodesk_product = $this->products->get_autodesk_product_from_catalog($product['id'], $product['autodesk_link'] ?? null);
            $autodesk_extras = $this->database_get_product_extra($orders_id, $product['id']);
            $orderAction = $autodesk_extras['orderAction'] ?? $autodesk_product['orderAction'];
            if ($orders_id != null) {
                $db_order_action = $this->database_get_orderAction($orders_id, $product['id']);
                if ($db_order_action != null) $orderAction = $db_order_action;
            }
            $orderAction_has_dda = false;
            if (str_ends_with($orderAction, '_DDA')) {
                $orderAction = str_replace('_DDA', '', $orderAction);
                $orderAction_has_dda = true;
            }
            $lineItems = [];
            if (is_array($autodesk_extras)) $autodesk_product = array_merge($autodesk_product, $autodesk_extras);
            $simpleAction = strtolower(str_replace(['_', '-'], '', $orderAction));
            $lineitems_extra = [];
            switch ($simpleAction) {
                case 'new':
                    break;
                case 'renewal':
                    if (isset($autodesk_product['subscriptionId']) || $include_all == true) {
                        $lineItems = ["action" => $orderAction, "quantity" => $autodesk_product["quantity"], "subscriptionId" => $autodesk_product["subscriptionId"], "promotionCode" => $autodesk_product["promotionCode"], "offer" => ["term" => ["code" => $autodesk_product["offer"]["term"]["code"], "description" => $autodesk_product["offer"]["term"]["description"]], "accessModel" => ["code" => $autodesk_product["offer"]["accessModel"]["code"], "description" => $autodesk_product["offer"]["accessModel"]["description"]], "intendedUsage" => ["code" => $autodesk_product["offer"]["intendedUsage"]["code"], "description" => $autodesk_product["offer"]["intendedUsage"]["description"]], "connectivity" => ["code" => $autodesk_product["offer"]["connectivity"]["code"], "description" => $autodesk_product["offer"]["connectivity"]["description"]], "servicePlan" => ["code" => $autodesk_product["offer"]["servicePlan"]["code"], "description" => $autodesk_product["offer"]["servicePlan"]["description"]]]];
                        $lineitems_extra =  ["subscriptionId" => $autodesk_product["subscriptionId"]];
                    } else {
                        ["error" => "Subscription ID not found for product " . $product['id']];
                    }
                    break;
                case 'switchproduct':
                    if (isset($autodesk_product['subscriptionId']) || $include_all == true) {
                        $lineItems = ["action" => $orderAction, "quantity" => $autodesk_product["quantity"], "subscriptionId" => $autodesk_product["subscriptionId"], "promotionCode" => $autodesk_product["promotionCode"], "offer" => ["term" => ["code" => $autodesk_product["offer"]["term"]["code"], "description" => $autodesk_product["offer"]["term"]["description"]], "accessModel" => ["code" => $autodesk_product["offer"]["accessModel"]["code"], "description" => $autodesk_product["offer"]["accessModel"]["description"]], "intendedUsage" => ["code" => $autodesk_product["offer"]["intendedUsage"]["code"], "description" => $autodesk_product["offer"]["intendedUsage"]["description"]], "connectivity" => ["code" => $autodesk_product["offer"]["connectivity"]["code"], "description" => $autodesk_product["offer"]["connectivity"]["description"]], "servicePlan" => ["code" => $autodesk_product["offer"]["servicePlan"]["code"], "description" => $autodesk_product["offer"]["servicePlan"]["description"]]]];
                        $lineitems_extra =  ["subscriptionId" => $autodesk_product["subscriptionId"]];
                    } else {
                        ["error" => "Subscription ID not found for product " . $product['id']];
                    }
                    break;
                case 'switchterm':
                    if (isset($autodesk_product['subscriptionId']) || $include_all == true) {
                        $lineItems = ["subscriptionId" => $product['subscriptionId'], "endDate" => date('Y-m-d')];
                    } else {
                        ["error" => "Subscription ID not found for product " . $product['id']];
                    }
                    $lineitems_extra =  ["subscriptionId" => $product['subscriptionId'], "endDate" => date('Y-m-d')];
                    break;
                case 'coterm':
                    if (isset($autodesk_product['referenceSubscriptionId']) || $include_all == true) {
                        $lineItems = ["action" => "Co-term", "referenceSubscriptionId" => $autodesk_product["referenceSubscriptionId"], "quantity" => $autodesk_product["quantity"], "startDate" => $autodesk_product["startDate"],];
                        $lineitems_extra =  ["referenceSubscriptionId" => $autodesk_product["referenceSubscriptionId"], "startDate" => $autodesk_product["startDate"],];
                    } else {
                        return ["error" => "referenceSubscriptionId ID not found for product " . $product['id']];
                    }
                    break;
                case 'trueup':
                    if (isset($autodesk_product['subscriptionId']) || $include_all == true) {
                        $lineItems = ["action" => "True-Up", "subscriptionId" => $autodesk_product["subscriptionId"], "quantity" => $autodesk_product["quantity"]];
                        $lineitems_extra =  ["subscriptionId" => $autodesk_product["subscriptionId"]];
                    } else {
                        ["error" => "Subscription ID not found for product " . $product['id']];
                    }
                    break;
                case 'extension':
                    $lineItems = ["action" => "Extension", "subscriptionId" => $autodesk_product["subscriptionId"], "endDate" => $autodesk_product["endDate"]];
                    $lineitems_extra =  ["subscriptionId" => $autodesk_product["subscriptionId"], "endDate" => $autodesk_product["endDate"]];
                default:
                    ["error" => "Action not found for product " . $product['id']];
                    break;
            }
            if ($orderAction_has_dda) {
                $lineitems_extra['opportunityLineItemId'] = $autodesk_product["opportunityLineItemId"];
            }
            $lineitems_new = null;
            ////print_rr($orderAction, 'orderAction beforeNew');
            if ($simpleAction == 'new' || $include_all == true) {
                ////print_rr($orderAction, 'orderAction pickedNew');
                if (isset($autodesk_product['offeringId'])) {
                    ////print_rr($autodesk_product['offeringId'], '$autodesk_product["offeringId"] isset');
                    $lineItems = ['offeringId' => $autodesk_product['offeringId'], 'offeringName' => $autodesk_product['offeringName'], 'offeringCode' => $autodesk_product['offeringCode'], 'action' => $orderAction, 'quantity' => $product['qty'], 'startDate' => date('Y-m-d'), 'offer' => ['term' => ['code' => $autodesk_product['term_code'], 'description' => $autodesk_product['term_description']], 'accessModel' => ['code' => $autodesk_product['accessModel_code'], 'description' => $autodesk_product['accessModel_description']], 'intendedUsage' => ['code' => $autodesk_product['intendedUsage_code'], 'description' => $autodesk_product['intendedUsage_description']],                'connectivity' => ['code' => $autodesk_product['connectivity_code'], 'description' => $autodesk_product['connectivity_description']], 'servicePlan' => ['code' => $autodesk_product['servicePlan_code'], 'description' => $autodesk_product['servicePlan_description']]],];
                }
            }
            if ($include_all) {
                $lineItems['new'] = $lineItems;
                $lineItems['extra'] = $lineitems_extra;
                $lineItems['internal'] = ["products_id" => $product['id']];
            }
            $this->lineItems[] = $lineItems;
        }
    }
    public function get_extra($orders_id, $products_id, $include_all = false) {
        $order = new Order($orders_id);
        $products = $order->products;
        $productData = $this->process_product_data($products, $include_all, $products_id, $orders_id);
        return $productData;
    }
    public function update_product_extra($orders_id, $products_id, $extras) {
        return $this->database_update_product_extra($orders_id, $products_id, $extras);
    }
    private function database_update_product_extra($orders_id, $products_id, $extras) {
        if (sizeof($extras) < 1) return ['response' => 'nothing to do'];
        $query_sql = "SELECT * FROM orders_products_autodesk WHERE orders_id = :orders_id and products_id = :products_id";
        $result = tep_db_query($query_sql, null, [":orders_id" => $orders_id, ":products_id" => $products_id]);
        $params = [":orders_id" => $orders_id, ":products_id" => $products_id];
        $set_sql = '';
        foreach ($extras as $key => $extra) {
            $params[':' . $key] = $extra;
            if ($set_sql != '') $set_sql .= ', ';
            $set_sql .= $key . ' = ' . ':' . $key;
        }
        if (!tep_db_num_rows($result)) {
            $query_sql = 'INSERT INTO orders_products_autodesk' . ' SET orders_id = :orders_id, products_id = :products_id, ' .  $set_sql;
        } else {
            $query_sql = 'UPDATE orders_products_autodesk' .  ' SET ' . $set_sql . ' WHERE orders_id = :orders_id and products_id = :products_id';
        }
        $response = tep_db_query($query_sql, null, $params);
        ////print_rr($query_sql, 'database_update_product_extra');
        return ['response' => 'Updated quote', 'query_sql' => $query_sql];
    }

    private function database_get_product_extra($orders_id, $products_id = null) {
        $query_sql = "SELECT * FROM orders_products_autodesk WHERE orders_id = :orders_id";
        $params = [":orders_id" => $orders_id];
        if ($products_id != null) {
            $query_sql .= " and products_id = :products_id";
            $params[':products_id'] = $products_id;
        }
        $result = tep_db_query($query_sql, null, $params);
        print_rr($result, 'database_get_product_extra query_sql');
        if ($products_id) return tep_db_fetch_array($result);
        return tep_db_fetch_all($result);
    }
}

class AutodeskQuotes {
    private AutodeskAPIInterface $api;
    public $debugLog;
    public $customer = [];
    public $products = [];
    public $currency = "GBP";
    public $account = ['accountCsn' => ADWS_CSN];
    public $opportunityNumber;
    public $debug = false;
    public $quote_data;
    public $quote_status;
    public $lineItems = [];

    public function __construct($api) {
        $this->api = $api;
    }

    public function get_current() {
        $quotes = $this->database_get_current_quotes();
        return tep_db_fetch_all($quotes);
    }
    private function database_get_current_quotes($transaction_id = null, $quotes_id = null) {
        $query_sql =
            "SELECT * 
        FROM autodesk_quotes a
        LEFT JOIN customers c 
            on a.customers_id = c.customers_id
        LEFT JOIN orders o 
            on a.orders_id = o.orders_id";
        $where_sql = "";
        $param = [];
        if ($transaction_id != null) {
            $where_sql .= ' transactionId = :transaction_id';
            $param[':transaction_id'] = $transaction_id;
        };
        if ($quotes_id != null) {
            if ($where_sql != '') $where_sql .= ' AND ';
            $where_sql .= ' quote_id = :quote_id';
            $param[':quote_id'] = $quotes_id;
        }
        if ($where_sql != '') $where_sql = ' WHERE ' . $where_sql;
        $query_sql .= $where_sql . ' order by a.quote_id desc';
        return tep_db_query($query_sql, null, $param);
    }
    private function database_get_product_extra($orders_id, $products_id = null) {
        $query_sql = "SELECT * FROM orders_products_autodesk WHERE orders_id = :orders_id";
        $params = [":orders_id" => $orders_id];
        if ($products_id != null) {
            $query_sql .= " and products_id = :products_id";
            $params[':products_id'] = $products_id;
        }
        $result = tep_db_query($query_sql, null, $params);
        ////print_rr($result, 'database_get_product_extra query_sql');
        if ($products_id) return tep_db_fetch_array($result);
        return tep_db_fetch_all($result);
    }
}
class AutodeskSubscription {

    public $subscriptionId;
    private AutodeskAPIInterface $api;
    public $debugLog;

    public function __construct($api) {
        $this->api = $api;
    }

    public function get($id) {
        return get_from_api($id);
    }
    public function get_from_api($id) {
        $criteria = [];
        if (strpos($id, '-'))
            $criteria["filter[subscriptionReferenceNumber]"] = $id;
        else
            $criteria["filter[subscriptionId]"] = $id;
        //print_rr($criteria, 'criteria');
        return $this->api->search_autodesk_subscriptions($criteria);
    }
}
class AutodeskSubscriptions {
    private AutodeskAPIInterface $api;
    public AutodeskAuthenticator $auth;
    public $debugLog;

    public function __construct($api, $auth) {
        $this->api = $api;
        $this->auth = $auth;
    }
    public function get_expiring($time = null) {
        $date = new DateTime();
        $now = $date->format('Y-m-d');
        $when = $date->modify('+29 day')->format('Y-m-d');
        $criteria["filter[endDate]"] = "{$now}..{$when}";
        //print_rr($criteria, 'criteria');
        return $this->api->search_autodesk_subscriptions($criteria);
    }

    public function search($criteria) {
        return $this->api->search_autodesk_subscriptions($criteria);
    }

    private function database_get_all($criteria = [], $order_by = []) {
        $search_columns = ["st.csn", "st.name", "sp.csn", "sp.name", "sp.address1", "sp.address2", "sp.address3", "sp.city", "sp.stateProvince", "sp.postalCode", "sp.country", "sp.stateProvinceCode", "sp.countryCode", "nr.csn", "nr.name", "ec.csn", "ec.name", "ec.address1", "ec.address2", "ec.address3", "ec.city", "ec.stateProvince", "ec.postalCode", "ec.country", "ec.primaryAdminFirstName", "ec.primaryAdminLastName", "ec.primaryAdminEmail", "ec.teamId", "ec.teamName", "ec.first", "ec.last", "ec.email"];

        $where = "";
        $order_by = "ORDER BY func_daydifference ASC";
        $begin = $end = "";
        $get_prefix = function ($col) {
            return match (explode("_", $col)[0]) {
                "SoldTo" => "st.",
                "endCustomer" => "ec.",
                "solutionProvider" => "sp.",
                "nurtureReseller" => "nr.",
                "func" => "",
                default => "asd."
            };
        };
        if ($criteria) {
            if ($criteria["where"]) {
                $where = " WHERE ";
                $count = 0;
                if ($criteria["where"]["search"]) {
                    foreach ($search_columns as $column) {
                        $prefix = $get_prefix($criteria["column"]);
                        if ($count > 0)  $where .= " OR ";
                        $count++;
                        $where .= " {$prefix}{$column} LIKE '%{$criteria["search"]}%'";
                    }
                }
                if ($count > 0)  $where .= " AND ";
                if ($criteria["where"]["columns"]) {
                    foreach ($criteria["where"]["columns"] as $column => $data) {
                        $prefix = $get_prefix($criteria["column"]);
                        if ($count > 0)  $where .= " AND ";
                        $count++;
                        $where .= " {$prefix}{$column} = '$data'";
                    }
                }
            }
            if ($criteria["orderBy"]) {
                $get_prefix($criteria["orderBy"]["column"]);
                $order_by = "ORDER BY {$prefix}{$criteria["orderBy"]["column"]} {$criteria["orderBy"]["direction"]}";
            }
        }
        if (isset($criteria["begin"]))  $begin = " BEGIN {$begin}";
        if (isset($criteria["end"]))  $end = " END {$end}";
        return tcs_db_query(
            "SELECT asd.*,
                DATEDIFF(asd.endDate, now()) AS func_daydifference,
                st.csn AS soldTo_csn,
                st.name AS soldTo_name,
                sp.csn AS solutionProvider_csn,
                sp.name AS solutionProvider_name,
                sp.localLanguageName AS solutionProvider_localLanguageName,
                sp.type AS solutionProvider_type,
                sp.address1 AS solutionProvider_address1,
                sp.address2 AS solutionProvider_address2,
                sp.address3 AS solutionProvider_address3,
                sp.city AS solutionProvider_city,
                sp.stateProvince AS solutionProvider_stateProvince,
                sp.postalCode AS solutionProvider_postalCode,
                sp.country AS solutionProvider_country,
                sp.stateProvinceCode AS solutionProvider_stateProvinceCode,
                sp.countryCode AS solutionProvider_countryCode,
                nr.csn AS nurtureReseller_csn,
                nr.name AS nurtureReseller_name,
                nr.lockdate AS nurtureReseller_lockdate,
                nr.nurtureDiscountEligibility AS nurtureReseller_nurtureDiscountEligibility,
                ec.csn AS endCustomer_csn,
                ec.name AS endCustomer_name,
                ec.type AS endCustomer_type,
                ec.address1 AS endCustomer_address1,
                ec.address2 AS endCustomer_address2,
                ec.address3 AS endCustomer_address3,
                ec.city AS endCustomer_city,
                ec.stateProvince AS endCustomer_stateProvince,
                ec.postalCode AS endCustomer_postalCode,
                ec.country AS endCustomer_country,
                ec.individualFlag AS endCustomer_individualFlag,
                ec.namedAccountFlag AS endCustomer_namedAccountFlag,
                ec.namedAccountGroup AS endCustomer_namedAccountGroup,
                ec.parentIndustryGroup AS endCustomer_parentIndustryGroup,
                ec.parentIndustrySegment AS endCustomer_parentIndustrySegment,
                ec.primaryAdminFirstName AS endCustomer_primaryAdminFirstName,
                ec.primaryAdminLastName AS endCustomer_primaryAdminLastName,
                ec.primaryAdminEmail AS endCustomer_primaryAdminEmail,
                ec.teamId AS endCustomer_teamId,
                ec.teamName AS endCustomer_teamName,
                ec.first AS endCustomer_first,
                ec.last AS endCustomer_last,
                ec.email AS endCustomer_email,
                ec.status AS endCustomer_status,
                ec.portalRegistration AS endCustomer_portalRegistration,
                ec.doNotCall AS endCustomer_doNotCall,
                ec.doNotEmail AS endCustomer_doNotEmail,
                ec.doNotMail AS endCustomer_doNotMail,
                ec.stateProvinceCode AS endCustomer_stateProvinceCode,
                ec.countryCode AS endCustomer_countryCode
            FROM autodesk_subscriptions asd
            JOIN autodesk_accounts st on asd.soldTo_id = st.id 
            JOIN autodesk_accounts sp on asd.solutionProvider_id = sp.id
            JOIN autodesk_accounts nr on asd.nurtureReseller_id = nr.id
            JOIN autodesk_accounts ec on asd.endCustomer_id = ec.id
            {$where} {$order_by} {$begin} {$end}
            Limit 100"
        );
    }


    public function get_all($criteria = []) {
        return $this->database_get_all($criteria);
    }


    public static function get_subscription_column_mapping() {
        return

        [
            "soldTo" => [
                "table" => 'autodesk_accounts',
                "columns" => [
                    'accounts.soldTo.csn' => 'account_csn',
                    'accounts.soldTo.name' => 'name',
                ]
            ],

            "solutionProvider" => [
                "table" => 'autodesk_accounts',
                "columns" => [
                    'accounts.solutionProvider.csn' => 'account_csn',
                    'accounts.solutionProvider.name' => 'name',
                    'accounts.solutionProvider.localLanguageName' => 'local_language_name',
                    'accounts.solutionProvider.type' => 'type',
                    'accounts.solutionProvider.address1' => 'address1',
                    'accounts.solutionProvider.address2' => 'address2',
                    'accounts.solutionProvider.address3' => 'address3',
                    'accounts.solutionProvider.city' => 'city',
                    'accounts.solutionProvider.stateProvince' => 'state_province',
                    'accounts.solutionProvider.postalCode' => 'postal_code',
                    'accounts.solutionProvider.country' => 'country',
                    'accounts.solutionProvider.stateProvinceCode' => 'state_province_code',
                    'accounts.solutionProvider.countryCode' => 'country_code',
                ]

            ],

            "nurtureReseller" => [
                "table" => 'autodesk_accounts',
                "columns" => [
                    'accounts.nurtureReseller.csn' => 'account_csn',
                    'accounts.nurtureReseller.name' => 'name',
                    'accounts.nurtureReseller.lockdate' => 'lockdate',
                ]
            ],
            "endCustomer" => [
                "table" => 'autodesk_accounts',
                "columns" => [
                    'endCustomer.account.endCustomerCsn' => 'account_csn',
                    'endCustomer.account.name' => 'name',
                    'endCustomer.account.type' => 'type',
                    'endCustomer.account.address1' => 'address1',
                    'endCustomer.account.address2' => 'address2',
                    'endCustomer.account.address3' => 'address3',
                    'endCustomer.account.city' => 'city',
                    'endCustomer.account.stateProvince' => 'state_province',
                    'endCustomer.account.postalCode' => 'postal_code',
                    'endCustomer.account.country' => 'country',
                    'endCustomer.account.individualFlag' => 'individual_flag',
                    'endCustomer.account.namedAccountFlag' => 'named_account_flag',
                    'endCustomer.account.namedAccountGroup' => 'named_account_group',
                    'endCustomer.account.parentIndustryGroup' => 'parent_industry_group',
                    'endCustomer.account.parentIndustrySegment' => 'parent_industry_segment',
                    'endCustomer.account.primaryAdminFirstName' => 'primary_admin_first_name',
                    'endCustomer.account.primaryAdminLastName' => 'primary_admin_last_name',
                    'endCustomer.account.primaryAdminEmail' => 'primary_admin_email',
                    'endCustomer.account.teamId' => 'team_id',
                    'endCustomer.account.teamName' => 'team_name',
                    'endCustomer.purchaser.first' => 'first_name',
                    'endCustomer.purchaser.last' => 'last_name',
                    'endCustomer.purchaser.email' => 'email',
                    'endCustomer.purchaser.status' => 'status',
                    'endCustomer.purchaser.portalRegistration' => 'portal_registration',
                    'endCustomer.purchaser.doNotCall' => 'do_not_call',
                    'endCustomer.purchaser.doNotEmail' => 'do_not_email',
                    'endCustomer.purchaser.doNotMail' => 'do_not_mail',
                    'endCustomer.account.stateProvinceCode' => 'state_province_code',
                    'endCustomer.account.countryCode' => 'country_code',
                ]
            ],
            "subscriptions" => [
                "table" => 'autodesk_subscriptions',
                "columns" => [
                    'subscriptionId' => 'subscription_id',
                    'subscriptionReferenceNumber' => 'subscription_reference_number',
                    'quantity' => 'quantity',
                    'status' => 'status',
                    'startDate' => 'start_date',
                    'endDate' => 'end_date',
                    'term' => 'term',
                    'billingBehavior' => 'billing_behavior',
                    'billingFrequency' => 'billing_frequency',
                    'offeringId' => 'offering_id',
                    'offeringCode' => 'offering_code',
                    'offeringName' => 'offering_name',
                    'renewalCounter' => 'renewal_counter',
                    'autoRenew' => 'auto_renew',
                    'recordType' => 'record_type',
                    'intendedUsage' => 'intended_usage',
                    'connectivity' => 'connectivity',
                    'connectivityInterval' => 'connectivity_interval',
                    'opportunityNumber' => 'opportunity_number',
                    'servicePlan' => 'service_plan',
                    'accessModel' => 'access_model',
                    'paymentMethod' => 'payment_method'
                ],
                "extra" => [
                    'nurtureReseller_id' => "<group_insert_id>nurtureReseller</group_insert_id>",
                    'endCustomer_id' => "<group_insert_id>endCustomer</group_insert_id>",
                    'solutionProvider_id' => "<group_insert_id>solutionProvider</group_insert_id>",
                    'soldTo_id' =>  "<group_insert_id>soldTo</group_insert_id>",
                ]
            ]
        ];
}



    //public function get_all() {
    //    
    //}get_subscriptions_from_api()


    //      "id":"d54ddc7f022695781865b01828722584","status":"processing","password":"Nzg2ZTVhNjQtNzdjYy00MDNhLTlhNmItM2U5OTliZmI4NWJj"}
    public function get_from_api() { /* Configuration*/
        $enc_file_path = DIR_FS_CATALOG . '/feeds/subscriptions.csv.zip.enc';
        $csv_file_path = DIR_FS_CATALOG . '/feeds/subscriptions.csv'; /* Define the path where you want to save the CSV file*/

        try {
            $get_subscriptions =  $this->api->get_autodesk_subscriptions();
            if ($get_subscriptions['status'] == 'fail') {
                return ['status' => 'fail', 'failing' => 'get_autodesk_subscriptions', 'response' => $get_subscriptions, 'log' => $this->debugLog];
            }
            //print_rr($get_subscriptions['response'];
            $response = $get_subscriptions['body'];
            $this->database_update_api_export_data($response);
        } catch (Exception $e) {
            $response = $e->getMessage();
            return ['status' => 'fail', 'response' => $response, 'log' => $this->debugLog];
        }

        return ["status" => "Requested, it's the webhook's problem now"];
    }

    public function download_autodesk_subscription_export($file_uri) { /* Configuration*/
        $enc_file_path = DIR_FS_CATALOG . 'feeds/subscriptions.csv.zip.enc'; /* Define the path where you want to save the CSV file*/
        $csv_file_path = DIR_FS_CATALOG . 'feeds/subscriptions.csv';
        $download = false;/* Check if the file exists and is less than a day old*/
        if (file_exists($csv_file_path)) {
            $file_age = time() - filemtime($csv_file_path);
            if ($file_age > 86400) $download = true;
        } else {
            $download = true;
        }

        if ($download) {
            try {
                $input_file = file_get_contents($file_uri);                            /* Download the CSV file and save it to the specified path                */
                file_put_contents($enc_file_path, $input_file);
            } catch (Exception $e) {
                $response = $e->getMessage();
                return ['status' => 'fail', 'response' => $response];
            }
        }
        try {
            $info = $this->database_get_api_export_data();
            //return ['status' => 'success', 'response' => print_r($info, true)];
            $decrypted_file = $this->auth->decryptFile($enc_file_path, $csv_file_path, $info->password);
        } catch (Exception $e) {
            $response = $e->getMessage();
            return ['status' => 'fail', 'response' => $response];
        }
        return ['status' => 'success', 'response' => $decrypted_file];
    }

    private function database_update_api_export_data($body) {
        $jsonData = json_encode($body);

        // Check if JSON encoding was successful
        if ($jsonData === false) {
            // Log the error or throw an exception
            error_log("Failed to encode body to JSON: " . json_last_error_msg());
            return false;
        }

        $query_sql = "INSERT INTO autodesk_storage (`autodesk_storage_key`, `autodesk_storage_data`) 
                      VALUES (:autodesk_storage_key, :autodesk_storage_data) 
                      ON DUPLICATE KEY UPDATE `autodesk_storage_data` = :autodesk_storage_data_update";

        try {
            return tep_db_query($query_sql, null, [
                ":autodesk_storage_key" => 'subscription_export_data',
                ":autodesk_storage_data" => $jsonData,
                ":autodesk_storage_data_update" => $jsonData
            ]);
        } catch (Exception $e) {
            // Handle or log error as needed
            error_log("Database query failed: " . $e->getMessage());
            return false;
        }
    }
    private function database_get_api_export_data() {
        $query_sql = "select * from autodesk_storage where autodesk_storage_key = 'subscription_export_data' limit 1";
        $value = tcs_db_query($query_sql, null);
        return json_decode($value[0]['autodesk_storage_data']);
    }
}

class AutodeskProducts {
    private AutodeskAPIInterface $api;
    public $debugLog;
    public $products = [];

    function __construct($api) {
        $this->api = $api;
    }
    public function get($id) {
        $criteria = [];
        if (strpos($id, '-'))
            $criteria["filter[productId]"] = $id;
        else
            $criteria["filter[productReferenceNumber]"] = $id;
        return $this->api->search_autodesk_products($criteria);
    }
    public function get_catalog() { /* Configuration*/
        $csv_file_path = DIR_FS_CATALOG . '/feeds/product_catalog.csv'; /* Define the path where you want to save the CSV file*/
        $download = false;/* Check if the file exists and is less than a day old*/
        if (file_exists($csv_file_path)) {
            $file_age = time() - filemtime($csv_file_path);
            if ($file_age > 86400) $download = true;
        } else {
            $download = true;
        }
        if ($download) {
            $get_catalog =  $this->api->get_autodesk_product_catalog();
            if ($get_catalog['status'] == 'fail') {
                return $get_catalog;
            } else {
                $response = $get_catalog['response'];
            }
            $response_headers = $response['headers'];
            if (is_string($response_headers['Location'][0])) {
                $input_file = file_get_contents($response_headers['Location'][0]);                            /* Download the CSV file and save it to the specified path                */
                file_put_contents($csv_file_path, $input_file);
            } else {
                return ['status' => 'fail', 'response' => $response];
            }
        }
        return $this->import_autodesk_catalog_into_database($csv_file_path);
    }


    private function get_product_catalog_header_map() {
        return  ['offeringName' => 'offeringName', 'offeringCode' => 'offeringCode', 'offeringId' => 'offeringId', 'intendedUsage.code' => 'intendedUsage_code', 'intendedUsage.description' => 'intendedUsage_description', 'accessModel.code' => 'accessModel_code', 'accessModel.description' => 'accessModel_description', 'servicePlan.code' => 'servicePlan_code', 'servicePlan.description' => 'servicePlan_description', 'connectivity.code' => 'connectivity_code', 'connectivity.description' => 'connectivity_description', 'term.code' => 'term_code', 'term.description' => 'term_description', 'lifeCycleState' => 'lifeCycleState', 'renewOnlyDate' => 'renewOnlyDate', 'discontinueDate' => 'discontinueDate', 'orderAction' => 'orderAction', 'specialProgramDiscount.code' => 'specialProgramDiscount_code', 'specialProgramDiscount.description' => 'specialProgramDiscount_description', 'fromQty' => 'fromQty', 'toQty' => 'toQty', 'currency' => 'currency', 'SRP' => 'SRP', 'costAfterSpecialProgramDiscount' => 'costAfterSpecialProgramDiscount', 'renewalDiscountPercent' => 'renewalDiscountPercent', 'renewalDiscountAmount' => 'renewalDiscountAmount', 'costAfterRenewalDiscount' => 'costAfterRenewalDiscount', 'transactionVolumeDiscountPercent' => 'transactionVolumeDiscountPercent', 'transactionVolumeDiscountAmount' => 'transactionVolumeDiscountAmount', 'costAfterTransactionVolumeDiscount' => 'costAfterTransactionVolumeDiscount', 'serviceDurationDiscountPercent' => 'serviceDurationDiscountPercent', 'serviceDurationDiscountAmount' => 'serviceDurationDiscountAmount', 'costAfterServiceDurationDiscount' => 'costAfterServiceDurationDiscount', 'effectiveStartDate' => 'effectiveStartDate', 'effectiveEndDate' => 'effectiveEndDate'];
    }


    public function import_autodesk_catalog_into_database($csv_file_path) {    /* Main script execution*/
        $output = AutodeskAPI::import_csv_into_database(
            mapping: $this->get_product_catalog_header_map(),
            unique_hash: ['offeringId', 'intendedUsage.code', 'accessModel.code', 'servicePlan.code', 'connectivity.code', 'term.code', 'orderAction', 'specialProgramDiscount.code', 'fromQty', 'toQty'],
            csv_file_path: $csv_file_path,
            debug: true
        );
        $this->database_update_autodesk_pricing();
        return $output;
    }
    private function database_get_autodesk_products() {
        return tcs_db_query(
            "
            select * 
            FROM products_autodesk_catalog pac 
                JOIN products_to_autodesk_catalog p2a ON p2a.unique_hash = pac.unique_hash
                JOIN products p ON p2a.products_id = p.products_id
            WHERE 
                p.products_price > 0 
                AND pac.srp > 0"
        );
    }

    public function database_update_autodesk_pricing($products = null) {
        if ($products == null) $products = $this->database_get_autodesk_products();
        ////print_rr($products);
        // Initialize an array to store product IDs and price mappings
        $ids = [];
        $caseStatements = [];

        // Collect all product prices and ids for the bulk update
        foreach ($products as $product) {
            $price = $this->products_calculate_pricing($product);
            $ids[] = $product['products_id'];  // Collect the product ID
            $caseStatements[] = "WHEN {$product['products_id']} THEN {$price}";
        }

        // If no products are found, stop further execution
        if (empty($ids)) return;

        // Build the bulk update query using CASE
        $idsList = implode(",", $ids);
        $caseQuery = implode(" ", $caseStatements);

        $sql_query = "" .
            "UPDATE `products` 
                SET `products_price` = CASE `products_id`
                     $caseQuery
                END
                WHERE `products_id` IN ($idsList)";

        // Execute the bulk update query
        ////print_rr($sql_query);
        tep_db_query($sql_query);
    }
    public function get_autodesk_product_from_catalog($products_id, $autodesk_id = null) {
        if (is_numeric($autodesk_id)) {
            $query_sql = "SELECT * FROM products_autodesk_catalog WHERE `id` = :autodesk_id";
            $query = tep_db_query($query_sql, null, [":autodesk_id" => $autodesk_id]);
            if (tep_db_num_rows($query)) return tep_db_fetch_array($query);
        }
        $query_sql = "SELECT * FROM products_to_autodesk_catalog p2a JOIN products_autodesk_catalog pac ON p2a.unique_hash = pac.unique_hash WHERE products_id = :products_id";
        $query = tep_db_query($query_sql, null, [":products_id" => $products_id]);
        return tep_db_fetch_array($query);
    }

    public function products_calculate_pricing($product) {
        $price = $product['SRP'];
        $price -= $product['renewalDiscountAmount'] ?? 0;
        $price -= $product['transactionVolumeDiscountAmount'] ?? 0;
        $price -= $product['serviceDurationDiscountAmount'] ?? 0;
        return $price;
    }
}

class autodeskCustomers {
    private AutodeskAPIInterface $api;
    public $debugLog;
    public function __construct($api) {
        $this->api = $api;
    }

    public function get($csn) {

        $out = $this->database_get($csn);
        $subs = $this->database_get_subscriptions($out['endCustomer_id']);
        $out['subscriptions'] = $subs;
        return $out;
    }

    private function database_get($csn) {
        $out = tcs_db_query(
            "SELECT 
               *
            FROM autodesk_accounts            
            WHERE csn = '{$csn}'
            Limit 1"
        );
        return $out[0];
    }

    public function get_all($criteria = []) {
        return $this->database_get_all($criteria);
    }
    private function database_get_all($criteria = [], $order_by = []) {
        $search_columns = ["name", "localLanguageName", "address1", "address2", "address3", "city", "stateProvince", "postalCode", "country", "primaryAdminFirstName", "primaryAdminLastName", "primaryAdminEmail", "first", "last", "email",];

        $where = "";
        $order_by = "ORDER BY ec.name ASC";
        $begin = $end = "";
        if ($criteria) {


            if ($criteria["where"]) {
                $where = " WHERE ";
                $count = 0;
                if ($criteria["where"]["search"]) {
                    foreach ($search_columns as $column) {
                        if ($count > 0)  $where .= " OR ";
                        $count++;
                        $where .= " {$column} LIKE '%{$criteria["search"]}%'";
                    }
                }
                if ($count > 0)  $where .= " AND ";
                if ($criteria["columns"]) {
                    foreach ($criteria["where"]["columns"] as $column => $data) {
                        if ($count > 0)  $where .= " AND ";
                        $count++;
                        $where .= " {$column} = '$data'";
                    }
                }
            }
            if ($criteria["orderBy"]) {
                $order_by = "ORDER BY {$criteria["column"]} {$criteria["direction"]}";
            }
            if ($criteria["begin"])  $begin = " BEGIN {$begin}";
            if ($criteria["end"])  $end = " END {$end}";
        }
        return tcs_db_query(
            "SELECT 
               ec.*
            FROM autodesk_accounts ec            
            {$where} {$order_by} {$begin} {$end}
            Limit 100"
        );
    }

    private function database_get_subscriptions($customer_id) {
        return tcs_db_query("SELECT * FROM autodesk_subscriptions WHERE endCustomer_id = '{$customer_id}'");
    }
    public function search($customers_id, $email) {
        $params = [":customers_id" => $customers_id, ":email" => $email];
        $cust_search_db = tep_db_query("SELECT * FROM `customers_autodesk` WHERE `customers_email_address` = :email and `customers_id` = :customers_id LIMIT 1", null, $params);
        if (tep_db_num_rows($cust_search_db) > 0) {
            return tep_db_fetch_array($cust_search_db);
        }
        $cust_search = $this->api->search_autodesk_customers(["contactEmail" => $email]);
        ////print_rr($this->debugLog, 'search_autodesk_customers');
        $params = [":customers_id" => $customers_id, ":accountcsn" => $cust_search['body']['results'][0]['csn'], ":email" => $email];
        tep_db_query("INSERT INTO `customers_autodesk` SET `accountCsn` = :accountcsn, `customers_email_address`  = :email, `customers_id` = :customers_id", null, $params);
        return $cust_search;
    }
}


class autodeskCustomerSuccess {
    private AutodeskAPIInterface $api;
    public $debugLog;

    public function __construct($api) {
        $this->api = $api;
    }



    public function get_autodesk_promotions() { /* Configuration*/
        $csv_file_path = DIR_FS_CATALOG . '/feeds/autodesk_promotions.csv'; /* Define the path where you want to save the CSV file        *//* Check if the file exists and is less than a day old*/
        if (file_exists($csv_file_path)) {
            $file_age = time() - filemtime($csv_file_path);
            if ($file_age < 86400) return ['status' => 'Nothing to do', 'response' => 'File is less than a day old.'];
        }
        $get_catalog =  $this->api->get_autodesk_promotions();
        if ($get_catalog['status'] == 'fail') {
            return $get_catalog;
        } else {
            $response = $get_catalog['response'];
        }
        $response_headers = $response['headers'];
        if (is_string($response_headers['Location'][0])) {
            $input_file = file_get_contents($response_headers['Location'][0]);                            /* Download the CSV file and save it to the specified path                */
            file_put_contents($csv_file_path, $input_file);
            return ['status' => 'success', 'response' => $response];
        }
    }

    public function get_opportunity($opportunityNumber = null, $endCustomerCsn = null) {
        return $this->api->get_opportunity($opportunityNumber, $endCustomerCsn);
    }
}

class autodeskOrders {
    private AutodeskAPIInterface $api;
    public $debugLog;

    public function __construct($api) {
        $this->api = $api;
    }
}
function tcs_db_query($query, $params = null) {
    print_rr($query);
    return tep_db_fetch_all(tep_db_query(query: trim(preg_replace('/\s+/', ' ', $query)), params: $params));
}
