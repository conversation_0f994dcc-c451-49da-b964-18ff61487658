<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2014 osCommerce

  Released under the GNU General Public License
*/

require('includes/application_top.php');
require('includes/classes/http_client.php');

// if the customer is not logged on, redirect them to the login page
if (!tep_session_is_registered('customer_id')) {
  $navigation->set_snapshot();
  tep_redirect(tep_href_link('login.php', '', 'SSL'));
}

// if there is nothing in the customers cart, redirect them to the shopping cart page
if ($cart->count_contents() < 1) {
  tep_redirect(tep_href_link('shopping_cart.php'));
}

// if no shipping destination address was selected, use the customers own address as default
if (!tep_session_is_registered('sendto')) {
  tep_session_register('sendto');
  if (@tep_not_null($customer_default_shipping_address_id)) {
    $sendto = $customer_default_shipping_address_id;
  } else {
    $sendto = $customer_default_address_id;
  }
} else {
  // verify the selected shipping address
  if ((is_array($sendto) && empty($sendto)) || is_numeric($sendto)) {
    $check_address_query = tep_db_query("select count(*) as total from " . TABLE_ADDRESS_BOOK . " where customers_id = '" . (int)$customer_id . "' and address_book_id = '" . (int)$sendto . "'");
    $check_address = tep_db_fetch_array($check_address_query);

    if ($check_address['total'] != '1') {
      $sendto = $customer_default_address_id;
      if (tep_session_is_registered('shipping')) tep_session_unregister('shipping');
    }
  }
}
$customer_default_shipping_address_id = $sendto;
$updateShipId_query = tep_db_query("UPDATE customers SET customers_default_shipping_address_id = '" .  (int)$sendto   . "' where customers_id = '" . (int)$customer_id . "'");
if (is_object($updateShipId_query)) {
  $updateShipId = tep_db_fetch_array($updateShipId_query);
}
// if no billing destination address was selected, use the customers own address as default
if (!tep_session_is_registered('billto')) {
  tep_session_register('billto');
  $billto = $customer_default_address_id;
} else {
  // verify the selected billing address
  if ((is_array($billto) && empty($billto)) || is_numeric($billto)) {
    $check_address_query = tep_db_query("select count(*) as total from " . TABLE_ADDRESS_BOOK . " where customers_id = '" . (int)$customer_id . "' and address_book_id = '" . (int)$billto . "'");
    $check_address = tep_db_fetch_array($check_address_query);

    if ($check_address['total'] != '1') {
      $billto = $customer_default_address_id;
      if (tep_session_is_registered('payment')) tep_session_unregister('payment');
    }
  }
}

require('includes/classes/order.php');
$order = new order;

// register a random ID in the session to check throughout the checkout procedure
// against alterations in the shopping cart contents
if (!tep_session_is_registered('cartID')) {
  tep_session_register('cartID');
} elseif (($cartID != $cart->cartID) && tep_session_is_registered('shipping')) {
  tep_session_unregister('shipping');
}

$cartID = $cart->cartID = $cart->generate_cart_id();
/*
// if the order contains only virtual products, forward the customer to the billing page as
// a shipping address is not needed
  if ($order->content_type == 'virtual') {
    if (!tep_session_is_registered('shipping')) tep_session_register('shipping');
    $shipping = false;
    $sendto = false;
    tep_redirect(tep_href_link('checkout_payment.php', '', 'SSL'));
  }
*/
$total_weight = $cart->show_weight();
$total_count = $cart->count_contents();

print_rr($order, "order");

$autodesk_products = [];
$remaining_products = [];
$autodesk_pricing = [
  'sub-total' => 0,
  'vat' => 0,
  'total' => 0
];
$remaining_pricing = [
  'sub-total' => 0,
  'vat' => 0,
  'total' => 0
];
$autodesk_products_output = '';
$remaining_products_output = '';
$rowsTodelete = [];
$has_remaining_products = false;
$has_autodesk_products = false;
for ($i = 0, $n = sizeof($order->products); $i < $n; $i++) {
  $product_subtotal = $order->products[$i]['final_price'] * $order->products[$i]['qty'];
  $product_vat = $order->products[$i]['final_price'] * $order->products[$i]['qty'] * ($order->products[$i]['tax'] / 100);
  $product_total =  $product_subtotal + $product_vat;
  if ($order->products[$i]['manufacturers_id'] != 15) {
    $has_remaining_products = true;
    $remaining_products[$i] = $order->products[$i];
    $remaining_pricing['sub-total'] += $product_subtotal;
    $remaining_pricing['vat'] += $product_vat;
    $remaining_pricing['total'] += $product_total;
    // print_rr($remaining_pricing,'price ' . $i);
    continue;
  }
  $autodesk_products[$i] = $order->products[$i];
  $autodesk_pricing['sub-total'] +=   $product_subtotal;
  $autodesk_pricing['vat'] += $product_vat;
  $autodesk_pricing['total'] += $product_total;
  $autodesk_products_output .= '          <tr>' . "\n" .
    '            <td align="right" valign="top" width="30">' . $autodesk_products[$i]['qty'] . '&nbsp;x&nbsp;</td>' . "\n" .
    '            <td valign="top">' . $autodesk_products[$i]['name'];

  if (STOCK_CHECK == 'true') {
    echo tep_check_stock($autodesk_products[$i]['id'], $autodesk_products[$i]['qty']);
  }

  if ((isset($autodesk_products[$i]['attributes'])) && (sizeof($autodesk_products[$i]['attributes']) > 0)) {
    for ($j = 0, $n2 = sizeof($autodesk_products[$i]['attributes']); $j < $n2; $j++) {
      $autodesk_products_output .= '<br /><nobr><small>&nbsp;<i> - ' . $autodesk_products[$i]['attributes'][$j]['option'] . ': ' . $autodesk_products[$i]['attributes'][$j]['value'] . '</i></small></nobr>';
    }
  }

  $autodesk_products_output .= '</td>' . "\n";

  if (sizeof($order->info['tax_groups']) > 1) $autodesk_products_output .= '            <td valign="top" align="right">' . tep_display_tax_value($autodesk_products[$i]['tax']) . '%</td>' . "\n";

  $autodesk_products_output .= '          <td align="right" valign="top">' . $currencies->display_price($autodesk_products[$i]['final_price'], $autodesk_products[$i]['tax'], $autodesk_products[$i]['qty']) . '</td>' . "\n" .
    '          </tr>' . "\n";

  $has_autodesk_products = true;
}






// load all enabled shipping modules
require('includes/classes/shipping.php');
$shipping_modules = new shipping;

if (defined('MODULE_ORDER_TOTAL_SHIPPING_FREE_SHIPPING') && (MODULE_ORDER_TOTAL_SHIPPING_FREE_SHIPPING == 'true')) {
  $pass = false;

  switch (MODULE_ORDER_TOTAL_SHIPPING_DESTINATION) {
    case 'national':
      if ($order->delivery['country_id'] == STORE_COUNTRY) {
        $pass = true;
      }
      break;
    case 'international':
      if ($order->delivery['country_id'] != STORE_COUNTRY) {
        $pass = true;
      }
      break;
    case 'both':
      $pass = true;
      break;
  }

  $free_shipping = false;

  if (($pass == true) && ($order->info['total'] >= MODULE_ORDER_TOTAL_SHIPPING_FREE_SHIPPING_OVER)) {
    $free_shipping = true;

    include('includes/languages/' . $language . '/modules/order_total/ot_shipping.php');
  }
} else {
  $free_shipping = false;
}


// get all available shipping quotes
$quotes = $shipping_modules->quote();

// if no shipping method has been selected, automatically select the cheapest method.
// if the modules status was changed when none were available, to save on implementing
// a javascript force-selection method, also automatically select the cheapest shipping
// method if more than one module is now enabled
if (!tep_session_is_registered('shipping') || (tep_session_is_registered('shipping') && ($shipping == false) && (tep_count_shipping_modules() > 1))) $shipping = $shipping_modules->cheapest();

require('includes/languages/' . $language . '/checkout.php');

if (defined('SHIPPING_ALLOW_UNDEFINED_ZONES') && (SHIPPING_ALLOW_UNDEFINED_ZONES == 'False') && ! tep_session_is_registered('shipping') && ($shipping == false)) {
  $messageStack->add_session('checkout_address', ERROR_NO_SHIPPING_AVAILABLE_TO_SHIPPING_ADDRESS);
  tep_redirect(tep_href_link('checkout_shipping_address.php', '', 'SSL'));
}

$breadcrumb->add(NAVBAR_TITLE_1, tep_href_link('checkout_shipping.php', '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_2, tep_href_link('checkout_shipping.php', '', 'SSL'));

require('includes/template_top.php');
?>

<div class="page-header">
  <h1 class="h1">Checkout</h1>
</div>
<?php
if (isset($xmasMessage)) { ?>
  <div class="alert alert-danger">
    <?php echo $xmasMessage; ?>
    <div class="clearfix"></div>
  </div><?php }

      if (isset($$payment->form_action_url)) {
        $form_action_url = $$payment->form_action_url;
      } else {
        $form_action_url = tep_href_link('checkout_one_process.php', '', 'SSL');
      }

        ?>
<h2 class="h2"><?php echo TABLE_HEADING_ADDRESSES; ?></h2>
<?php echo tep_draw_form('checkout_process', $form_action_url, 'post', 'class="form-horizontal" onsubmit="return check_form();" id="paymentMethodsForm"', true); ?>


<div class="contentText row">
  <div class="col-sm-4">
    <div class="alert alert-warning">
      <?php echo TEXT_SELECTED_ADDRESSES; ?>
      <div class="clearfix"></div>
      <div class="pull-right">
      </div>
      <div class="clearfix"></div>
    </div>
  </div>
  <div class="col-sm-4">
    <div class="panel panel-primary">
      <div class="panel-heading"><?php echo TITLE_BILLING_ADDRESS; ?></div>
      <div class="panel-body">
        <?php echo tep_address_label($customer_id, $billto, true, ' ', '<br />'); ?>
      </div>
      <div class="panel-footer">
        <?php echo tep_draw_button(IMAGE_BUTTON_CHANGE_ADDRESS, 'fa fa-home', tep_href_link('checkout_payment_address.php', '', 'SSL')); ?>
      </div>
    </div>
  </div>

  <div class="col-sm-4">
    <div class="panel panel-primary">
      <div class="panel-heading">End User/Shipping Address</div>
      <div class="panel-body">
        <?php echo tep_address_label($customer_id, $sendto, true, ' ', '<br />'); ?>
      </div>
      <div class="panel-footer">
        <?php echo tep_draw_button(IMAGE_BUTTON_CHANGE_ADDRESS, 'fa fa-home', tep_href_link('checkout_shipping_address.php', '', 'SSL')); ?>
      </div>
    </div>
  </div>
</div>
<?php /* <div class="clearfix"></div> */ ?>

<div class="contentContainer">
  <div class="contentText">
    <?php
    if ($has_autodesk_products) {

      require('includes/classes/order_total.php');
      $order_total_modules = new order_total;
      $order_total_modules->process();


    ?>

      <h2>Autodesk products</h2>
      <div class="contentText">
        <div class="alert alert-warning">
          <div class="row">
            <div class="col-xs-12">
              These will be billed directly by email from autodesk, see <a target="_blank" href="https://www.cadservices.co.uk/autodesk_new_buying_experience.html">Autodesk new buying experience.</a> for more info. <?php echo $has_remaining_products ? "Scroll down to continue process for remaining products." : ""; ?>
            </div>
          </div>
        </div>
      </div>
    <?php
      $autodesk_products_output_header = '' .
        '<div class="panel panel-default">' .
        '  <div class="panel-heading"><strong>Autodesk Products</strong>' . tep_draw_button(TEXT_EDIT, 'fa fa-edit', tep_href_link('shopping_cart.php'), NULL, NULL, 'pull-right btn-info btn-xs') . '</div>' .
        '  <div class="panel-body">' .
        '     <table width="100%" class="table-hover order_confirmation">' .
        '       <tbody>';

      $autodesk_products_output .= '' .
        '   </tbody>' .
        ' </table>' .
        ' <hr>' .
        ' <table width="100%" class="pull-right">';

      $autodesk_products_output .= '<tbody>' .
        '   <tr>' .
        '        <td align="right" class="main">Sub-Total:</td>' .
        '        <td align="right" class="main" style="width: 10ch">' . $currencies->format($autodesk_pricing['sub-total'], true, $order->info['currency'], $order->info['currency_value']) . ' </td>' .
        '   </tr>' .
        '   <tr>'  .
        '        <td align="right" class="main">UK VAT:</td>' .
        '        <td align="right" class="main">' . $currencies->format($autodesk_pricing['vat'], true, $order->info['currency'], $order->info['currency_value']) . '</td>' .
        '   </tr>' .
        '   <tr>'  .
        '        <td align="right" class="main">Total:</td>' .
        '        <td align="right" class="main"><strong>' . $currencies->format($autodesk_pricing['total'], true, $order->info['currency'], $order->info['currency_value']) . '</strong></td>' .
        '    </tr>';

      $autodesk_products_output .= '</tbody></table>' .
        '     </div>' .
        '  </div>';

      print_rr($autodesk_pricing, "autodesk_pricing");

      echo $autodesk_products_output_header . $autodesk_products_output;
    }
    print_rr($order->products, "products");
    ?>



    <?php
    if (sizeof($remaining_products) > 0) { ?>
      <h2>Remaining products</h2>
      <div class="clearfix"></div>

      <?php
      if (tep_count_shipping_modules() > 0) {
      ?>

        <h2 class="h2">Shipping</h2>

        <div class="contentText">
          
         <?php echo 'foo';?>
          <table class="table table-striped table-condensed table-hover">
            <tbody>

              <?php
              if ($free_shipping == true) {             
                ?>
               <div class="contentText">
                  <div class="panel panel-success">
                    <div class="panel-heading"><strong><?php echo FREE_SHIPPING_TITLE; ?></strong>&nbsp;<?php echo $quotes[$i]['icon']; ?></div>
                    <div class="panel-body">
                      <?php echo sprintf(FREE_SHIPPING_DESCRIPTION, $currencies->format(MODULE_ORDER_TOTAL_SHIPPING_FREE_SHIPPING_OVER)) . tep_draw_hidden_field('shipping', 'free_free'); ?>
                    </div>
                  </div>
                </div>

                <?php
              } else {
               
                for ($i = 0, $n = sizeof($quotes); $i < $n; $i++) {
                  for ($j = 0, $n2 = sizeof($quotes[$i]['methods']); $j < $n2; $j++) {
                    // set the radio button to be checked if it is the method chosen
                    $checked = (($quotes[$i]['id'] . '_' . $quotes[$i]['methods'][$j]['id'] == $shipping['id']) ? true : false);
                ?>
                    <tr class="table-selection">
                      <td>
                        <strong><?php 
                        $remaining_shipping_module = $quotes[$i]['module'];
                        echo $remaining_shipping_module ?></strong>
                        <?php
                        if (isset($quotes[$i]['icon']) && @tep_not_null($quotes[$i]['icon'])) echo '&nbsp;' . $quotes[$i]['icon'];
                        ?>

                        <?php
                        if (isset($quotes[$i]['error'])) {
                          echo '<div class="help-block">'.  $quotes[$i]['error'] . '</div>';
                        }
                        ?>

                        <?php
                        if (@tep_not_null($quotes[$i]['methods'][$j]['title'])) {
                          echo '<div class="help-block">' . 
                          $remaining_shipping_title = '*1' . $quotes[$i]['methods'][$j]['title'];
                          echo $remaining_shipping_title;
                          echo '</div>';
                        }
                        if (@tep_not_null($quotes[$i]['methods'][$j]['displayReason'])) {
                          echo '<div class="help-block">'; 
                          $remaining_shipping_reason = '*2' . $quotes[$i]['methods'][$j]['displayReason'];
                          echo $remaining_shipping_reason;
                          echo '</div>';
                        }

                        ?>
                      </td>
                      <?php
                      if (($n > 1) || ($n2 > 1)) {
                      ?>
                        <td align="right">
                          <?php
                          if (isset($quotes[$i]['error'])) {
                            // nothing
                            echo '&nbsp;';
                          } else {
                            $remaining_shipping_price = $currencies->format(tep_add_tax($quotes[$i]['methods'][$j]['cost'], (isset($quotes[$i]['tax']) ? $quotes[$i]['tax'] : 0))) . tep_draw_hidden_field('shipping', $quotes[$i]['id'] . '_' . $quotes[$i]['methods'][$j]['id']); 
                            $remaining_shipping_price_raw = $quotes[$i]['methods'][$j]['cost'];
                            echo $remaining_shipping_price;      
                          }
                           ?>
                        </td>
                      <?php
                      } else {
                      ?>
                        <td align="right">
                          <?php 
                             $remaining_shipping_price = $currencies->format(tep_add_tax($quotes[$i]['methods'][$j]['cost'], (isset($quotes[$i]['tax']) ? $quotes[$i]['tax'] : 0))) . tep_draw_hidden_field('shipping', $quotes[$i]['id'] . '_' . $quotes[$i]['methods'][$j]['id']); 
                             $remaining_shipping_price_raw = $quotes[$i]['methods'][$j]['cost'];
                             echo $remaining_shipping_price;   
                          ?>
                        </td> 
                      <?php } ?>                  
                    </tr>
              <?PHP
                }
              }
              
            }
              ?>

            </tbody>
          </table>
         <?php echo 'bar';?>
        </div>

      <?php
      }
      ?>
      <hr>
  </div>
  <div class="contentContainer">
    <?php
      if (isset($_GET['payment_error']) && is_object(${$_GET['payment_error']}) && ($error = ${$_GET['payment_error']}->get_error())) {
    ?>

      <div class="contentText">
        <?php echo '<strong>' . tep_output_string_protected($error['title']) . '</strong>'; ?>

        <p class="messageStackError"><?php echo tep_output_string_protected($error['error']); ?></p>
      </div>

    <?php
      }
    ?>
    <h2 class="h2">Payment</h2>
    <?php
      // load all enabled payment modules
      require('includes/classes/payment.php');
      $payment_modules = new payment;
      $selection = $payment_modules->selection();
      if (sizeof($selection) > 1) {
    ?>

      <div class="contentText">
        <div class="alert alert-warning">
          <div class="row">
            <div class="col-xs-8">
              <?php echo TEXT_SELECT_PAYMENT_METHOD; ?>
            </div>
            <div class="col-xs-4 text-right">
              <?php echo '<strong>' . TITLE_PLEASE_SELECT . '</strong>'; ?>
            </div>
          </div>
        </div>
      </div>
      <div class="contentText">
        <div id="paymentError" class="alert alert-danger" style="display:none"></div>
      </div>

    <?php
      } else {
    ?>

      <div class="contentText">
        <div class="alert alert-info"><?php echo TEXT_ENTER_PAYMENT_INFORMATION; ?></div>
      </div>


    <?php
      }
    ?>

    <div id="paymentAccordian" class="contentText">

      <?php
      $radio_buttons = 0;
      for ($i = 0, $n = sizeof($selection); $i < $n; $i++) {
      ?><br style="clear:both">
        <div style="float:left;valign:middle"><?php
                                              // modified by mark to allow hiding of different payment options to prevent customertrs filling in the wrong fields
                                              if (sizeof($selection) > 1) {
                                                echo tep_draw_radio_field('payment', $selection[$i]['id'], '', 'id=' . '"paymentItem' . $i . '" onclick="setAccord(' . $i . ')"');
                                              } else {
                                                echo tep_draw_hidden_field('payment', $selection[$i]['id']);
                                              }
                                              ?></div>
        <div class="panel panel-default" style="margin-left:25px;">
          <div class="accordHeader panel-heading" style="font-size: 14px;" id=<?php echo '"' . $selection[$i]['id'] . 'Header"' ?> colspan="5"><b><?php echo $selection[$i]['module']; ?></b></div>
          <div class="panel-body">
            <?php
            if (isset($selection[$i]['error'])) {
              echo $selection[$i]['error'];
            } elseif (isset($selection[$i]['fields']) && is_array($selection[$i]['fields'])) {

              // modified by mark to allow hiding of different payment options to prevent customertrs filling in the wrong fields  
            ?>
              <table width="100%" border="0" id=<?php echo '"' . $selection[$i]['id'] . 'row"' ?> cellspacing="0" cellpadding="2">
                <?php
                for ($j = 0, $n2 = sizeof($selection[$i]['fields']); $j < $n2; $j++) {
                ?>
                  <tr>
                    <td>
                      <div class="col-lg-6"><?php echo $selection[$i]['fields'][$j]['title']; ?></div>
                      <div class="col-lg-6"><?php echo $selection[$i]['fields'][$j]['field']; ?></div>
                      </dt>
                  </tr>
                <?php
                }
                ?>
              </table>
            <?php
            }
            ?>
          </div>
        </div>

      <?php
        $radio_buttons++;
      }
      ?>
      <h1 class="h2">Final Review</h2>
        <div class="contentContainer">
          <div class="contentText">

            <div class="panel panel-default">
              <div class="panel-heading"><?php echo '<strong>' . HEADING_PRODUCTS . '</strong>' . tep_draw_button(TEXT_EDIT, 'fa fa-edit', tep_href_link('shopping_cart.php'), NULL, NULL, 'pull-right btn-info btn-xs'); ?></div>
              <div class="panel-body">
                <table width="100%" class="table-hover order_confirmation">
                  <tbody>

                    <?php
                    $remaining_products = array_values($remaining_products);
                    for ($i = 0, $n = sizeof($remaining_products); $i < $n; $i++) {
                      echo '          <tr>' . "\n" .
                        '            <td align="right" valign="top" width="30">' . $remaining_products[$i]['qty'] . '&nbsp;x&nbsp;</td>' . "\n" .
                        '            <td valign="top">' . $remaining_products[$i]['name'];

                      if (STOCK_CHECK == 'true') {
                        echo tep_check_stock($remaining_products[$i]['id'], $remaining_products[$i]['qty']);
                      }

                      if ((isset($remaining_products[$i]['attributes'])) && (sizeof($remaining_products[$i]['attributes']) > 0)) {
                        for ($j = 0, $n2 = sizeof($remaining_products[$i]['attributes']); $j < $n2; $j++) {
                          echo '<br /><nobr><small>&nbsp;<i> - ' . $remaining_products[$i]['attributes'][$j]['option'] . ': ' . $remaining_products[$i]['attributes'][$j]['value'] . '</i></small></nobr>';
                        }
                      }

                      echo '</td>' . "\n";

                      if (sizeof($order->info['tax_groups']) > 1) echo '            <td valign="top" align="right">' . tep_display_tax_value($remaining_products[$i]['tax']) . '%</td>' . "\n";

                      echo '            <td align="right" valign="top">' . $currencies->display_price($remaining_products[$i]['final_price'], $remaining_products[$i]['tax'], $remaining_products[$i]['qty']) . '</td>' . "\n" .
                        '          </tr>' . "\n";
                    }
                    ?>


                  </tbody>
                </table>
                <hr>
                <?php
                $remaining_shipping_output = '';            
                if (@$remaining_shipping_price != null) {
                  $remaining_shipping_output = '<tr>'  .
                    '        <td align="right" class="main">' . $remaining_shipping_module . ': </td>' .
                    '        <td align="right" class="main">' . $remaining_shipping_price . '</td>' .
                    '   </tr>';
                  $shipping_pricing_vat += $remaining_shipping_price_raw * ($order->products[$i]['tax'] / 100);
                  $remaining_pricing['vat'] += $shipping_pricing_vat;
                  $remaining_pricing['total'] += $remaining_shipping_price_raw + $shipping_pricing_vat;
                }
                ?>
                <table width="100%" class="pull-right">
                  <?php
                    $remaining_products_output = '<tbody>' .
                    '   <tr>' .
                    '        <td align="right" class="main">Sub-Total:</td>' .
                    '        <td align="right" class="main" style="width: 10ch">' . $currencies->format($remaining_pricing['sub-total'], true, $order->info['currency'], $order->info['currency_value']) . ' </td>' .
                    '   </tr>' .
                    $remaining_shipping_output .
                    '   <tr>'  .
                    '        <td align="right" class="main">UK VAT:</td>' .
                    '        <td align="right" class="main">' . $currencies->format($remaining_pricing['vat'], true, $order->info['currency'], $order->info['currency_value']) . '</td>' .
                    '   </tr>' .
                    '   <tr>'  .
                    '        <td align="right" class="main">Total:</td>' .
                    '        <td align="right" class="main"><strong>' . $currencies->format($remaining_pricing['total'], true, $order->info['currency'], $order->info['currency_value']) . '</strong></td>' .
                    '    </tr></tbody>';
                  echo $remaining_products_output;
                  ?>
                </table>
              </div>
            </div>
          </div>
        </div>
    </div>
  <?php } ?>



  <hr>
  <h2 class="h2">Comments</h2>

  <div class="contentText">
    <div class="form-group">
      <label for="inputComments" class="control-label col-sm-4"><?php echo TABLE_HEADING_COMMENTS; ?></label>
      <div class="col-sm-8">
        <?php
        echo tep_draw_textarea_field('comments', 'soft', 60, 5, $comments, 'id="inputComments" placeholder="' . TABLE_HEADING_COMMENTS . '"');
        ?>
      </div>
    </div>
  </div>

  <div class="buttonSet">
    <div class="text-right">
      <?php
      /*  if (is_array($payment_modules->modules)) {
        echo $payment_modules->process_button();
      }*/
      echo tep_draw_button(IMAGE_BUTTON_FINALISE_ORDER, 'fas fa-check-circle', null, 'primary', null, 'btn-success btn-block btn-lg');
      ?>
    </div>
  </div>

  <?php if ($has_remaining_products) { ?>
    <div class="well  well-lg">
      <div class="alert alert-info">Surcharge is applied to credit card payments only, other payment methods such debit card, bank transfer etc are not subject to surcharge.</div>
      <div class="alert alert-success">**With ref to <strong>Consumer Rights Regulations</strong> (payment of surcharges), the ban on surcharges does not apply to payments made using commercial, business or corporate credit cards and to business to business contracts. Most of our goods are for business and commercial sector but if your purchase is consumer related then please state in <strong>comments section</strong> below in order to avoid surcharges as we can offer alternative payment options such as; payment by proforma invoice, debit card, bank transfer or cheque. To checkout for now, select “Call me to take payment details option.</div>
    </div>

  <?php } ?>


  <div class="clearfix"></div>

  </div>
</div>
</form>
<div class="modal fade" id="paymentMethodChangeModal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title">Change Payment Method</h4>
      </div>
      <div class="modal-body">
        <p>You have entered Credit/Debit card details, if you change to another payment method they will be lost upon clicking Continue.</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" id="keepCCBtn" data-dismiss="modal">Keep Credit Card Selected</button>
        <button type="button" class="btn btn-primary" data-dismiss="modal">Change Payment Method</button>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<div class="modal fade" id="ccErrorModal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title">Change Payment Method</h4>
      </div>
      <div class="modal-body">
        <p>There are errors on your credit card entry please check and try again.</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" data-dismiss="modal">Close</button>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<?php
require('includes/template_bottom.php');
require('includes/application_bottom.php');
?>