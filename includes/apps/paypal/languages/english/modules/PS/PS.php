module_ps_title = PayPal Payments Standard
module_ps_short_title = Payments Standard
module_ps_public_title = PayPal (including Credit and Debit Cards)

module_ps_legacy_admin_app_button = Manage App

module_ps_introduction = <ul>
  <li>Accept credit cards and PayPal on your online store.</li>
  <li>Simplified PCI compliance standards.</li>
  <li>Accept multiple currencies worldwide.</li>
  <li>Optimized mobile checkout experience.</li>
</ul>

<p>PayPal handles the payment acceptance experience and returns the customer to your store after payment has been made.</p>
<p>For new buyers, signing up for a PayPal account is optional meaning customers can complete their payments first and then decide to save their information in a PayPal account for future purchases.</p>

module_ps_info_auto_return_url = If Auto Return for Website Payments is enabled in your PayPal Profile account settings, the following URL must be used as the Return URL:<br /><br />:auto_return_url

module_ps_error_curl = This module requires cURL to be enabled in PHP and will not load until it has been enabled on this webserver.
module_ps_error_credentials = This module will not load until the Seller E-Mail Address parameter has been configured. Please edit and configure the settings of the PayPal App.
module_ps_error_credentials_pdt_api = This module will not load until the PDT Identity Token or API Credentials have been configured. The PDT Identity Token can be entered in the Payments Standard configuration page, or the API Credentials can be entered in the Credentials configuration page of the PayPal App.

module_ps_button_return_to_store = Back to :storename

module_ps_error_invalid_transaction = Could not verify the PayPal transaction. Please try again.
