cfg_ssl_version_title = SSL Version
cfg_ssl_version_desc = Use the default cURL configured SSL version connection setting when making API calls to PayPal's servers or force TLS v1.2 connections.

cfg_ssl_version_default = Default
cfg_ssl_version_tls12 = TLS v1.2

cfg_ssl_version_button_connection_test = Test Connection
cfg_ssl_version_button_more_info = More Info

cfg_ssl_version_dialog_connection_test_title = SSL Connection Test
cfg_ssl_version_dialog_button_more_info = More Info
cfg_ssl_version_dialog_button_close = Close
cfg_ssl_version_dialog_processing = Processing, please wait..
cfg_ssl_version_dialog_curl_version = cURL Version:
cfg_ssl_version_dialog_curl_ssl_version = cURL SSL Version:
cfg_ssl_version_dialog_default_setting = Default Setting:
cfg_ssl_version_dialog_tlsv12_setting = TLS v1.2:
cfg_ssl_version_dialog_success = Success
cfg_ssl_version_dialog_failed = Failed
cfg_ssl_version_dialog_default_failed = A connection to PayPal could not be made using the default cURL configured SSL version.<br /><br />The SSL Version parameter must be set to TLS v1.2 by <b>June 30, 2017</b> otherwise connections will continue to fail.
cfg_ssl_version_dialog_tlsv12_failed = A connection to PayPal could not be made using TLS v1.2. Please consult with your hosting provider to upgrade the cURL version that is installed with your PHP web server to support TLS v1.2 connections.<br /><br />This must be performed by <b>June 30, 2017</b> otherwise connections will continue to fail.
cfg_ssl_version_dialog_general_error = A general error occurred. Please try again.
