button_apply_update = Apply Update
button_applying_updates_progress = Applying Updates &hellip;
button_check_for_updates = Check for Updates
button_retrieving_progress = Retrieving &hellip;
button_visit_app_page = Visit App at osCommerce

retrieving_update_list = Retrieving update availability list &hellip;
manual_update_available = v:version is available as an update! This can be downloaded and applied manually from the osCommerce Apps site.
no_updates_available = No updates are currently available.
invalid_update_list_format = Could not read the update availability list. Please try again.
could_not_request_update_list = The update availability list could not be requested. Please try again.
applying_updates_heading = Applying Updates
applying_updates_success = Updates have been applied successfully!
downloading_version_progress = Downloading v:version &hellip;
applying_version_progress = Applying v:version &hellip;
error_applying_heading = Error: Could not apply v:version!
error_applying = An error occured during this update.
error_applying_start = Could not start the procedure to apply the update. Please try again.
error_heading = Error!
error_download_start = Could not start the procedure to download v:version. Please try again.
no_updates_found = No versions could be found to update to. Please try again.

error_saving_download = Could not download the update package to the following location. Please delete the file if it exists and try again.<br /><br />:filepath
error_download_directory_permissions = The required permissions on the following directory is not correctly set. Please update the permissions to allow write access.<br /><br />:filepath

log_update_started = #### UPDATE TO v:version STARTED
log_updated_file = Updated: :filepath
log_update_success = #### UPDATE TO v:version COMPLETED
log_update_failed = #### UPDATE TO v:version FAILED
log_error_files = **** ERROR: Could not update the following files. Please update the file and directory permissions to allow write access.
log_error_extraction = **** ERROR: Update package extraction failed.
log_error_verification = **** ERROR: Update package verification failed.
log_error_invalid = **** ERROR: Invalid update package contents.
log_error_nonexisting = **** ERROR: Update package does not exist: :filepath
