onboarding_intro_title = Retrieve API Credentials

onboarding_intro_body = <p>Start selling and earning faster by allowing osCommerce to automatically and securely configure your online store with your PayPal API credentials.</p>
<p>Don't have a PayPal Account? We can do this for new and existing PayPal sellers.</p>
<p style="text-align: center;">:button_retrieve_live_credentials :button_retrieve_sandbox_credentials</p>
<p>Live PayPal Accounts are for live shops ready to accept payments. Sandbox PayPal Accounts are used for testing purposes where orders are processed but no actual payments are made.</p>

manage_credentials_title = Manage API Credentials

manage_credentials_body = <p>Enter your PayPal API Credentials and start selling with PayPal.</p>
<p>:button_manage_credentials</p>

button_manage_credentials = Manage Your API Credentials

alert_onboarding_initialization_error = Could not initiate the start account procedure. Please try again in a short while.
alert_onboarding_connection_error = Could not connect to the osCommerce website to initiate the start account procedure. Please try again in a short while.
alert_onboarding_account_type_error = Please select to start with a Live or Sandbox account.
alert_onboarding_retrieve_error = Could not retrieve the account credentials. Please try again in a short while.
alert_onboarding_retrieve_connection_error = Could not connect to the osCommerce website to retrieve the PayPal account credentials. Please try again in a short while.

alert_onboarding_success = PayPal account credentials have been successfully configured.
