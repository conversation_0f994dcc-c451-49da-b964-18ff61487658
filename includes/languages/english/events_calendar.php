<?php
/*
*/


  define('NAVBAR_TITLE', 'Events Calendar');
  define('HEADING_TITLE', 'Events Calendar');
  define('TEXT_SELECT_EVENT', 'Select event');
  
  define('TEXT_EVENTS_FOR', 'Events for ');
  define('TEXT_EVENT_DATE', 'Date :');
  define('TEXT_EVENTS_START', 'Events starting on ');
  define('TEXT_EVENT_START_DATE', 'From :');
  define('TEXT_EVENT_END_DATE', 'Through :');
  define('TEXT_EVENT_LINK', 'Link :');
  define('TEXT_EVENT_OSC_LINK', '');
  define('TEXT_EVENT_TITLE', 'Title :');
  define('TEXT_EVENT_DESCRIPTION', 'Event :');
  define('TEXT_EVENT_MORE', '... More >>');
  define('TEXT_NO_EVENTS', 'No Events found.');
  define('TEXT_OTHER_EVENTS', 'Other events for today :');

  define('TEXT_DISPLAY_NUMBER_OF_EVENTS', 'Displaying event n� <b>%d</b><b>%_</b> of <b>%d</b> for ');
  define('TEXT_DISPLAY_NUMBER_OF_PAGES', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> events) ');
  define('MAX_DISPLAY_PAGE_LINKS', 5);
  define('MAX_DISPLAY_NUMBER_EVENTS', 5);
  

  define('BOX_CALENDAR_TITLE', ' View all events');
  define('BOX_GO_BUTTON', 'go');
  define('BOX_GO_BUTTON_TITLE', ' submit - month view ');
  define('BOX_TODAY_BUTTON', ' T ');
  define('BOX_TODAY_BUTTON_TITLE', ' view this month ');
  define('BOX_YEAR_VIEW_BUTTON', ' Y ');
  define('BOX_YEAR_VIEW_BUTTON_TITLE', ' year view ');
  define('BOX_NEXT_LINK', 'next_month');
  define('BOX_PREV_LINK', 'previous_month');
  define('BOX_CLICK_LINK', 'Click for events');
  
  define('FIRST_DAY_OF_WEEK', 0);// First day of the week  1=Monday 0=Sunday;

  define('NUMBER_OF_YEARS', 5);  //n� of years to be shown in drop-down
  
  define('MONTHS_SHORT_ARRAY', 'JAN,FEB,MAR,APR,MAY,JUN,JUL,AUG,SEP,OCT,NOV,DEC');
  

  
?>
