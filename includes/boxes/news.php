<?php
/*
  $Id: news.php, v 1.0 2009/01/27 17:00:00 hpdl Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
  Addition to contribution by Lunaxod
  <EMAIL> url:www.charger.od.ua
*/
?>
          <tr>
            <td>
<?php
  if ($_GET['action'] == 'reply') {
   $id = (int)tep_db_prepare_input($_POST['id']);
   $name = tep_db_prepare_input($_POST['name']);
   $content = tep_db_prepare_input2($_POST['content']);
   $username = tep_db_prepare_input($_POST['username']);
   $email = tep_db_prepare_input($_POST['email']);
   $date = date("Y-m-d G:i:s");
   if (NEWS_APPROVED == 'true') { 
        $approved = 0;
      } else {
      $approved = 1;
     }
}
   $content = strip_tags($content, '<p>');

  require('includes/languages/' . $language . '/news.php');

  $info_box_contents = array();
  $info_box_contents[] = array('text' => BOX_HEADING_NEWS);

  new infoBoxHeading($info_box_contents, false, false, tep_href_link('news.php'));

    $listing_sql = "select n.id, n.date_created, nd.name, nd.content, n.replys from " . TABLE_NEWS . " n, " . TABLE_NEWS_DESC . " nd where nd.language_id = '" . (int)$languages_id . "' and n.id = nd.news_id";


  $info_box_contents = array();
 if ($_GET['date'] == true) {
    $month_date = tep_db_prepare_input($_GET['date']);
    $listing_sql .= " and month_date = '$month_date'";
}
    $listing_sql .= " order by id desc";
    $listing_split = new splitPageResults($listing_sql, 5);
     if ($listing_split->number_of_rows > 0) {
    $listing_query = tep_db_query($listing_split->sql_query);
    while ($listing = tep_db_fetch_array($listing_query)) {
  $info_box_contents[] = array('text' => '<a href="' . tep_href_link('news.php', 'article=' . $listing['id']) . '">' . $listing['name'] . ' <i>' . tep_date_short($listing['date_created']) . '</i></a><br>');
 }
}


  new infoBox($info_box_contents);
?>
            </td>
          </tr>