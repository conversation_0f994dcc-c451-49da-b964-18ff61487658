<?php
require_once("application_top.php");

$tableName = "products_autodesk_catalog";


// Drop table if exists
$sql = "DROP TABLE IF EXISTS $tableName";
tep_db_query($sql);

// Create table
$sql = "CREATE TABLE $tableName (
    offeringName VARCHAR(255),
    offeringCode VARCHAR(255),
    offeringId VARCHAR(255),
    intendedUsage_code VARCHAR(255),
    intendedUsage_description VARCHAR(255),
    accessModel_code VARCHAR(255),
    accessModel_description VARCHAR(255),
    servicePlan_code VARCHAR(255),
    servicePlan_description VARCHAR(255),
    connectivity_code VARCHAR(255),
    connectivity_description VARCHAR(255),
    term_code VARCHAR(255),
    term_description VARCHAR(255),
    lifeCycleState VARCHAR(255),
    renewOnlyDate DATE,
    discontinueDate DATE,
    orderAction VARCHAR(255),
    specialProgramDiscount_code VARCHAR(255),
    specialProgramDiscount_description VARCHAR(255),
    fromQty INT,
    toQty INT,
    currency VARCHAR(255),
    SRP FLOAT,
    costAfterSpecialProgramDiscount FLOAT,
    renewalDiscountPercent FLOAT,
    renewalDiscountAmount FLOAT,
    costAfterRenewalDiscount FLOAT,
    transactionVolumeDiscountPercent FLOAT,
    transactionVolumeDiscountAmount FLOAT,
    costAfterTransactionVolumeDiscount FLOAT,
    serviceDurationDiscountPercent FLOAT,
    serviceDurationDiscountAmount FLOAT,
    costAfterServiceDurationDiscount FLOAT,
    effectiveStartDate DATE,
    effectiveEndDate DATE,
    changeFlag VARCHAR(255)
)";

tep_db_query($sql);

// Open CSV file
$csvFile = fopen(DIR_WS_HTTP_CATALOG . 'ProductCatalog.csv', 'r');
if ($csvFile === FALSE) {
    die("Error opening CSV file.");
}

// Get the first row (header)
$header = fgetcsv($csvFile);
if ($header === FALSE) {
    die("Error reading CSV header.");
}

// Prepare insert statement dynamically
$columns = implode(", ", $header);
$placeholders = implode(", ", array_fill(0, count($header), "?"));

$sql = "INSERT INTO $tableName ($columns) VALUES ($placeholders)";
tep_db_query($sql);

// Dynamically bind parameters
$params = [];
foreach ($header as $column) {
    $params[] = &$data[$column];
}
call_user_func_array([$stmt, 'bind_param'], array_merge(["" . str_repeat("s", count($header))], $params));

// Read CSV file and insert data into table
while (($row = fgetcsv($csvFile)) !== FALSE) {
    foreach ($header as $index => $column) {
        $data[$column] = $row[$index];
    }
    $stmt->execute();
}

// Close connections
$stmt->close();
$conn->close();
fclose($csvFile);

echo "Data imported successfully.";
?>
