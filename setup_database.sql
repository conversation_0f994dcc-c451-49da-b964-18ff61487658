-- Database Setup for Local Development
-- Run this in phpMyAdmin or MySQL command line

-- Create the local development database
CREATE DATABASE IF NOT EXISTS `cadservices_local` 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create a dedicated user for the application (optional, for better security)
-- CREATE USER 'cadservices_user'@'localhost' IDENTIFIED BY 'your_password_here';
-- GRANT ALL PRIVILEGES ON cadservices_local.* TO 'cadservices_user'@'localhost';
-- FLUSH PRIVILEGES;

-- Use the database
USE `cadservices_local`;

-- Basic tables that your osCommerce application likely needs
-- Note: You should import your actual database structure here

-- Example: If you have a database dump, import it like this:
-- SOURCE /path/to/your/database_dump.sql;

-- Or if you need to create basic osCommerce tables, uncomment below:
/*
CREATE TABLE IF NOT EXISTS `configuration` (
  `configuration_id` int(11) NOT NULL AUTO_INCREMENT,
  `configuration_title` varchar(255) NOT NULL,
  `configuration_key` varchar(255) NOT NULL,
  `configuration_value` text NOT NULL,
  `configuration_description` varchar(255) NOT NULL,
  `configuration_group_id` int(11) NOT NULL,
  `sort_order` int(5) DEFAULT NULL,
  `last_modified` datetime DEFAULT NULL,
  `date_added` datetime NOT NULL,
  `use_function` varchar(255) DEFAULT NULL,
  `set_function` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`configuration_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Add more tables as needed based on your application requirements
*/

-- Show databases to confirm creation
SHOW DATABASES;
