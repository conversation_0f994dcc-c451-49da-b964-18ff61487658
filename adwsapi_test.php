<?php

require_once "includes/application_top.php";
require_once "includes/classes/autodesk_wsapi_guzzle.php";
require_once "vendor/autoload.php";

//connect to test database


/*{
"id":"512936b1-a31b-4870-9412-0b76d9a6fcdd"
"topic":"quote-status"
"event":"changed"
"sender":"PWS Quote Status"
"publishedAt":"2024-08-08T13:44:49.355Z"
"csn":"5103159758"
"environment":"stg"
"payload":{
"quoteNumber":"Q-00859"
"quoteStatus":"Ordered"
"transactionId":"13847669-f969-4e2a-bfba-a9ac3c051a21"
"message":"Quote# Q-01522 status changed to Ordered."
"modifiedAt":"2024-08-08T13:44:49.355Z"
}
}*/


// Get headers
$headers = [];
foreach ($_SERVER as $name => $value) {
    if (substr($name, 0, 5) == 'HTTP_') {
        $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value;
    }
}

// Get the raw POST data
$raw_post_data = '{
        "id":"4963e1cd-055f-4f7f-9d78-be1847b9a6a7",
        "topic":"product-catalog",
        "event":"changed",
        "sender":"PWS Catalog Service",
        "environment":"stg",
        "payload":{
        "message":"Catalog was updated",
        "modifiedAt":"2024-08-14T14:00:46.944Z"
        },
        "country":"GB",
        "publishedAt":"2024-08-14T14:07:56.000Z"
}';

// Decode the JSON payload
$json_payload = json_decode($raw_post_data, true);

switch ($json_payload['topic']) {
    case 'quote-status':
        switch ($json_payload['event']) {
            case 'created':                
                break;
            case 'changed':
                break;
            case 'deleted':
                break;
        }
        $quoteNumber = $json_payload['payload']['quoteNumber'];
        $quoteStatus = $json_payload['payload']['quoteStatus'];
        $transactionId = $json_payload['payload']['transactionId'];
        $message = $json_payload['payload']['message'];
        $modifiedAt = $json_payload['payload']['modifiedAt'];
        $csn = $json_payload['csn'];
        if (isset($transactionId) OR isset($quoteNumber)) {
            $query_sql = ("
                UPDATE autodesk_quotes SET
                `quoteStatus` = '" . $quoteStatus . "',
                `transactionId` = '" . $transactionId . "',
                `quoteNumber` = '" . $quoteNumber . "',
                `message` = '" . $message . "',
                `modifiedAt` = '" . $modifiedAt . "'
                WHERE `transactionId` = '" . $transactionId . "'
                OR `quoteNumber` = '" . $quoteNumber . "'
            ");
            $response = ['query_sql' => $query_sql, 'query' => tep_db_query($query_sql)];
        }
        break;
    case 'product-catalog':
        switch ($json_payload['event']) {            
            case 'changed':
                $autodesk = new AutodeskAPI(); 
                $response = $autodesk->get_autodesk_product_catalog();
            break;        
        }
        break;
    default:
        $jsondecded = json_encode($json_payload);
        $response = ['status' => 'Nothing to do', 'topic' => $json_payload['topic'], 'payload' => $jsondecded, 'json_payload' => $raw_post_data];
        break;
}
header('Content-type: application/json');
echo json_encode($response);
?>