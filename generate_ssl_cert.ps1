# PowerShell SSL Certificate Generation Script
# Run this as Administrator: powershell -ExecutionPolicy Bypass -File generate_ssl_cert.ps1

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "SSL Certificate Generation (PowerShell)" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$XamppPath = "E:\tools\xampp"
$OpenSSLPath = "$XamppPath\apache\bin\openssl.exe"
$CertDir = "$XamppPath\apache\conf\ssl.crt"
$KeyDir = "$XamppPath\apache\conf\ssl.key"

# Check if OpenSSL exists
if (-not (Test-Path $OpenSSLPath)) {
    Write-Host "ERROR: OpenSSL not found at $OpenSSLPath" -ForegroundColor Red
    Write-Host "Please check your XAMPP installation." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Create directories
Write-Host "Creating certificate directories..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path $CertDir | Out-Null
New-Item -ItemType Directory -Force -Path $KeyDir | Out-Null

# Create temporary OpenSSL configuration
$ConfigContent = @"
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = GB
ST = Local
L = Local
O = Local Development
OU = IT Department
CN = localhost.cadservices

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost.cadservices
DNS.2 = localhost
IP.1 = 127.0.0.1
"@

$TempConfig = "$env:TEMP\openssl_temp.cnf"
$ConfigContent | Out-File -FilePath $TempConfig -Encoding ASCII

Write-Host ""
Write-Host "Generating SSL certificate..." -ForegroundColor Yellow
Write-Host ""

# Change to OpenSSL directory
Set-Location "$XamppPath\apache\bin"

# Generate certificate
$Arguments = @(
    "req", "-x509", "-nodes", "-days", "365", "-newkey", "rsa:2048",
    "-keyout", "$KeyDir\localhost.key",
    "-out", "$CertDir\localhost.crt",
    "-config", $TempConfig
)

try {
    & $OpenSSLPath $Arguments
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "SSL Certificate generated successfully!" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "Certificate: $CertDir\localhost.crt" -ForegroundColor White
        Write-Host "Private Key: $KeyDir\localhost.key" -ForegroundColor White
        Write-Host ""
        Write-Host "The certificate includes:" -ForegroundColor Cyan
        Write-Host "- Common Name: localhost.cadservices" -ForegroundColor White
        Write-Host "- Subject Alternative Names: localhost.cadservices, localhost, 127.0.0.1" -ForegroundColor White
        Write-Host "- Valid for: 365 days" -ForegroundColor White
        Write-Host ""
        Write-Host "Next steps:" -ForegroundColor Cyan
        Write-Host "1. Restart Apache in XAMPP Control Panel" -ForegroundColor White
        Write-Host "2. Visit https://localhost.cadservices" -ForegroundColor White
        Write-Host "3. Accept the security warning in your browser" -ForegroundColor White
    } else {
        throw "OpenSSL command failed with exit code $LASTEXITCODE"
    }
} catch {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Red
    Write-Host "ERROR: Failed to generate SSL certificate" -ForegroundColor Red
    Write-Host "========================================" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Troubleshooting:" -ForegroundColor Yellow
    Write-Host "1. Make sure XAMPP is installed at: $XamppPath" -ForegroundColor White
    Write-Host "2. Check if OpenSSL exists at: $OpenSSLPath" -ForegroundColor White
    Write-Host "3. Try running PowerShell as Administrator" -ForegroundColor White
    Write-Host "4. Check Windows Defender or antivirus isn't blocking the operation" -ForegroundColor White
} finally {
    # Clean up temporary config file
    if (Test-Path $TempConfig) {
        Remove-Item $TempConfig -Force
    }
}

Write-Host ""
Read-Host "Press Enter to continue"
