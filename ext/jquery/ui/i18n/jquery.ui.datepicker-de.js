/* German initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON> (<EMAIL>). */
jQuery(function($){
	$.datepicker.regional['de'] = {
		closeText: '<PERSON><PERSON><PERSON>ßen',
		prevText: '&#x3C;Zurück',
		nextText: 'Vor&#x3E;',
		currentText: 'Heute',
		monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','April','<PERSON>','Juni',
		'Juli','August','September','Oktober','November','Dezember'],
		monthNamesShort: ['Jan','Feb','<PERSON><PERSON><PERSON>','Apr','<PERSON>','Jun',
		'Jul','Aug','Sep','Okt','Nov','Dez'],
		dayNames: ['Sonntag','Montag','Dienstag','Mittwoch','<PERSON><PERSON>tag','Freitag','Samstag'],
		dayNamesShort: ['So','<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>','Sa'],
		dayNamesMin: ['So','<PERSON>','<PERSON>','Mi','Do','Fr','Sa'],
		weekHeader: 'KW',
		dateFormat: 'dd.mm.yy',
		firstDay: 1,
		isRTL: false,
		showMonthAfterYear: false,
		yearSuffix: ''};
	$.datepicker.setDefaults($.datepicker.regional['de']);
});
