/* Italian initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON> (<EMAIL>). */
jQuery(function($){
	$.datepicker.regional['it'] = {
		closeText: '<PERSON><PERSON>',
		prevText: '&#x3C;Prec',
		nextText: 'Succ&#x3E;',
		currentText: 'Oggi',
		monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','Aprile','<PERSON><PERSON>','<PERSON><PERSON><PERSON>',
			'<PERSON><PERSON>o','<PERSON><PERSON><PERSON>','<PERSON>tem<PERSON>','<PERSON><PERSON>','Novembre','Dicembre'],
		monthNamesShort: ['Gen','Feb','Mar','Apr','Mag','Giu',
			'Lug','Ago','Set','Ott','Nov','Dic'],
		dayNames: ['<PERSON>nica','Luned<PERSON>','<PERSON><PERSON><PERSON>','Mercoled<PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>'],
		dayNamesShort: ['<PERSON>','Lun','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','V<PERSON>','Sab'],
		dayNamesMin: ['<PERSON>','<PERSON>','Ma','Me','Gi','Ve','Sa'],
		weekHeader: 'Sm',
		dateFormat: 'dd/mm/yy',
		firstDay: 1,
		isRTL: false,
		showMonthAfterYear: false,
		yearSuffix: ''};
	$.datepicker.setDefaults($.datepicker.regional['it']);
});
