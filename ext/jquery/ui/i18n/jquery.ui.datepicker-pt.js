/* Portuguese initialisation for the jQuery UI date picker plugin. */
jQuery(function($){
	$.datepicker.regional['pt'] = {
		closeText: '<PERSON><PERSON><PERSON>',
		prevText: 'Anterior',
		nextText: '<PERSON><PERSON><PERSON>',
		currentText: '<PERSON><PERSON>',
		monthNames: ['<PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>',
		'<PERSON><PERSON>','Agos<PERSON>','Setem<PERSON>','Outubro','Novembro','<PERSON>zemb<PERSON>'],
		monthNamesShort: ['Jan','Fev','<PERSON>','Ab<PERSON>','<PERSON>','<PERSON>',
		'Jul','<PERSON><PERSON>','<PERSON>','Out','Nov','Dez'],
		dayNames: ['<PERSON>','<PERSON>-feira','<PERSON><PERSON><PERSON>-feira','Quarta-feira','Quinta-feira','Sexta-feira','S<PERSON>bad<PERSON>'],
		dayNamesShort: ['Dom','Seg','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON><PERSON>'],
		dayNamesMin: ['<PERSON>','Seg','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>ui','<PERSON>','<PERSON>áb'],
		weekHeader: 'Sem',
		dateFormat: 'dd/mm/yy',
		firstDay: 0,
		isRTL: false,
		showMonthAfterYear: false,
		yearSuffix: ''};
	$.datepicker.setDefaults($.datepicker.regional['pt']);
});
