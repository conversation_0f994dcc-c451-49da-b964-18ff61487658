/* Finnish initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON> (<EMAIL>). */
jQuery(function($){
	$.datepicker.regional['fi'] = {
		closeText: '<PERSON><PERSON>',
		prevText: '&#xAB;Edellinen',
		nextText: '<PERSON><PERSON><PERSON>&#xBB;',
		currentText: 'Tän<PERSON>än',
		monthNames: ['<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON>ak<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>'],
		monthNamesShort: ['<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>'],
		dayNamesShort: ['<PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON>'],
		dayNames: ['<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON>ii<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>antai'],
		dayNamesMin: ['Su','Ma','Ti','Ke','To','Pe','La'],
		weekHeader: 'Vk',
		dateFormat: 'd.m.yy',
		firstDay: 1,
		isRTL: false,
		showMonthAfterYear: false,
		yearSuffix: ''};
	$.datepicker.setDefaults($.datepicker.regional['fi']);
});
