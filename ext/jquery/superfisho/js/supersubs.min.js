!function(a){a.fn.supersubs=function(b){var c=a.extend({},a.fn.supersubs.defaults,b);return this.each(function(){var b=a(this),d=a.meta?a.extend({},c,b.data()):c,e=b.find("ul").show(),f=a('<li id="menu-fontsize">&#8212;</li>').css({padding:0,position:"absolute",top:"-999em",width:"auto"}).appendTo(b)[0].clientWidth;a("#menu-fontsize").remove(),e.each(function(b){var c=a(this),e=c.children(),g=e.children("a"),h=e.css("white-space","nowrap").css("float");c.add(e).add(g).css({float:"none",width:"auto"});var i=c[0].clientWidth/f;i+=d.extraWidth,i>d.maxWidth?i=d.maxWidth:i<d.minWidth&&(i=d.minWidth),i+="em",c.css("width",i),e.css({float:h,width:"100%","white-space":"normal"}).each(function(){var b=a(this).children("ul"),c=void 0!==b.css("left")?"left":"right";b.css(c,"100%")})}).hide()})},a.fn.supersubs.defaults={minWidth:9,maxWidth:25,extraWidth:0}}(jQuery);