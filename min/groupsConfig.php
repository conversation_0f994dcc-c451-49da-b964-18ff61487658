<?php
/**
 * Groups configuration for default Minify implementation
 * @package Minify
 */

/** 
 * You may wish to use the Minify URI Builder app to suggest
 * changes. http://yourdomain/min/builder/
 *
 * See http://code.google.com/p/minify/wiki/CustomSource for other ideas
 **/

return array(
    // 'js' => array('//js/file1.js', '//js/file2.js'),
    // 'css' => array('//css/file1.css', '//css/file2.css'),
	  'js.js' => array(
				'//ext/jquery/jquery-3.1.1.min.js', 
				'//ext/jquery/ui/jquery-ui.min.js', 
                '//ext/jquery/superfish/js/jquery.hoverIntent.minified.js', 
                '//ext/jquery/superfish/js/superfish.js', 
                '//ext/jquery/superfish/js/supersubs.js', 
                '//ext/bootstrap/js/bootstrap.min.js', 
                '//ext/jquery/cookie.js',
				'//includes/misc.js',
				'//ext/liteyoutubejsModule/lite-yt-embed.js'
      ),
      'css.css' => array(
				'//ext/bootstrap/css/bootstrap.min.css',
				'//ext/css/offCanvas.css',
                '//ext/font-awesome/4.6.1/css/font-awesome.min.css', 
                '//ext/jquery/superfish/css/superfish.css', 
                '//ext/jquery/superfish/css/superfish-vertical.css', 
                '//custom.css', 
                '//user.css',
				'//ext/liteyoutubejsModule/lite-yt-embed.css'
      ),
      'admin.js' => array( 
                '//ext/jquery/jquery-2.2.3.min.js',
                '//ext/jquery/ui/jquery-ui-1.10.4.min.js',
                '//ext/flot/jquery.flot.min.js',
                '//ext/flot/jquery.flot.time.min.js',
                '//ext/bootstrap/js/bootstrap.min.js',
                '//baffletrain/autocadlt/includes/general.js',				
                '//baffletrain/autocadlt/includes/misc.js',
                '//baffletrain/autocadlt/ext/clipboard.min.js'
				
      ),
        'admin.css' => array(  
				'//ext/jquery/ui/redmond/jquery-ui-1.10.4.min.css',				
				'//ext/bootstrap/css/bootstrap.min.css',
                '//baffletrain/autocadlt/includes/stylesheet.css'
                
      )           
     );