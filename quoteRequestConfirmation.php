<?php


  require('includes/application_top.php');

  require('includes/languages/' . $language . '/contact_us.php');
  
function check_email_address($email) {
  // First, we check that there's one @ symbol, 
  // and that the lengths are right.
  if (!preg_match("[^[^@]{1,64}@[^@]{1,255}$]", $email)) {
    // Email invalid because wrong number of characters 
    // in one section or wrong number of @ symbols.
    return false;
  }
  // Split it into sections to make life easier
  $email_array = explode("@", $email);
  $local_array = explode(".", $email_array[0]);
	  for ($i = 0; $i < sizeof($local_array); $i++) {
		if
	(!preg_match("[^(([A-Za-z0-9!#$%&'*+/=?^_`{|}~-][A-Za-z0-9!#$%&?'*+/=?^_`{|}~\.-]{0,63})|(\"[^(\\|\")]{0,62}\"))$]",
	$local_array[$i])) {
		  return false;
		}
	  }
	  // Check if domain is IP. If not, 
	  // it should be valid domain name
	  if (!preg_match("^\[?[0-9\.]+\]?$", $email_array[1])) {
		$domain_array = explode(".", $email_array[1]);
		if (sizeof($domain_array) < 2) {
			return false; // Not enough parts to domain
		}
		for ($i = 0; $i < sizeof($domain_array); $i++) {
		  if
	(!preg_match("[^(([A-Za-z0-9][A-Za-z0-9-]{0,61}[A-Za-z0-9])|?([A-Za-z0-9]+))$]",
	$domain_array[$i])) {
			return false;
		  }
		}
	  }
	  $emailNotEntered = 1;
	}
/*
 if (isset($_GET['action']) && ($_GET['action'] == 'quote') ) {
    $error = false;

    $name = tep_db_prepare_input($_POST['queryFrmName']);
    $email_address = tep_db_prepare_input($_POST['queryFrmEmail']);
    $enquiry = tep_db_prepare_input($_POST['queryFrmEnquiry']);
	$product = tep_db_prepare_input($_POST['queryFrmProduct']);
	$phone = tep_db_prepare_input($_POST['queryFrmPhone']);

	
	
    if (!tep_validate_email($email_address)) {
      $error = true;

      $messageStack->add('contact', ENTRY_EMAIL_ADDRESS_CHECK_ERROR);
    }

    $actionRecorder = new actionRecorder('ar_contact_us', (tep_session_is_registered('customer_id') ? $customer_id : null), $name);
    if (!$actionRecorder->canPerform()) {
      $error = true;

      $actionRecorder->record(false);

      $messageStack->add('contact', sprintf(ERROR_ACTION_RECORDER, (defined('MODULE_ACTION_RECORDER_CONTACT_US_EMAIL_MINUTES') ? (int)MODULE_ACTION_RECORDER_CONTACT_US_EMAIL_MINUTES : 15)));
    }

   // if ($error == false) {
      tep_mail(STORE_OWNER, '<EMAIL>', 'Quote Request', $enquiry, $name, $email_address);

      $actionRecorder->record();
      tep_redirect("https://www.cadservices.co.uk/response.php");
      //tep_redirect(tep_href_link('contact_us.php', 'action=success'));
   // }
  }
    */
    
if ($_POST['queryFrmName']){
	$firstEqryRun=0;
	$name = tep_db_prepare_input($_POST['queryFrmName']);
    $email_address = tep_db_prepare_input($_POST['queryFrmEmail']);
    $enquiry = tep_db_prepare_input($_POST['queryFrmEnquiry']);
	$product = tep_db_prepare_input($_POST['queryFrmProduct']);
	$phone = tep_db_prepare_input($_POST['queryFrmPhone']);
	$extra= $_POST['queryFrmextra'];
	
	if ($name== ''){
		$nameNotEntered = 1;
	} else {
		$nameNotEntered = 0;
	}
	$message = $_POST['queryFrmMessage'];
	$extraCorrect = 0;
	if(strtolower(str_replace(' ','',$_POST['queryFrmextra'])) == 'ybzq'){
            $extraCorrect = 1;
            $form_details =  "Name: " . $name . "
            Name: " . $name . "
            Phone: " . $phone . "
            Product: " . $product . "
            E-Mail: " .  $email_address . " 
            Message: 
            " . $enquiry;
		$recipient = '<EMAIL>'; // Email address for sending the information
		$subject = 'Enquiry from cadservices.co.uk'; // Displays the subject in Outlook Express
		$from = $_POST['queryFrmEmail']; // Displays the email address in Outlook Express
		mail($recipient, $subject, $form_details, "FROM: $from \n");
		tep_redirect("https://www.cadservices.co.uk/response.php");
	}
	
}

require('includes/template_top.php');
?>
<script type="text/javascript">
    $(document).ready(function() {
        $("#fancyForm").modal('toggle');
    });
</script>
 <?php include("includes/fancyForm.php");?>

<?php
  require('includes/template_bottom.php');
  require('includes/application_bottom.php');
?>

