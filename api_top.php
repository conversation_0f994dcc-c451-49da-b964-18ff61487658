<?php
/*
  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com
*/

// start the timer for the page parse time log
define('PAGE_PARSE_START_TIME', microtime());
// set the level of error reporting
error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED & ~E_STRICT);

// load server configuration parameters
if (file_exists('includes/local/configure.php')) {
  include('includes/local/configure.php');
} else {
  include('includes/configure.php');
}

// define the project version
define('PROJECT_VERSION', 'osCommerce Online Merchant v2.3');

// set the type of request (secure or not)
$request_type = (getenv('HTTPS') == 'on') ? 'SSL' : 'NONSSL';

// set php_self in the local scope
$req = parse_url($_SERVER['SCRIPT_NAME']);
$PHP_SELF = substr($req['path'], ($request_type == 'NONSSL') ? strlen(DIR_WS_HTTP_CATALOG) : strlen(DIR_WS_HTTPS_CATALOG));

if ($request_type == 'NONSSL') {
  define('DIR_WS_CATALOG', DIR_WS_HTTP_CATALOG);
} else {
  define('DIR_WS_CATALOG', DIR_WS_HTTPS_CATALOG);
}

// include the list of project database tables
require('includes/database_tables.php');

// include the database functions
require('includes/functions/database.php');

// make a connection to the database
tep_db_connect() or die('Unable to connect to database server!');

// set the application parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
  define($configuration['cfgKey'], $configuration['cfgValue']);
}

// Compression handling - keep if needed for API responses
if (CHEMO_COMPRESS_ENABLE == 'true') {
  require('includes/functions/chemo_compress.php');
  ob_start('chemoCompress');
}

// define general functions used application-wide
require('includes/functions/general.php');
require('includes/functions/html_output.php');

// include currencies class and create an instance
require('includes/classes/currencies.php');
$currencies = new currencies();

// set the language
if (!tep_session_is_registered('language') || isset($_GET['language'])) {
  if (!tep_session_is_registered('language')) {
    tep_session_register('language');
    tep_session_register('languages_id');
  }

  include('includes/classes/language.php');
  $lng = new language();

  if (isset($_GET['language']) && @tep_not_null($_GET['language'])) {
    $lng->set_language($_GET['language']);
  } else {
    $lng->get_browser_language();
  }

  $language = $lng->language['directory'];
  $languages_id = $lng->language['id'];
}

// include the language translations
$_system_locale_numeric = setlocale(LC_NUMERIC, 0);
require('includes/languages/' . $language . '.php');
setlocale(LC_NUMERIC, $_system_locale_numeric);

// currency default
$currency = 'GBP';

// set default currency if not already set
if (!tep_session_is_registered('currency')) {
  tep_session_register('currency');
  $currency = DEFAULT_CURRENCY;
}
