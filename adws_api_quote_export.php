<?php
use autodesk_api\AutodeskAPI;
if (!isset($path['fs_app_root'])) $path['fs_app_root'] = __DIR__ . "/baffletrain/autocadlt/autobooks/";
require_once $path['fs_app_root'] . "resources/system/startup_sequence.php";

// Set up the log file path

$csv_file_path = __DIR__ . '/feeds/quotes.csv';
// Connect to the test database (add your connection code here)
AutodeskAPI::log_message("Connecting to the test database...");

// Get headers
$headers = [];
foreach ($_SERVER as $name => $value) {
    if (substr($name, 0, 5) == 'HTTP_') {
        $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value;
    }
}
AutodeskAPI::log_message("Headers retrieved: " . json_encode($headers));

// Get the raw POST data
$raw_post_data = file_get_contents('php://input');
AutodeskAPI::log_message("Raw POST data received.");

// Decode the JSON payload
$json_payload = json_decode($raw_post_data, true);
AutodeskAPI::log_message("JSON payload decoded: " . json_encode($json_payload));

header('Content-type: application/json');
if (!empty($json_payload['result']['downloadLink'])) {
    if (is_string($json_payload['result']['downloadLink'])) {
        AutodeskAPI::log_message("Download link found: " . $json_payload['result']['downloadLink']);
        try {
            $autodesk = new AutodeskAPI();
            AutodeskAPI::log_message("AutodeskAPI instance created.");
            $download = false;      
            AutodeskAPI::log_message("looking for file: " . $csv_file_path );      
            if (file_exists($csv_file_path)) {
                $file_age = time() - filemtime($csv_file_path);
                AutodeskAPI::log_message("file found: " . $csv_file_path . " - age: " . $file_age);
                if ($file_age > 86400) {
                    $download = true;                    
                }else{
                    AutodeskAPI::log_message("using cached file" ); 
                }
            } else {
                $download = true;
            }
            AutodeskAPI::log_message("Download: " . $download ? "true" : "false");
            if ($download) {
                $returned = $autodesk->quotes->download_autodesk_quote_export($json_payload['result']['downloadLink']);
                AutodeskAPI::log_message("Download initiated successfully: {$returned['status']} - {$returned['response']}" );
            }
            AutodeskAPI::log_message("importing");
            $import = AutodeskAPI::import_csv_into_database(
                mapping: $autodesk->quotes->get_quote_column_mapping(),
                csv_file_path: $csv_file_path,
                debug: true);
            AutodeskAPI::log_message("Import initiated successfully: {$import['status']}" );
            AutodeskAPI::log_message($import['response']);
        } catch (Exception $e) {
            AutodeskAPI::log_message("Error initiating download: " . $e->getMessage());
        }
    } else {
        AutodeskAPI::log_message("Invalid download link format.");
    }
} else {
    AutodeskAPI::log_message("No download link provided in payload.");
}
?>