# Local Development Environment Setup

This guide will help you set up a local development environment for your PHP application using XAMPP.

## Prerequisites

- XAMPP installed at `E:\tools\xampp\`
- PHP installed at `E:\tools\php\` (optional, can use XAMPP's PHP)
- Project files at `E:\Build\httpdocs\`
- Administrator access to modify system files

## Quick Setup

1. **Run the setup script:**
   ```batch
   local_dev_setup.bat
   ```

2. **Follow the manual steps outlined by the script**

## Manual Setup Steps

### 1. Configure Apache Virtual Host

1. Open `E:\tools\xampp\apache\conf\extra\httpd-vhosts.conf`
2. Add the contents from `xampp-vhost-config.conf`
3. Open `E:\tools\xampp\apache\conf\httpd.conf`
4. Uncomment the line: `Include conf/extra/httpd-vhosts.conf`

### 2. Add Host Entry

1. Open `C:\Windows\System32\drivers\etc\hosts` as Administrator
2. Add this line: `127.0.0.1 localhost.cadservices`

### 3. Generate SSL Certificate

**Option A: Simple Batch Script (Recommended)**
```batch
generate_ssl_cert_simple.bat
```

**Option B: PowerShell Script (if batch fails)**
```powershell
powershell -ExecutionPolicy Bypass -File generate_ssl_cert.ps1
```

**Option C: Original Script (if OpenSSL config exists)**
```batch
generate_ssl_cert.bat
```

**Option D: Manual Generation**
```batch
cd E:\tools\xampp\apache\bin
openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout ../conf/ssl.key/localhost.key -out ../conf/ssl.crt/localhost.crt -subj "/C=GB/ST=Local/L=Local/O=Local Development/CN=localhost.cadservices"
```

### 4. Set Up Database

1. Start XAMPP Control Panel
2. Start Apache and MySQL services
3. Open phpMyAdmin: http://localhost/phpmyadmin
4. Run the SQL commands from `setup_database.sql`
5. Import your existing database structure/data

### 5. Configure Application

**Option A: Use Local Configuration**
```batch
copy includes\configure_local.php includes\configure.php
```

**Option B: Modify Existing Configuration**
Edit `includes/configure.php` and change:
- `DB_SERVER` to `127.0.0.1`
- `DB_SERVER_USERNAME` to `root`
- `DB_SERVER_PASSWORD` to `` (empty)
- `DB_DATABASE` to `cadservices_local`
- `HTTP_SERVER` to `http://localhost.cadservices`
- `HTTPS_SERVER` to `https://localhost.cadservices`

### 6. Install Dependencies

If you haven't already, install Composer dependencies:
```batch
cd E:\Build\httpdocs
composer install
```

### 7. Set Directory Permissions

Ensure these directories are writable:
- `temp/`
- `download/`
- `pub/`
- `logs/`

### 8. Test Your Setup

1. Start XAMPP services (Apache + MySQL)
2. Visit: https://localhost.cadservices
3. Run configuration check: https://localhost.cadservices/php_config_check.php

## Troubleshooting

### Common Issues

**Apache won't start:**
- Check if port 80/443 are in use
- Check Apache error logs: `E:\tools\xampp\apache\logs\error.log`

**SSL Certificate errors:**
- Accept the self-signed certificate in your browser
- Or add the certificate to your trusted certificates

**Database connection errors:**
- Ensure MySQL service is running
- Check database credentials in configuration
- Verify database exists

**Permission errors:**
- Run XAMPP Control Panel as Administrator
- Check directory permissions for temp, download, pub folders

### Log Files

- Apache Error Log: `E:\tools\xampp\apache\logs\error.log`
- Apache Access Log: `E:\tools\xampp\apache\logs\access.log`
- MySQL Error Log: `E:\tools\xampp\mysql\data\mysql_error.log`
- PHP Error Log: Check `php.ini` for `error_log` setting

## Development Tips

1. **Enable PHP Error Reporting:**
   Add to your local configuration:
   ```php
   ini_set('display_errors', 1);
   error_reporting(E_ALL);
   ```

2. **Use Different Database:**
   Keep your local database separate from production to avoid conflicts.

3. **Version Control:**
   Add `includes/configure.php` to `.gitignore` to avoid committing local settings.

4. **Backup:**
   Always backup your database before making changes.

## Files Created

- `xampp-vhost-config.conf` - Apache virtual host configuration
- `includes/configure_local.php` - Local development configuration
- `local_dev_setup.bat` - Automated setup script
- `generate_ssl_cert.bat` - SSL certificate generation
- `setup_database.sql` - Database setup script
- `php_config_check.php` - PHP configuration checker

## Next Steps

After setup is complete:
1. Test all application functionality
2. Set up debugging tools (Xdebug, etc.)
3. Configure your IDE for the project
4. Set up automated testing environment
