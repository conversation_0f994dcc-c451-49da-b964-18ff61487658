<?php

  define('PAGE_NOT_FOUND', 'Requested page not found');
  define('NOT_FOUND', 'Not Found');

  require('includes/application_top.php');

  $current_page = 'define_content.php';

  if(isset($_REQUEST['page'])){  
	  $curPage = tep_db_input($_REQUEST['page']);	  
	  $curPage_query = tep_db_query("SELECT id, page_url, seo_title,seo_description,seo_keywords, page_title, page_content, login_view FROM " . TABLE_DEFINE_CONTENT . " WHERE page_url = '" . $curPage . "' AND status='yes' AND language_id =" . (int)$languages_id);	
  }
  
  if(isset($curPage) && tep_db_num_rows($curPage_query) > 0){
	  
	  $curPageData = tep_db_fetch_array($curPage_query);
	  
	  if(!tep_session_is_registered('customer_id') && ( $curPageData['login_view'] == 'yes') ) {  
		  $navigation->set_snapshot();
		  $messageStack->add_session(NEED_LOGIN, 'warning');
    	  tep_redirect(tep_href_link('login.php', '', 'SSL'));
	  }
	  
	  $breadcrumb->add($curPageData['page_title'], tep_href_link('pages.php', 'page=' . $curPageData['page_url']));
	  
  }else{
	  
	  $curPageData['page_title'] = NOT_FOUND;
	  
	  $curPageData['page_content'] = PAGE_NOT_FOUND;
	  
	  $breadcrumb->add('Not Found');
  }
  define('META_SEO_KEYWORDS', $curPageData['seo_keywords']);
  define('META_SEO_DESCRIPTION', $curPageData['seo_description']);
   if(@tep_not_null($curPageData['seo_title'])){
	$oscTemplate->setTitle($curPageData['seo_title']);
  }else{
	$oscTemplate->setTitle($curPageData['page_title']);
  }
  require('includes/template_top.php');
  
  
?>

<h1><?php echo $oscTemplate->getTitle() ?></h1>

<div class="contentContainer">
  <div class="contentText PageContent">
    <?php echo stripslashes($curPageData['page_content']); ?>
  </div>

  <div class="buttonSet">
    <span class="buttonAction"><?php echo tep_draw_button(IMAGE_BUTTON_CONTINUE, 'triangle-1-e', tep_href_link('index.php')); ?></span>
  </div>
</div>

<?php
  require('includes/template_bottom.php');
  require('includes/application_bottom.php');
?>
