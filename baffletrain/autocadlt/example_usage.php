<?php
/**
 * Example usage of the new attribute dependency checking functionality
 *
 * This file demonstrates how to use the new check_dependency_issues() method
 * that has been moved to the tcs_product_attributes class for reuse elsewhere.
 */

require_once("includes/classes/attributes.class.php");

// Example 1: Basic usage for a specific product
function check_product_dependencies($product_id) {
    // Create an instance of the attributes class
    $products_attributes = new tcs_product_attributes($product_id);

    // Check for dependency issues using the class method
    $issues = $products_attributes->check_dependency_issues();

    if (empty($issues)) {
        echo "No dependency issues found for product {$product_id}\n";
        return true;
    } else {
        echo "Found " . count($issues) . " dependency issues for product {$product_id}:\n";
        foreach ($issues as $issue) {
            echo "- {$issue['type']}: {$issue['message']}\n";
        }
        return false;
    }
}

// Example 2: Batch checking multiple products
function check_multiple_products($product_ids) {
    $results = [];

    foreach ($product_ids as $product_id) {
        $products_attributes = new tcs_product_attributes($product_id);
        $issues = $products_attributes->check_dependency_issues();

        $results[$product_id] = [
            'has_issues' => !empty($issues),
            'issue_count' => count($issues),
            'issues' => $issues
        ];
    }

    return $results;
}

// Example 3: API endpoint for dependency checking
function api_check_dependencies() {
    $product_id = $_GET['product_id'] ?? null;

    if (!$product_id) {
        return json_encode(['error' => 'Product ID required']);
    }

    try {
        $products_attributes = new tcs_product_attributes($product_id);
        $issues = $products_attributes->check_dependency_issues();

        return json_encode([
            'success' => true,
            'product_id' => $product_id,
            'has_issues' => !empty($issues),
            'issues' => $issues
        ]);
    } catch (Exception $e) {
        return json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}

// Example 4: Integration with product validation
function validate_product_before_save($product_id) {
    $products_attributes = new tcs_product_attributes($product_id);
    $issues = $products_attributes->check_dependency_issues();

    $validation_errors = [];

    foreach ($issues as $issue) {
        switch ($issue['type']) {
            case 'missing_dependency':
                $validation_errors[] = "Dependency Error: " . $issue['message'];
                break;
            case 'circular_dependency':
                $validation_errors[] = "Circular Dependency Error: " . $issue['message'];
                break;
            case 'sort_order_violation':
                $validation_errors[] = "Sort Order Error: " . $issue['message'];
                break;
            case 'missing_variation':
                $validation_errors[] = "Missing Variation Error: " . $issue['message'];
                break;
            case 'default_conflict':
                $validation_errors[] = "Default Conflict Error: " . $issue['message'];
                break;
        }
    }

    return $validation_errors;
}

// Example usage:
if (isset($_GET['example'])) {
    switch ($_GET['example']) {
        case 'single':
            $product_id = $_GET['product_id'] ?? 1;
            check_product_dependencies($product_id);
            break;

        case 'multiple':
            $product_ids = [1, 2, 3, 4, 5]; // Example product IDs
            $results = check_multiple_products($product_ids);
            print_r($results);
            break;

        case 'api':
            header('Content-Type: application/json');
            echo api_check_dependencies();
            break;

        case 'validate':
            $product_id = $_GET['product_id'] ?? 1;
            $errors = validate_product_before_save($product_id);
            if (empty($errors)) {
                echo "Product {$product_id} validation passed!\n";
            } else {
                echo "Product {$product_id} validation failed:\n";
                foreach ($errors as $error) {
                    echo "- {$error}\n";
                }
            }
            break;
    }
}

?>
