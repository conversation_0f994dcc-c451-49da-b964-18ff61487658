<?php
/**
 * Test script for the new dependency checks:
 * - Check whether there is a variation for a particular attribute
 * - Check whether the defaults can all be displayed at once
 */

require_once("includes/classes/attributes.class.php");

function test_new_dependency_checks($product_id) {
    echo "Testing new dependency checks for product ID: {$product_id}\n";
    echo str_repeat("=", 60) . "\n";
    
    // Create an instance of the attributes class
    $products_attributes = new tcs_product_attributes($product_id);
    
    // Check for all dependency issues including the new ones
    $issues = $products_attributes->check_dependency_issues();
    
    if (empty($issues)) {
        echo "✅ No dependency issues found for product {$product_id}\n";
        return true;
    }
    
    echo "Found " . count($issues) . " dependency issues for product {$product_id}:\n\n";
    
    // Group issues by type for better display
    $grouped_issues = [];
    foreach ($issues as $issue) {
        $grouped_issues[$issue['type']][] = $issue;
    }
    
    // Display each type of issue
    foreach ($grouped_issues as $type => $type_issues) {
        echo get_issue_type_header($type) . "\n";
        foreach ($type_issues as $issue) {
            echo "  • {$issue['message']}\n";
        }
        echo "\n";
    }
    
    return false;
}

function get_issue_type_header($type) {
    switch ($type) {
        case 'missing_dependency':
            return "🔗 Missing Dependencies:";
        case 'circular_dependency':
            return "🔄 Circular Dependencies:";
        case 'sort_order_violation':
            return "📊 Sort Order Violations:";
        case 'missing_variation':
            return "🎯 Missing Variations:";
        case 'default_conflict':
            return "⚠️  Default Conflicts:";
        default:
            return "❓ Unknown Issue Type ({$type}):";
    }
}

function test_specific_checks($product_id) {
    echo "\nTesting specific new checks for product ID: {$product_id}\n";
    echo str_repeat("-", 60) . "\n";
    
    $products_attributes = new tcs_product_attributes($product_id);
    
    // Test if product has variations
    if ($products_attributes->has_variations) {
        echo "✅ Product has variations - will check for missing variations\n";
    } else {
        echo "ℹ️  Product has no variations - skipping variation checks\n";
    }
    
    // Test if product has multiple default attributes
    $default_attributes = $products_attributes->default_attributes;
    if (count($default_attributes) > 1) {
        echo "✅ Product has multiple default attributes - will check for conflicts\n";
        echo "   Default attributes: " . count($default_attributes) . "\n";
    } else {
        echo "ℹ️  Product has " . count($default_attributes) . " default attribute(s) - no conflict checking needed\n";
    }
    
    echo "\n";
}

// Example usage
if (isset($_GET['product_id'])) {
    $product_id = (int)$_GET['product_id'];
    test_new_dependency_checks($product_id);
    test_specific_checks($product_id);
} else {
    echo "Usage: test_new_dependency_checks.php?product_id=123\n";
    echo "\nThis script tests the new dependency checks:\n";
    echo "1. Missing Variations: Checks if attributes have corresponding product variations\n";
    echo "2. Default Conflicts: Checks if default attributes can be displayed together\n";
    echo "\nExample URLs:\n";
    echo "- test_new_dependency_checks.php?product_id=1\n";
    echo "- test_new_dependency_checks.php?product_id=5\n";
}

?>
