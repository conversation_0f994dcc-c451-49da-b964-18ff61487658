<?php

  class tcs_product_attributes {
	  
		public $attributes = array();
		
		public $options_values = array();

		public $variations = array();

		public $has_attributes = false;

		public $has_variations = false;

		public $default_attributes = array();

		public $selected_attributes = array();

		public $selected_variation = array();

		public $input_attributes;

		public $isPreset = false;
		
		
		
		
    function __construct($products_id = '',$languages_id = 1,$input_attributes = null) {
		$this->products_id = explode('{', $products_id,2)[0];
		$this->products_id = $this->products_id;
		$this->languages_id = $languages_id;
		$this->isPreset = true;
		if (isset($input_attributes)){
			if (is_array($input_attributes)){
				$this->input_attributes = $input_attributes;			
			} else if (is_string($input_attributes)){
				$this->input_attributes = $this->parse_attribute_string($input_attributes);		
			} else if (is_numeric(strpos($products_id,'{'))) {
				$this->input_attributes = $this->parse_attribute_string('{' . explode('{', $products_id,2)[1]);			
			} else {
				$this->isPreset = false;
			}
		}else{
			$this->isPreset = false;
		}
	//print_rr($this);
		$q = tep_db_query("SELECT * FROM `products_attributes` where `products_id` = " . $this->products_id);
		$this->has_attributes = tep_db_num_rows($q) > 0 ? true : false;
		
		$q = tep_db_query("SELECT * FROM `products_variations` where `products_id` = " . $this->products_id);
		$this->has_variations = tep_db_num_rows($q) > 0 ? true : false;
    }

	
	private function init_attributes(){
		$output = array();
		$options_values=array();
		$products_options_sql= "  
			select distinct 
				popt.products_options_id,
				popt.products_options_name,
				patrib.attribute_default,
				patrib.products_attributes_id
			from
				products_options popt, 
				products_attributes patrib 
			where 
				patrib.products_id='" . (int)$this->products_id . "' and 
				patrib.options_id = popt.products_options_id and 
				popt.language_id = '" . (int)$this->languages_id . "' 
			order by 
				patrib.products_attributes_sort_order";

		$products_options_query = tep_db_query($products_options_sql);
		$count = 0;
		while ($products_options = tep_db_fetch_array($products_options_query)) {
			$attribute_default = $products_options["attribute_default"] == 1 ? true : false;
			$output[$products_options['products_options_id']] = Array(
				"products_options_name" => $products_options['products_options_name'],
				"attribute_default" => $attribute_default,
				"values" => array()
			);
			$products_options_values_sql = "
				select 
					pov.products_options_values_id, 
					pov.products_options_values_name,
					pa.options_values_price, 
					pa.price_prefix, 
					pa.attribute_default,
					pa.dependson_options_id, 
					pa.dependson_options_values_id,
					pa.products_attributes_sort_order
				from
					products_attributes pa, 
					products_options_values pov 
				where 
					pa.products_id = '" . (int)$this->products_id . "' 
					and pa.options_id = '" . (int)$products_options['products_options_id'] . "' 
					and pa.options_values_id = pov.products_options_values_id and pov.language_id = '" . (int)$this->languages_id . "' 
					order by pa.products_attributes_sort_order";
					
			////echo $products_options_values_sql;
			$products_options_values_query = tep_db_query($products_options_values_sql);
			$i = 0;
			while ($products_options_values = tep_db_fetch_array($products_options_values_query)) {	
				$value = array(
					"enabled" => 0,
					"products_attributes_id" => $products_options['products_attributes_id'],
					"products_options_values_name" => $products_options_values['products_options_values_name'],
					"options_values_price" =>  $products_options_values['options_values_price'], 
					"price_prefix" =>  $products_options_values['price_prefix'],  
					"attribute_default" =>  $products_options_values['attribute_default'], 
					"dependson_options_id" =>  $products_options_values['dependson_options_id'], 
					"dependson_options_values_id" =>  $products_options_values['dependson_options_values_id'],
					"products_attributes_sort_order" => $products_options_values['products_attributes_sort_order']
				);
				$output[$products_options['products_options_id']]['values'][$products_options_values['products_options_values_id']] = $value;
				$options_values[$products_options_values['products_options_values_id']] = $value;
				if ($products_options_values['attribute_default'] == 1 || $i == 0){ 
					$this->default_attributes[$products_options['products_options_id']] = $products_options_values['products_options_values_id'];
				}
				$i++;
			}
			//set default attrib
			$output[$products_options['products_options_id']]['values'][$this->default_attributes[$products_options['products_options_id']]]["attribute_default"] = 1;
		}
		$this->attributes = $output;
		$this->options_values = $options_values;
		$this->selected_attributes = $this->get_current_selected_attributes();
		return $this->attributes;
	}
	
	public function get_attributes(){
		if (empty($this->attributes)) $this->init_attributes();
		if (empty($this->variations)) $this->init_variations();
		foreach ($this->attributes as $keya=>$attributes){
			foreach ($attributes['values'] as $keyb=>$options){
				if ($options['enabled'] == 0){
					unset($this->attributes[$keya]['values'][$keyb]);
				}
			}
			if (empty($this->attributes[$keya]['values'])){
				unset($this->attributes[$keya]);
			}
		}
		return $this->attributes;
	}
 
	private function init_variations(){
		if (empty($this->attributes)) $this->init_attributes();
		$output = array();
		$variations_sql = "select * from products_variations pv	WHERE products_id = '" . $this->products_id . "' 
		
		order by sort_order";
		$variations_query = tep_db_query($variations_sql);
		if(tep_db_num_rows($variations_query)){
			while ($variations_values = tep_db_fetch_array($variations_query)) {
				$attributes = $this->parse_attribute_string($variations_values['attributes']);
				$product_name_suffix = $this->generate_product_suffix($attributes);
				//$product_url = tep_href_link('product_info.php', 'products_id=' . $this->products_id . $variations_values['attributes']);
				$product_image_url = "";
				if (@tep_not_null($variations_values['image_id'])){
					$values = tep_db_query("select image from products_images where id = '" . $variations_values['image_id'] . "'");
					
					if (tep_db_num_rows($values) > 0){
						$values_values = tep_db_fetch_array($values);
						$product_image_url = "https://www.cadservices.co.uk/images/" . $values_values["image"];
					}
				} 
				$output[] = array(
					'products_variations_id' => $variations_values['products_variations_id'],
					'attribute_string' => $variations_values['attributes'],
					'product_name_suffix' => $product_name_suffix,
					'model' => $variations_values['model'],
					'price' => $variations_values['price'],
					'gtin' => $variations_values['gtin'],
					'image_url' => $product_image_url,
					'image_id' => $variations_values['image_id'],
					'attributes'=> $attributes
				);
			}
		}		
		$this->variations = $output;
		// enable attributes that have variations
		foreach ($output as $variation){
			foreach ($variation['attributes'] as $key=>$attrib){
				$this->attributes[$key]['values'][$attrib]['enabled'] = 1;
			}			
		}
		
		return $this->variations;
	}
	
	public function get_variations(){
		if ($this->has_variations){
			$this->init_variations();
		}
		return $this->variations;
	}
	
	function get_current_selected_attributes(){
		if 	($this->isPreset){
			$attribs = $this->get_attributes_from_array($this->input_attributes);
		} else {
			$attribs = $this->get_attributes_from_array($this->default_attributes);
		}
		return $attribs;
	}

	function get_current_selected_variation(){
		if 	($this->isPreset){
			$attribs = $this->input_attributes;			
		}else{
			$attribs = $this->default_attributes;
		}	
		$variation = $this->get_variation_from_array($attribs);
		if ($variation) return $variation; else return false;
	}

	function get_price(){		
		$attribs = $this->input_attributes;			
		$variation = $this->get_variation_from_array($attribs);
		$thePrice = $variation['price'];
		if (isset($thePrice) && $thePrice > 0 )
			return $thePrice;
		else 
			return false;
	}




	function get_attributes_from_array($attribArray){
		$output = array();		
	//print_rr( $this->attributes,'this->attributes ');
	//print_rr($attribArray,'this attribs: ');
		foreach($attribArray as $aKey => $attrib){
			$output[$aKey][$attrib] = $this->attributes[$aKey]['values'][$attrib];
		}
		
		return $output;
	}

	function get_variation_from_attrib($a){
		if(!is_array($a)){
			$array = $this->parse_attribute_string($a);
		} else {
			$array = $a;
		}
		return $this->get_variation_from_array($array);
	}
		

	function get_variation_from_array($attribs){
		if($this->has_variations){
			if (empty($this->variations)) $this->init_variations();		
			foreach ($this->variations as $variations) {
				$dbAttrib = $this->parse_attribute_string($variations['attribute_string']);
				//////print_rr($dbAttrib);
				if (!$this->compare_attributes($attribs,$dbAttrib)){
					continue;
				} else {
					$product_name_suffix = $this->generate_product_suffix($dbAttrib);
					$product_image_url = "";
					if (@tep_not_null($variations['image_id'])){
						$values = tep_db_query("select image from products_images where id = '" . $variations['image_id'] . "'");
						if (tep_db_num_rows($values) > 0){
							$values_values = tep_db_fetch_array($values);
							$product_image_url = $values_values["image"];
						}
					} 
					return array(
						'variation_id' => $variations['products_variations_id'],
						'variation_string' => $variations['attribute_string'],
						'product_name_suffix' => $product_name_suffix,
						'model' => $variations['model'],
						'price' => $variations['price'],
						'gtin' => $variations['gtin'],
						'image' => $product_image_url,
						'image_id' => $variations['image_id']
					);
				}
			}
		}		
		return false;
	}
	
	
	
	function parse_attribute_string($string){
		if (is_numeric(strpos($string, '{'))){
			$attributes = explode('{', substr($string, strpos($string, '{')+1));
			
			$attributes_array = array();
			for ($i=0, $n=sizeof($attributes); $i<$n; $i++) {
				$pair = explode('}', $attributes[$i]);
				$attributes_array[$pair[0]] = $pair[1];
			}
			//print_rr($attributes_array);
			return $attributes_array;
		}else{
			return false;
		}
	}
	
	function generate_product_suffix($attributes, $separator = ', '){
		$product_name_suffix = "";
		if (!is_array($attributes)){
			$attributes = $this->parse_attribute_string($attributes);
			if(!$attributes) return false;
		}
		
		foreach($attributes as $attrib){
			$product_name_suffix .= $separator . $this->values_name($attrib);
		}
		return $product_name_suffix;
	}
	
	function options_name($options_id) {
		if (isset($options_id)){
			return $this->attributes[$options_id]['products_options_name'];
		}
		return false;
	}

	function values_name($values_id) {
		if (isset($values_id)) return $this->options_values[$values_id]['products_options_values_name'];
		return false;
	}
  function compare_attributes($a, $b) {
		if (!is_array($a)) {
			$a_array = $this->parse_attribute_string($a);
		} else {
			$a_array = $a;
		}
		if (!is_array($b)) {
			$b_array = $this->parse_attribute_string($b);
		} else {
			$b_array = $b;
		}
	//print_rr($a_array);
		usort($a_array, array($this,'attributes_customSort'));
		usort($b_array, array($this,'attributes_customSort'));
		if (serialize($a_array) == serialize($b_array)){
			return true;
		} else {
			return false;
		}
	}
	function attributes_customSort($a, $b) {
		// Compare the first value of each array entry
		//print_rr($a);
		$cmp = $a[0] - $b[0];
		// If the first values are equal, compare the second values
		if ($cmp === 0) {
			$cmp = $a[1] - $b[1];
		}
		return $cmp;
	}
}