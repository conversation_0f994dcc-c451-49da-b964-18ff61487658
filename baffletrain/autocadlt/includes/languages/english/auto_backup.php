<?php
/*
  $Id: auto_backup.php,v 1 23/02/2012 13:00:00

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2012 Sam

  Released under the GNU General Public License
*/

define('DBASE_MANAGER', 'Database Backup Manager');
define('AT_TITLE', 'Auto Backup');
define('AT_DELETE_TITLE', 'Auto Backup Delete');
define('AT_DATETIME_FMT', (BACKUP_DATETIME_FMT == 'true' ? "jS M Y H:i" : PHP_DATE_TIME_FORMAT));
define('AT_NOCRON_BACKUP', 'No backup made.');
define('AT_NOCRON_BACKUP2', ' Last backup made less than %s mins ago.');
define('AT_RESUMING', 'ATTENTION: Resuming the Auto-Backup of your Database.');
define('AT_BACKUP_FIRST', 'ATTENTION: Making the first Auto-Backup of your Database.');
define('AT_BACKUP_START', 'ATTENTION: Making a Auto-Backup of your Database ... ( %s minutes since last backup ).');
define('AT_HOURS', ' Hrs ');
define('AT_PAUSING', 'Backup paused after %s secs, continues on next page load, saved %s tables with %s rows so far...');
define('AT_COMPLETED', 'Auto Database Backup completed OK, %s tables with %s rows saved in %s seconds.');
define('AT_FILES_FOUND', ' files found.');
define('AT_DELETE_INTERIM_OK', 'Deleted expired interim backup: %s (dated %s)');
define('AT_DELETE_INTERIM_FAIL', 'Problem deleting expired interim backup: ');
define('AT_BACKUP_OK', 'Database Backup completed %s, %s tables with %s rows saved in %s secs');
define('AT_GZIP_OK', ' and GZipped in %s secs.');
define('CRON', 'Cron');
define('AT_DIRECT', 'Direct');
define('AT_JOB', ' Job Run ');
define('AT_BACKUP_KEPT', 'Backups are set to be kept for %s days.');
define('AT_DELETE_OK', 'Deleted expired backup %s ( %s days old )');
define('AT_DELETE_FAIL', 'Problem deleting expired backup %s ( %s days old )');
define('AT_CANT_DELETE', 'Cannot delete expired backups, no current backups in place!');
define('AT_NIL2DELETE', 'No backups to delete today!');
define('AT_PARTIAL', ' ( partial file )');
define('AT_NFF', 'No files found in backup directory!');
define('AT_EOF', 'End of File.');
define('AT_DELETE_CALLED', 'Auto Backup Delete was called, please check log file for results -- ');
define('AT_SCHEMA_HEAD', '# osCommerce, Open Source E-Commerce Solutions' . "\n" .
               '# http://www.oscommerce.com' . "\n" .
               '#' . "\n" .
               '# Database Backup For ' . STORE_NAME . "\n" .
               '# created by Auto Backup Pro by Sam ' . "\n" .
               '# (please consider donating if you use often)' . "\n" .
               '# Copyright (c) ' . date('Y') . ' ' . STORE_OWNER . "\n" .
               '#' . "\n" .
               '# Database: ' . DB_DATABASE . "\n" .
               '# Database Server: ' . DB_SERVER . "\n" .
               '#' . "\n" .
               '# Backup Date: '); // every line must start with a #, use "\n" to specify 'new line' 

define('AT_ERROR_WRITE_OPEN', 'Error opening backup file for write,  please check backup directory permissions.');
define('AT_ERROR_GZIP', 'ERROR, GZipping stopped, file access issue? or server out of disk space?');
define('AT_ERROR_WRITE', 'ERROR: A problem occurred when writing the backup file to disk on %s after %s secs and %s tables');
define('AT_ERROR_WRITE_FAIL_LOG', 'And problems creating error log file to flag your auto backup problem!');
define('AT_ERROR_LOG_EXIST', 'ERROR: Auto Backup failure log file exists, fix issue and delete log file to re-enable Auto Backup.');
define('ERROR_BACKUP_DIRECTORY_DOES_NOT_EXIST','Error, Backup Directory Does Not Exist, please create it or run %sauto_backup_setup.php');
define('ERROR_BACKUP_DIRECTORY_NOT_WRITEABLE', 'Error: Backup directory is not writeable.');

?>