/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2014 osCommerce

  Released under the GNU General Public License
*/
@media screen {
body { font-family: Verdana, "Helvetica Neue", Helvetica, Arial, sans-serif; }
@keyframes fadeIn {
  0% { opacity: 0; background-color: yellow; }
  25% { opacity: .25; background-color: rgba(255, 255, 0, 0.726); }
  50% { opacity: .5; background-color: rgba(255, 255, 0, 0.507); }
  75% { opacity: .75; background-color: rgba(255, 255, 0, 0.267); }
  100% { opacity: 1;}
}

tr.variation_row {
  opacity: 1;
  animation: fadeIn 2s;
}
/* links */
a { color: #000000; font-weight: normal; text-decoration: none; }
a:hover { text-decoration: underline; }

a.headerLink { color: #ffffff; font-weight: bold; text-decoration: none; }
a.headerLink:hover { text-decoration: underline; }

a.menuBoxHeadingLink { color: #616060; font-weight: bold; text-decoration: none; }
a.menuBoxHeadingLink:hover { }

a.menuBoxContentLink { color: #616060; font-weight: normal; text-decoration: none; }
a.menuBoxContentLink:hover { text-decoration: underline; }

a.splitPageLink { color: #0000FF; font-weight: normal; text-decoration: none; }
a.splitPageLink:hover { text-decoration: underline; background-color: #FFFF33; }

/* menu box */
.menuBoxHeading { color: #616060; background-color: #ffffff; }
.menuBoxContent { color: #616060; }

/* page */
body { background-color: #ffffff; color: #000000; margin: 0px; }
.headerBar { background-color: #B3BAC5; }
.headerBarContent { color: #ffffff; font-weight: bold; padding: 2px; }
.columnLeft { background-color: #F0F1F1; border-color: #999999; border-width: 1px; border-style: solid; padding: 2px; }
.pageHeading { font-size: 1.5em; color: #727272; font-weight: bold; }

/* data table */
.dataTableHeadingRow { background-color: #C9C9C9; }
.dataTableHeadingContent { color: #ffffff; font-weight: bold; }
.dataTableRow { background-color: #F0F1F1; }
.dataTableRowSelected { background-color: #DEE4E8; }
.dataTableRowOver { background-color: #FFFFFF; cursor: pointer; cursor: hand; }
.dataTableContent { color: #000000; }

/* info box */
.infoBoxHeading { color: #ffffff; background-color: #B3BAC5; }
.infoBoxContent { color: #000000; background-color: #DEE4E8; }

/* message box */

.messageBox { }
.messageStackError, .messageStackWarning { background-color: #ffb3b5; }
.messageStackSuccess { background-color: #99ff00; }

/* forms */
CHECKBOX, INPUT, RADIO, SELECT, TEXTAREA, FILE { font-family: Verdana, "Helvetica Neue", Helvetica, Arial, sans-serif; }
FORM { display: inline; }

/* account */
.formArea { background-color: #f1f9fe; border-color: #7b9ebd; border-style: solid; border-width: 1px; }
.formAreaTitle { font-family: Tahoma, Helveticy, Verdana, Arial, sans-serif; font-weight: bold; }

/* attributes */
.attributes-odd { background-color: #f4f7fd; }
.attributes-even { background-color: #ffffff; }

/* miscellaneous */
.specialPrice { color: #ff0000; }
.oldPrice { text-decoration: line-through; }
.fieldRequired { color: #ff0000; }
.smallText { }
.main { }
.errorText { color: #ff0000; }

/* new messageStack styles */
.secInfo, .secSuccess, .secWarning, .secError {
  border: 1px solid;
  margin: 10px 0px;
  padding: 5px 10px 5px 50px;
  background-repeat: no-repeat;
  background-position: 10px center;
  border-radius: 10px;
  -moz-border-radius: 10px;
  -webkit-border-radius: 10px;
}

.secInfo {
  border-color: #00529B;
  background-image: url('../images/ms_info.png');
  background: url('../images/ms_info.png') no-repeat 10px center, url('../images/ms_info_bg.png') repeat-x; /* css3 multiple backgrounds */
  background-color: #BDE5F8;
}

.secSuccess {
  border-color: #4F8A10;
  background-image: url('../images/ms_success.png');
  background: url('../images/ms_success.png') no-repeat 10px center, url('../images/ms_success_bg.png') repeat-x; /* css3 multiple backgrounds */
  background-color: #DFF2BF;
}

.secWarning {
  border-color: #9F6000;
  background-color: #FEEFB3;
  background-image: url('../images/ms_warning.png');
  background: url('../images/ms_warning.png') no-repeat 10px center, url('../images/ms_warning_bg.png') repeat-x; /* css3 multiple backgrounds */
  background-color: #FEEFB3;
}

.secError {
  border-color: #D8000C;
  background-image: url('../images/ms_error.png');
  background: url('../images/ms_error.png') no-repeat 10px center, url('../images/ms_error_bg.png') repeat-x; /* css3 multiple backgrounds */
  background-color: #FFBABA;
}

.secInfo p, .secSuccess p, .secWarning p, .secError p {
  padding: 2px;
}

#adminAppMenu {
  float: left;
  width: 225px;
  padding: 5px;
}

#adminAppMenu h3 {
}

#adminAppMenu ul {
  list-style: none;
  margin: -5px 0 -5px -10px;
  padding: 0;
}

#contentText {
  margin-left: 235px;
}

/* Custom jQuery UI */
.ui-widget { }
.ui-dialog { min-width: 500px; }

/* buttons */

.tdbLink a { }

.tdbLink button { }


.clipTarget{
	display:inline;
	font-family: Verdana,"Helvetica Neue",Helvetica,Arial,sans-serif;
}

.clipboardButton{
border:none;
background:none;
cursor:pointer;display:inline;
padding-left:6px;vertical-align: middle;-webkit-user-select: none;  -khtml-user-select: none;   -moz-user-select: none; -o-user-select: none;    user-select: none;
}
.clipboardButton:hover{display:inline;}

.clipboardButtonHighlight{
background:#24A424;
color:#fff;}

.familyListing:hover{background-color:#fff}
.flash{

  -moz-animation: flash 1s ease-out;
  -moz-animation-iteration-count: 1;

  -webkit-animation: flash 1s ease-out;
  -webkit-animation-iteration-count: 1;

  -ms-animation: flash 1s ease-out;
  -ms-animation-iteration-count: 1;

}

@-webkit-keyframes flash {
    0% { background-color:none;}
    50% { background-color:#24A424;}        
    100% {background-color:none;}
}

@-moz-keyframes flash {
    0% { background-color:none;}
    50% { background-color:#24A424;}        
    100% {background-color:none;}
}

@-ms-keyframes flash {
    0% { background-color:none;}
    50% { background-color:#24A424;}        
    100% {background-color:none;}
}

.ui-state-default a, .ui-state-default a:link, .ui-state-default a:visited {
  color: #2e6e9e;
  text-decoration: none;
}
#adminAppMenu {
  float: left;
  padding: 5px;
  width: 225px;
}

#contentText {
    margin-left: 235px;
}

.faded td{
  color:#aaa;

}

}
@media print {

.btn {
	display: none;
}

.clipTarget{
	display:inline;
}

.clipboardButton{
	display: none !important;
}
	
img{ display:none;}
.main,TD.main{
font-family: Verdana,Arial,sans-serif;
    font-size: 10px;
    line-height: 1.1em;
}
.ui-state-default a, .ui-state-default a:link, .ui-state-default a:visited {color:#fff;font-weight:bold;}
.ui-accordion-icons .ui-accordion-header a {
    padding-left: 1em;
}

.header, .menuBoxHeading, .ui-accordion, .ui-widget, .ui-helper-reset, .ui-accordion-icons, .footer, .invoiceFooter, .ui-button, .headerBar {
	display: none !important;
}

#contentText {
    margin-left: 0px;
}
.paymentTable TD.main2{
    font-size: 1.25em;
}
	
}
