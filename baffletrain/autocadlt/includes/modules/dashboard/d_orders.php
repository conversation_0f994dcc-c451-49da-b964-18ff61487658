<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2010 osCommerce

  Released under the GNU General Public License
*/

  class d_orders {
    var $code = 'd_orders';
    var $title;
    var $description;
    var $sort_order;
    var $enabled = false;

    function __construct() {
      $this->title = MODULE_ADMIN_DASHBOARD_ORDERS_TITLE;
      $this->description = MODULE_ADMIN_DASHBOARD_ORDERS_DESCRIPTION;

      if ( defined('MODULE_ADMIN_DASHBOARD_ORDERS_STATUS') ) {
        $this->sort_order = MODULE_ADMIN_DASHBOARD_ORDERS_SORT_ORDER;
        $this->enabled = (MODULE_ADMIN_DASHBOARD_ORDERS_STATUS == 'True');
      }
    }

    function getOutput() {
      global $languages_id;

      $output = '<table id="ordersTable" class="table table-condensed table-striped small" border="0" width="100%" cellspacing="0" cellpadding="4">' .
                '  <thead>
					<tr>' .                
                '    <th colspan="2">' . MODULE_ADMIN_DASHBOARD_ORDERS_TITLE . '</td>' .
                '    <th>' . MODULE_ADMIN_DASHBOARD_ORDERS_TOTAL . '</td>' .
                '    <th>' . MODULE_ADMIN_DASHBOARD_ORDERS_DATE . '</td>' .
                '    <th>' . MODULE_ADMIN_DASHBOARD_ORDERS_ORDER_STATUS . '</td>' .
                '  </tr>
				</thead>';

      $orders_query = tep_db_query("select o.orders_id, o.customers_name,o.customers_company, greatest(o.date_purchased, ifnull(o.last_modified, 0)) as date_last_modified, s.orders_status_name, ot.text as order_total from " . TABLE_ORDERS . " o, " . TABLE_ORDERS_TOTAL . " ot, " . TABLE_ORDERS_STATUS . " s where o.orders_id = ot.orders_id and ot.class = 'ot_total' and o.orders_status = s.orders_status_id and s.language_id = '" . (int)$languages_id . "' order by date_last_modified desc limit 20");
		$count = 0;
		$output .= '  <tbody id="ordersTableBody">';
	 while ($orders = tep_db_fetch_array($orders_query)) {
		if ($count == 0) $lastOrder = $orders['date_last_modified'];
		$count++;
		
		if ($orders['customers_company'] == '' ){
			$company = tep_output_string_protected($orders['customers_name']);
			$name = '';
		} else {
			$company = tep_output_string_protected($orders['customers_company']);
			$name = tep_output_string_protected($orders['customers_name']);
		};
		
		if ($orders['orders_status_name'] == 'Pending'){ 
			 $output .= '  <tr class="success" id="order_' . $orders['orders_id'] . '">';
		} else if ($orders['orders_status_name'] == 'Cancelled'){
			 $output .= '  <tr class="danger" id="order_' . $orders['orders_id'] . '">';
		}else {
			 $output .= '  <tr id="order_' . $orders['orders_id'] . '">';
		}
		
        $output .= '    <td><a href="' . tep_href_link('orders.php', 'oID=' . (int)$orders['orders_id'] . '&action=edit') . '">' . $company . '</a></td>' .
                   '    <td><a href="' . tep_href_link('orders.php', 'oID=' . (int)$orders['orders_id'] . '&action=edit') . '">' . $name . '</a></td>' .
                   '    <td>' . strip_tags($orders['order_total']) . '</td>' .
                   '    <td>' . tep_date_short($orders['date_last_modified']) . '</td>' .
                   '    <td>' . $orders['orders_status_name'] . '</td>' .
                   '  </tr>';
	 }
    $output .= '</tbody></table>
	<script>
			$(document).ready(function(){
				var lastOrder = "' . $lastOrder . '";
				 setInterval(function(){
					$.get("api.php?action=dashboard_liveOrder&lastOrder=" + lastOrder, function(data, status){
						if (status == "success") {
							for (x in data["orders"]) {
								theID = data["orders"][x]["id"];
								existing = document.getElementById("order_" + theID);
								if (existing){
									if (data["orders"][x]["status"] == "Processing"){
										existing.setAttribute("class", "");
									} else if (data["orders"][x]["status"] == "Canceled"  || data["orders"][x]["status"] == "Cancelled"){
										existing.setAttribute("class", "danger");
									}
									existing.innerHTML = \'<td><a href="orders.php?oID=\' + data["orders"][x]["id"] + \'&action=edit">\' + data["orders"][x]["company"] + \'</td><td><a href="orders.php?oID=\' + data["orders"][x]["id"] + \'&action=edit">\' + data["orders"][x]["title"] + \'</td><td>\' + data["orders"][x]["total"] + \'</td><td>\' + data["orders"][x]["date"] + \'</td><td>\' + data["orders"][x]["status"] + \'</td>\';
								} else {
									newRow = document.createElement("tr");
									newRow.setAttribute("id", "order_" + data["orders"][x]["id"]);									
									newRow.setAttribute("class", "success");
									newCell = document.createElement("td");
										newCell.setAttribute("id",\'order_\' + \'data["orders"][x]["id"]\')
										cellText = document.createTextNode(data["orders"][x]["company"]);
										newlink = document.createElement("a");
										newlink.setAttribute("href", \'orders.php?oID=\' + data["orders"][x]["id"] + \'&action=edit\');
										newlink.appendChild(cellText);
										newCell.appendChild(newlink);
										newRow.appendChild(newCell);
									newCell = document.createElement("td");
										newCell.setAttribute("id",\'order_\' + \'data["orders"][x]["id"]\')
										cellText = document.createTextNode(data["orders"][x]["title"]);
										newlink = document.createElement("a");
										newlink.setAttribute("href", \'orders.php?oID=\' + data["orders"][x]["id"] + \'&action=edit\');
										newlink.appendChild(cellText);
										newCell.appendChild(newlink);
										newRow.appendChild(newCell);
									
									newCell = document.createElement("td");
										cellText = document.createTextNode(data["orders"][x]["total"]);
										newCell.appendChild(cellText);
										newRow.appendChild(newCell);
									newCell = document.createElement("td");
										cellText = document.createTextNode(data["orders"][x]["date"]);
										newCell.appendChild(cellText);
										newRow.appendChild(newCell);
									newCell = document.createElement("td");
										cellText = document.createTextNode(data["orders"][x]["status"]);
										newCell.appendChild(cellText);
										newRow.appendChild(newCell);
									ordersBody = document.getElementById("ordersTableBody");
									ordersBody.insertBefore(newRow,ordersBody.firstChild);
								}
								lastOrder = data["orders"][0]["date"];
							}
						}
					});
				},5000);
			});
	  </script>';

      return $output;
    }

    function isEnabled() {
      return $this->enabled;
    }

    function check() {
      return defined('MODULE_ADMIN_DASHBOARD_ORDERS_STATUS');
    }

    function install() {
      tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Enable Orders Module', 'MODULE_ADMIN_DASHBOARD_ORDERS_STATUS', 'True', 'Do you want to show the latest orders on the dashboard?', '6', '1', 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
      tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Sort Order', 'MODULE_ADMIN_DASHBOARD_ORDERS_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', now())");
    }

    function remove() {
      tep_db_query("delete from " . TABLE_CONFIGURATION . " where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
      return array('MODULE_ADMIN_DASHBOARD_ORDERS_STATUS', 'MODULE_ADMIN_DASHBOARD_ORDERS_SORT_ORDER');
    }
  }
?>
