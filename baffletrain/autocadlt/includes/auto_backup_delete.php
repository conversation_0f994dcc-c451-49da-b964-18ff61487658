<?php
  /*
  $Id: auto_backup_delete.php, v 1.0 02 2012 13:00:00

  add a daily cron command like: php $HOME/public_html/catalog/admin/includes/auto_backup_delete.php > $HOME/public_html/catalog/admin/backups/removed_backup.log
  Rename admin to your admin dir name & remove catalog if your shop is in the root

  Released under the GNU General Public License
  */

  // initialise dBase etc
  if(!defined('DB_DATABASE'))
  {
    $cron = (php_sapi_name() == 'cli' && empty($_SERVER['REMOTE_ADDR']));
    $at_dir_ok = $at_logsdate = false;
    require ('configure.php');
    chdir(DIR_FS_ADMIN);
    require('includes/functions/database.php');
    tep_db_connect() or die('Unable to connect to database server!');
    require('includes/functions/general.php');
    require('includes/database_tables.php');
    $configuration_query = tep_db_query('select configuration_key as cfg<PERSON>ey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
    while ($configuration = tep_db_fetch_array($configuration_query)) define($configuration['cfgKey'], $configuration['cfgValue']);
    include('includes/classes/language.php');
    $lng = new language();
    if (isset($_GET['language']) && @tep_not_null($_GET['language'])) $lng->set_language($_GET['language']);
    $lng->get_browser_language();
    $language = $lng->language['directory'];    
  }
  include_once('includes/languages/' . $language . '/auto_backup.php');
  //create sort func
  function at_comp2($a, $b)
  {
    return ($a['date'] > $b['date']) ? -1 : 1;
  }
  // extract date
  function at_date2($digits)
  {
    if (!preg_match('/\d{12}/', $digits, $matches)) return NULL;
    return strtotime($matches[0]);
  }
  $at_nl = $cron || $at_logsdate ? "\r\n" : "<br />";
  $at_keepfor = BACKUP_DELETE > 2 ? BACKUP_DELETE : 2;
  if ($at_logsdate)
  {
    $atw_file = fopen(DIR_FS_BACKUP . 'removed_backup.log','w');
    function at_callback($buffer)
    {
      global $atw_file;
      fwrite($atw_file,$buffer);
    }
    ob_start('at_callback');
  }
  echo AT_DELETE_TITLE . $at_nl . $at_nl;
  echo ($cron ? CRON : ($at_logsdate ? AT_TITLE : AT_DIRECT)) . AT_JOB . date(AT_DATETIME_FMT) . $at_nl . $at_nl;
  echo sprintf(AT_BACKUP_KEPT, $at_keepfor) . $at_nl . $at_nl;
  // check if the backup directory exists
  if (!$at_dir_ok)
    if (is_dir(DIR_FS_BACKUP))
      if (is_writeable(DIR_FS_BACKUP)) $at_dir_ok = true;
      else echo ERROR_BACKUP_DIRECTORY_NOT_WRITEABLE . $at_nl;
    else
      echo sprintf(ERROR_BACKUP_DIRECTORY_DOES_NOT_EXIST,'') . $at_nl;
  if ($at_dir_ok)
  {
    $at_dir = dir(DIR_FS_BACKUP);
    $at_contents = array();
    $at_filename = false;
    while ($at_file = $at_dir->read())
    {
      while ($at_file = $at_dir->read()) if (!is_dir(DIR_FS_BACKUP . $at_file)) $at_contents[]=array('file' => $at_file,'date' => at_date2($at_file));
    }
    $at_dir->close();
    if ($at_total = sizeof($at_contents))
    {
      echo $at_total . AT_FILES_FOUND . $at_nl;
      usort($at_contents, 'at_comp2'); // newest 1st
      $at_found = 0;
      foreach($at_contents as $at_entry)
      {
        if (!$at_entry['date']) continue; // ignore non date stamped files
        $at_ref_entry = $at_entry['file'];
        $at_dif_date = intval((time() - $at_entry['date'])/60/60/24);
        if ($at_dif_date < $at_keepfor && !($at_part = strpos($at_ref_entry, '.sql.part')) ) // too young & not partial file
        {
          $at_found ++;
          continue;
        }
        if ($at_part && (time() - $at_entry['date']) < 600) continue; // delete any partial files over 10 mins old
        $at_filename = DIR_FS_BACKUP . $at_ref_entry;
        if ($at_ref_entry && ($at_found || $at_part))
        {
          $at_ref_entry .= $at_part ? AT_PARTIAL : '';
          if (unlink($at_filename))
          echo sprintf(AT_DELETE_OK, $at_ref_entry, $at_dif_date) . $at_nl;
          else
          echo sprintf(AT_DELETE_FAIL, $at_ref_entry, $at_dif_date) . $at_nl;
        }
      }
      if ($at_filename && !$at_found) echo AT_CANT_DELETE . $at_nl;
      elseif (!$at_filename) echo AT_NIL2DELETE . $at_nl;
    }
    else
    {
      echo AT_NFF . $at_nl;
    }
  }
  echo $at_nl . AT_EOF . $at_nl;
  if ($at_logsdate)
  {
    ob_end_flush();
    fclose($atw_file);
    $messageStack->add( AT_DELETE_CALLED . '<a href="' . tep_href_link('backup.php') . '">' . DBASE_MANAGER . '</a>', 'warning');
  }
?>