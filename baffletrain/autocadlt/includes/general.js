function SetFocus() {
    if (document.forms.length > 0) {
        isNotAdminLanguage:
        for (f=0; f<document.forms.length; f++) {
            if (document.forms[f].name != "adminlanguage") {
                var field = document.forms[f];
                for (i=0; i<field.length; i++) {
                    if ( (field.elements[i].type != "image") &&
                        (field.elements[i].type != "hidden") &&
                        (field.elements[i].type != "reset") &&
                        (field.elements[i].type != "button") &&
                        (field.elements[i].type != "submit") ) {

                        document.forms[f].elements[i].focus();

                        if ( (field.elements[i].type == "text") ||
                            (field.elements[i].type == "password") )
                            document.forms[f].elements[i].select();
                            break isNotAdminLanguage;
                    }
                }
            }
        }
    }
}

function rowOverEffect(object) {
    if (object.className == 'dataTableRow') object.className = 'dataTableRowOver';
}

function rowOutEffect(object) {
    if (object.className == 'dataTableRowOver') object.className = 'dataTableRow';
}

/**
 * Function to toggle the display of a div block based on its ID.
 *
 * @param {string} id - The ID of the div block to toggle.
 */
function toggleDivBlock(id) {
    if (document.getElementById) {
        itm = document.getElementById(id);
    } else if (document.all){
        itm = document.all[id];
    } else if (document.layers){
        itm = document.layers[id];
    }

    if (itm) {
        if (itm.style.display != "none") {
            itm.style.display = "none";
        } else {
            itm.style.display = "block";
        }
    }
}


/**
 * Function to update the UI with the newly uploaded images
 * @param {Array} imageData - Array of objects containing image data
 */
function updateUI(imageData) {
    // Iterate over each image in the imageData array
    imageData.forEach(function(image) {
        // Generate HTML content for each image
        let htmlContent = `
            <li id="piId${image.id}" class="ui-state-default" style="float:left; max-width:120px">
                <span class="ui-icon ui-icon-arrowthick-2-n-s" style="float: right;"></span>
                <a id="${'large_image_' + image.id}" data-imageid="${image.id}" class="imageDelete ui-icon ui-icon-trash" style="cursor:pointer;float: right;"></a> 
                <strong>Image ${image.id}</strong><br />
                <a href="${image.url}" target="_blank"><img class="img-responsive" src="${image.url}"></a>
                <input type="file" name="products_image_large_${image.id}" style="width: 90px;overflow: hidden;">
            </li>`;
        // Append HTML content to the piList element
        $('#piList').append(htmlContent);
    });
}

$('#piList').sortable({
    containment: 'parent'
});

var piSize = $('#piList li').length

function addNewPiForm() {
    piSize++;
    $('#piList').append('<li id="piId' + piSize + '" class="ui-state-default"><span class="ui-icon ui-icon-arrowthick-2-n-s" style="float: right;"></span><a href="#" onclick="showPiDelConfirm(' + piSize + ');return false;" class="ui-icon ui-icon-trash" style="float: right;"></a><strong>Large Image</strong><br /><input type="file" name="products_image_large_new_' + piSize + '" /><br /><br />HTML Content<br /><textarea name="products_image_htmlcontent_new_' + piSize + '" wrap="soft" cols="70" rows="3"></textarea></li>');
}


function showPiDelConfirm(piId) {
    piDelConfirmId = piId;
    $('#piDelConfirm').dialog('open');
}
/*
 * Related Products 
 */
function relatedProductsBuildEmptyTable(relatedTableID){        
    return    $("#relatedProducts").append(''
        + '<table id="relatedProductsTable-' + relatedTableID + '" width="100%" cellspacing="3" cellpadding="3" class="table-draggable table table-striped table-hover table-condensed small" data-tableId="' + relatedTableID + '">'
        + '        <thead class="thead-dark">'
        + '            <tr>'
        + '                <th>#</th>'
        + '                <th>Product</th>'
        + '                <th class="text-right">Action</th>'
        + '            </tr>'
        + '         </thead>'
        + '   <tbody id="relatedProductsTable-' + relatedTableID + '" class="connectedSortable">'
        + '     </tbody>'
        + '</table>'
    ).fadeIn("slow");
}

/**
 * Builds a row for a related product and adds it to the related products table.
 * If the product has variations, it also adds a table for the variations.
 *
 * @param {string} relatedId - The ID of the related product.
 * @param {string} relatedTableId - The ID of the related products table.
 * @param {string} productId - The ID of the product.
 * @param {string} productModel - The model of the product.
 * @param {string} productName - The name of the product.
 * @param {string} sortOrder - The sort order of the product.
 * @param {Array} variations - An array of the product's variations.
 */
function relatedProductsBuildAddProductRow(relatedId, relatedTableId, productId, productModel, productName, sortOrder, variations) {
    // Get the related products table
    var $table = $("#relatedProductsTable-" + relatedTableId);

    // If the product has variations, add a table for them
    if (variations.length > 0) {
        // Create the main row for the product
        var $mainRow = $table.append('<tr class="table-active small" id="relatedProductsRow' + productId + '" data-id="' + productId + '" data-relatedId="' + relatedId + '">'
                + '<td class="portletTD"><div class="portlet">&nbsp;</div></td>'
                + '<td colspan="3" style="padding:0;">'
                + '<table id="relatedProductsVariationsTable-' + relatedTableId + '-' + productId + '" cellspacing="0" cellpadding="3" class="table table-hover table-condensed table-sm" width="100%" data-relatedid="' + relatedTableId + '">'
                + '<tbody>'
                + '<tr class="table-active" data-id="' + productId + '" data-relatedId="' + relatedId + '">'
                + '<td style="width:5%" class="relatedProductsSOtd"><input class="indexSO relatedProductSOinput" type="text" size="1" maxlength="4" value="' + sortOrder + '"></td>'
                + '<td style="width:15%">' + productModel + '</td>'
                + '<td style="width:80%" colspan="2">' + productName + '</td>'
                + '</tr>'
                + '</tbody>'
                + '<tbody>').fadeIn("slow");

        // Add each variation to the variations table
        variations.forEach(function(variation) {
            $mainRow.find('tbody:last').append(
                    '<tr class="table-active relatedVaritionRow small" id="variationProductsRow' + variation.products_variations_id + '" data-id="' + variation.products_variations_id + '" data-variationId="' + variation.products_variations_id + '">'
                    + '<td style="width:5%" class="variationtd"></td>'
                    + '<td style="width:15%">' + variation.model + '</td>'
                    + '<td style="width:80%">' + variation.product_name_suffix.replace(', ', ' ') + '</td>'
                    + '<td class="variationProductsDelete listsOptionsToggle">'
                    + '<div class="material-switch pull-right">'
                    + '<input class="relatedVaritionToggles" id="relatedVaritionToggle-' + productId + '-' + variation.products_variations_id + '" type="checkbox" checked data-products_id="' + productId + '" data-related_id="' + relatedId + '" data-variation_id="' + variation.products_variations_id + '"/>'
                    + '<label class="relatedVaritionToggleLabels label-success" for="relatedVaritionToggle-' + productId + '-' + variation.products_variations_id + '"></label>'
                    + '</div></td>'
                    + '</tr>'
            ).fadeIn("slow");
        });

        // Add the main row and variations table to the table
        $mainRow.append('</tbody></table></td><td class="relatedProductsDelete listsOptionsDelete">x</td></tr>');
        $table.append($mainRow).fadeIn("slow");
    } else {
        // Add the main row to the table
        $table.append(
                '<tr class="table-active small" id="relatedProductsRow' + productId + '" data-id="' + productId + '" data-relatedId="' + relatedId + '">'
                + '<td class="portletTD"><div class="portlet">&nbsp;</div></td>'
                + '<td style="width:5%" class="relatedProductsSOtd"><input class="indexSO relatedProductSOinput" type="text" size="1" maxlength="4" value="' + sortOrder + '"></td>'
                + '<td style="width:15%">' + productModel + '</td>'
                + '<td style="width:80%">' + productName + '</td>'
                + '<td class="relatedProductsDelete listsOptionsDelete">x</td>'
                + '</tr>'
        );
    }
}
/*
* attributes


function attributes_AddRow(data) {	
    attribute_id = data["attributes"][0]["attribute_id"];
    options_name = data["attributes"][0]["options_name"];
    options_id = data["attributes"][0]["options_id"];
    values_id = data["attributes"][0]["values_id"];
    values_name = data["attributes"][0]["values_name"];
    value_price = data["attributes"][0]["value_price"];
    price_prefix = data["attributes"][0]["price_prefix"];
    attribute_default = data["attributes"][0]["attribute_default"] == "1" ? 'Yes' : 'No';
    dependson_options_id = data["attributes"][0]["dependson_options_id"];
    dependson_options_name = data["attributes"][0]["dependson_options_name"];
    dependson_values_id = data["attributes"][0]["dependson_values_id"];
    dependson_values_name = data["attributes"][0]["dependson_values_name"];
    
    var rowNum = options_id + '_' + values_id;
    
    var elements = $('<tr style="display:none" data-attributesid="' + attribute_id + '" data-rownum="' + rowNum + '" class="" id="attributesTableRow_' + rowNum + '">'
        + '<td class="portletTD"><div class="portlet">&nbsp;</div></td>'
        + '<td id="attributesTable_optionsName_' + rowNum + '" data-options_id="' + options_id + '">' + options_name + '</td>'
        + '<td id="attributesTable_optionsValueName_' + rowNum + '" data-values_id="' + values_id + '">' + values_name + '</td>'
        + '<td id="attributesTable_optionsValuePrice_' + rowNum + '" align="center">' + value_price + '</td>'
        + '<td id="attributesTable_optionsValuePricePrefix_' + rowNum + '" align="center">' + price_prefix + '</td>'				
        + '<td id="attributesTable_optionsValuePriceDefault_' + rowNum + '" align="center">' + attribute_default + '</td>'
        + '<td id="attributesTable_DependsOn_' + rowNum + '" data-dependson_options_id="' + dependson_options_id + '" data-dependson_values_id="' + dependson_values_id + '">' + dependson_options_name + ': ' + dependson_values_name + '</td>'
        + '<td style="width:5%" class=""><input class="indexSO attributesSOinput" type="text" name="" size="1" maxlength="4" value=""></td>'
        + '<td class="attributesOptionsEdit listsOptionsEdit">e</td>'
        + '<td class="attributesOptionsDelete listsOptionsDelete">x</td>'
    + '</tr>');
    
    var row = $('#attributesTableRow_' + rowNum);					
    if (row.length) {
        row.replaceWith(elements);
        elements.fadeIn("slow");
    } else {
        elements.appendTo("#attributesTable").fadeIn("slow");
    }			
}

function attributes_updateValuesButtons(){
    var valuesSelectBtnsCont = $('#attributes_values_id_btns_container');
    var valuesSelectCont = $('#attributes_values_id_container');
    var valuesSelect = $('#attributes_values_id');
    var valuesSelectBtns = $(".valuesSelectBtns");
    var currentVal = valuesSelect.val();
    valuesSelectBtns.removeClass('active btn-success').addClass('btn-default');
    $("#valuesSelectBtn_" + currentVal).addClass('active btn-success');
}

function attributes_getValueList(e,selected_item){
    var valuesSelectBtnsCont = $('#attributes_values_id_btns_container');
    var valuesSelectCont = $('#attributes_values_id_container');
    var valuesSelect = $('#attributes_values_id');
    var options_select = $("#attributes_options_id");
    var method = "product_attributes_getValueList";
    if (valuesSelect.data("current_id") != options_select.val()){
        $.get("api.php?action=" + method + "&options_id=" + options_select.val(), function(data, status){				
            if (status == "success") {
                if (e){e.stopPropagation();}			
            //	attributes_values_id_btns_container = $("#attributes_values_id_btns_container");
                valuesSelectBtnsCont.empty();
                valuesSelect.empty().append($('<option disabled selected value>Values</option>'));
                var count = 0;
                var selected = "selected";
                for (x in data["attributes"]) {
                    if (selected_item >= 0){
                        if (selected_item == data["attributes"][x]["values_id"]){
                            var selected = "selected";
                        } else {
                            var selected = "";
                        }
                    }
                    valuesSelect.append($('<option id="valuesSelectItem_' +  count + '" value="' + data["attributes"][x]["values_id"] + '"' + selected  + '>' + data["attributes"][x]["values_name"] + '</option>'));
                    valuesSelectBtnsCont.append($('<button type="button" id="valuesSelectBtn_' + data["attributes"][x]["values_id"] + '"' + ' data-value="' + data["attributes"][x]["values_id"] + '" class="btn btn-default valuesSelectBtns">' + data["attributes"][x]["values_name"] + '</button>'));
                    var selected = "";												
                }
                attributes_updateValuesButtons();
                valuesSelectBtns = $(".valuesSelectBtns");
                if ((valuesSelectBtnsCont.width() > 450) || (valuesSelectBtnsCont.height() > 100)){
                    valuesSelectBtnsCont.hide();
                    valuesSelectCont.show();
                } else {
                    valuesSelectBtnsCont.show();
                    valuesSelectCont.hide();
                    valuesSelectBtns.click(function(e) {
                        valuesSelectBtns.removeClass('active btn-success').addClass('btn-default');
                        $(this).removeClass('btn-default').addClass('active btn-success');
                        valuesSelect.val($(this).data("value"));
                    });
                }
            }					
            valuesSelect.prop('disabled', false);	
            valuesSelect.data("current_id",options_select.val());
        });
    } else if (selected_item >= 0){
        valuesSelect.val(selected_item);
        attributes_updateValuesButtons();
    }
}




function attributes_updateValuesButtons(){
    var valuesSelectBtnsCont = $('#attributes_values_id_btns_container');
    var valuesSelectCont = $('#attributes_values_id_container');
    var valuesSelect = $('#attributes_values_id');
    var valuesSelectBtns = $(".valuesSelectBtns");
    var currentVal = valuesSelect.val();
    valuesSelectBtns.removeClass('active btn-success').addClass('btn-default');
    $("#valuesSelectBtn_" + currentVal).addClass('active btn-success');
}


function attributes_getValueList(e,selected_item){
    var valuesSelectBtnsCont = $('#attributes_values_id_btns_container');
    var valuesSelectCont = $('#attributes_values_id_container');
    var valuesSelect = $('#attributes_values_id');
    var options_select = $("#attributes_options_id");
    var method = "product_attributes_getValueList";
    if (valuesSelect.data("current_id") != options_select.val()){
        $.get("api.php?action=" + method + "&options_id=" + options_select.val(), function(data, status){				
            if (status == "success") {
                if (e){e.stopPropagation();}			
            //	attributes_values_id_btns_container = $("#attributes_values_id_btns_container");
                valuesSelectBtnsCont.empty();
                valuesSelect.empty().append($('<option disabled selected value>Values</option>'));
                var count = 0;
                var selected = "selected";
                for (x in data["attributes"]) {
                    if (selected_item >= 0){
                        if (selected_item == data["attributes"][x]["values_id"]){
                            var selected = "selected";
                        } else {
                            var selected = "";
                        }
                    }
                    valuesSelect.append($('<option id="valuesSelectItem_' +  count + '" value="' + data["attributes"][x]["values_id"] + '"' + selected  + '>' + data["attributes"][x]["values_name"] + '</option>'));
                    valuesSelectBtnsCont.append($('<button type="button" id="valuesSelectBtn_' + data["attributes"][x]["values_id"] + '"' + ' data-value="' + data["attributes"][x]["values_id"] + '" class="btn btn-default valuesSelectBtns">' + data["attributes"][x]["values_name"] + '</button>'));
                    var selected = "";												
                }
                attributes_updateValuesButtons();
                valuesSelectBtns = $(".valuesSelectBtns");
                if ((valuesSelectBtnsCont.width() > 450) || (valuesSelectBtnsCont.height() > 100)){
                    valuesSelectBtnsCont.hide();
                    valuesSelectCont.show();
                } else {
                    valuesSelectBtnsCont.show();
                    valuesSelectCont.hide();
                    valuesSelectBtns.click(function(e) {
                        valuesSelectBtns.removeClass('active btn-success').addClass('btn-default');
                        $(this).removeClass('btn-default').addClass('active btn-success');
                        valuesSelect.val($(this).data("value"));
                    });
                }
            }					
            valuesSelect.prop('disabled', false);	
            valuesSelect.data("current_id",options_select.val());
        });
    } else if (selected_item >= 0){
        valuesSelect.val(selected_item);
        attributes_updateValuesButtons();
    }
}



function attributes_dependsOn_getValueList(e,selected_items){
    var dependson_valuesSelectCont = $('#attributes_dependson_values_id_container');
    var dependson_valuesSelect = $('#attributes_dependson_values_id');
    var dependson_options_select = $('#attributes_dependson_options_id');
    method = "product_attributes_getValueList";
    if (dependson_valuesSelect.data("current_id") != dependson_options_select.val()){
        $.get("api.php?action=" + method + "&options_id=" + dependson_options_select.val() + "&filter=" + dependson_options_select.data('productsid'), function(data, status){
            if (status == "success") {
                if (e) e.stopPropagation();		
                dependson_valuesSelect.empty().append($('<option disabled selected value>Values</option>'));
                for (x in data["attributes"]) {
                    dependson_valuesSelect.append($('<option value="' + data["attributes"][x]["values_id"] + '">' + data["attributes"][x]["values_name"] + '</option>'));
                    //dependson_valuesSelectBtnsCont.append($('<button type="button" id="dependson_valuesSelectBtn_' + data["attributes"][x]["values_id"] + '" data-value="' + data["attributes"][x]["values_id"] + '" class="btn btn-default dependson_valuesSelectBtns">' + data["attributes"][x]["values_name"] + '</button>'));
                }					
            }
            dependson_valuesSelect.prop('disabled', false);
            dependson_valuesSelect.val(selected_items);
            valuesSelect.data("current_id",options_select.val());
        });
    } else if (selected_items != null){
        dependson_valuesSelect.val(selected_items);
        attributes_updateValuesButtons();
    }
}


function variationsAddRow(data) {    
    variation_id = data["variations"][0]["products_variations_id"];
    attributes_text    = data["variations"][0]["attributes_text"];
    attributes = data["variations"][0]["attributes"];
    model = data["variations"][0]["model"];
    gtin = data["variations"][0]["gtin"];
    image_id = data["variations"][0]["image_id"];
    price = data["variations"][0]["price"];
    var rowNum = model + '_' + variation_id;
    
    var elements = $('<tr style="display:none" data-variationsId="' + variation_id + '" data-rownum="' + rowNum + '" class="" id="variationsTableRow_' + rowNum + '">'
        + '<td class="portletTD"><div class="portlet">&nbsp;</div></td>'
        + '<td id="variationsTable_model_' + rowNum + '" data-model="' + model + '">' + model + '</td>'
        + '<td id="variationsTable_gtin_' + rowNum + '" data-gtin="' + gtin + '">' + gtin + '</td>'
        + '<td id="image_id_' + rowNum + '">' + image_id + '</td>'
        + '<td id="variationsTable_price_' + rowNum + '" align="center">' + price + '</td>'
        + '<td id="variationsTable_attributes' + rowNum + '" data-attributes="' + attributes + '" valign="center">' + attributes_text + '</td>'
        + '<td style="width:5%" class=""><input class="indexSO variationsSOinput" type="text" name="" size="1" maxlength="4" value=""></td>'
        + '<td class="variationsOptionsEdit listsOptionsEdit">e</td>'
        + '<td class="variationsOptionsDelete listsOptionsDelete">x</td>'
    + '</tr>');
    
    var row = $('#variationsTableRow_' + rowNum);                    
    if (row.length) {
        row.replaceWith(elements);
        elements.fadeIn("slow");
    } else {
        elements.appendTo("#variationsTable").fadeIn("slow");
    }            
}



    $("#attributes_insert_btn").click(function(e) {
        var error = 0;       
        var culprit = "";
        var options_id = $('#attributes_options_id').val();
        var values_id = $('#attributes_values_id').val();	
        
        
        var dependson_options_id = $('#attributes_dependson_options_id').val();
        var dependson_values_id = $('#attributes_dependson_values_id').val();
        var value_price = $('#attributes_value_price').val();
        var price_prefix = encodeURIComponent($('#attributes_price_prefix').val());
               var attribute_default = $('#attributes_attribute_default').prop("checked") == true ? 1 : 0;
                var method = "product_attributes_addToProduct";
        $("#attributesTable tbody tr").each(function(){
            var row = $(this).data("rownum");
            var elmstring = "#attributesTable_optionsValueName_" + row;
            if ($(elmstring).data("values_id") == values_id) {
                 method = "product_attributes_addToProduct";
                 return false;
            }
        });
        $.get("api.php?action=" + method + "&products_id=" + products_id + "&options_id=" + options_id + "&values_id=" + values_id + "&value_price=" + value_price + "&price_prefix=" + price_prefix + "&attribute_default=" + attribute_default + "&dependson_options_id=" + dependson_options_id + "&dependson_values_id=" + dependson_values_id, function(data, status){
            if (status == "success") {
                if (data["Complete"] > 0) {										
                    attributes_AddRow(data);								
                    e.stopPropagation();					
                }
            }
        });
    });
    


    $("#attributesTable").on("click", ".attributesOptionsDelete", function(e) {
        var that = $(this);
        var method = "product_attributes_removeAttribute";
        $.get("api.php?action=" + method + "&products_id=" + products_id + "&products_attributes_id=" + that.parent().data("attributesid"), function(data, status){
            if (status == "success") {
                e.stopPropagation();
                that.parent().fadeOut("slow",function(){
                    this.remove();
                });
            }
        });	
    });	
    

    $("#attributesTable").on("click",".attributesOptionsEdit",function(e,) {
        var row = $(this).closest('tr').data('rownum');
        var options_id = $('#attributesTable_optionsName_' + row).data('options_id');
        var values_id = $('#attributesTable_optionsValueName_' + row).data('values_id');			
        var dependson_options_id = $('#attributesTable_DependsOn_' + row).data('dependson_options_id').toString();
        var dependson_values_id = $('#attributesTable_DependsOn_' + row).data('dependson_values_id').toString();
        var value_price = $('#attributesTable_optionsValuePrice_' + row).text().trim();
        var price_prefix = $('#attributesTable_optionsValuePricePrefix_' + row).text().trim();
        var attribute_default = $('#attributesTable_optionsValuePriceDefault_' + row).text().trim() == "Yes" ? 1 : 0;
        var $optionsSelect = $('#attributes_options_id');
        var $valuesSelect = $('#attributes_values_id');
        $optionsSelect.val(options_id);
        attributes_getValueList(null,values_id);
        $('#attributes_value_price').val(value_price);
        $('#attributes_price_prefix').val(price_prefix);			
        $('#attributes_prefix_plus_btn').trigger('change');
        $('#attributes_attribute_default').prop("checked", attribute_default);
        if ((dependson_options_id != null) && (dependson_values_id != null)){
            $('#attributes_dependson_options_id').val(dependson_options_id);
            if(dependson_values_id.indexOf(',') == -1){
                dependson_values_ids=Array([dependson_values_id]);
            } else {
                dependson_values_ids=dependson_values_id.split(',');
            }
            attributes_dependsOn_getValueList(null,dependson_values_ids);
        } else {
            $('#attributes_dependson_options_id').val(0);
            $('#attributes_dependson_values_id').html("<option disabled selected value>Values</option>");
        }			
    });	
    



    $("#attributes_values_id").change(function(e, callback) {
        attributes_updateValuesButtons();					
        if (typeof callback === "function") callback();
    });
    
    $("#attributes_options_id").change(function(e, callback) {			
        attributes_getValueList(e);
        if (typeof callback === "function") callback();
    });        
    
    
    
    
    $("#attributes_dependson_options_id").change(function(e, callback) {
        attributes_dependsOn_getValueList(e);
    });
    

    $(".attributes_prefix_btns").click(function(e) {
        $("#attributes_price_prefix").val($(this).text()).trigger('change');
            
    });	
    
    $('#attributes_price_prefix').on("change", function(e, callback) {
        $(".attributes_prefix_btns").removeClass("active btn-success").addClass('btn-default');
        var theText = $(this).val();
        if (theText == "+"){
            $("#attributes_prefix_plus_btn").addClass("active btn-success").removeClass('btn-default');
        } else if (theText == "-"){
            $("#attributes_prefix_minu_btn").addClass("active btn-success").removeClass('btn-default');
        }	
    });
    
*/
$(document).ready(function(e) {
    const products_id = $('#products_id_hidden_input').val();
    const cPath = $('#cPath_hidden_input').val();
    if ($('#products_ship_flags_digiProduct').val()){
        $('.indvShip').fadeTo('slow',.25);        
        $('#products_digiProduct_yes').click(function() {
            $('.indvShip').fadeTo('slow',.25);
        });
        $('#products_digiProduct_no').click(function() {
            $('.indvShip').fadeTo('slow',1);
        });
    }

    var piDelConfirmId = 0;
    $('#piDelConfirm').dialog({
        autoOpen: false,
        resizable: false,
        draggable: false,
        modal: true,
        buttons: {
            'Delete': function() {
                $('#piId' + piDelConfirmId).effect('blind').remove();
                $(this).dialog('close');
            },
            Cancel: function() {
                $(this).dialog('close');
            }
        }
    });
    

    $(".table-draggable tbody").sortable({
        distance: 5,
        delay: 100,
        opacity: 0.6,
        cursor: 'move',
        connectWith: '.connectedSortable',
        cancel: ".relatedVaritionRow",
        handle: ".portlet",
        helper: function(e, tr) {
            var $originals = tr.children();
            var $helper = tr.clone();
            $helper.children().each(function(index) {
                $(this).width($originals.eq(index).width());
            });
            return $helper;
        },
        update: function(e, ui) {
            var updatedData = {};
            var apiAction = null;
    
            function updateSortOrder(className, dataAttribute) {
                $('.indexSO.' + className, ui.item.parent()).each(function(i) {
                    $(this).val(i).trigger("change");
                    var updatedValue = $(this).val();
                    var attributesId = $(this).closest('tr').data(dataAttribute);
                    updatedData[attributesId] = updatedValue;
                });
            }
    
            if ($(this).find('.attributesSOinput').length > 0) {
                apiAction = 'product_attributes_updateSortOrder';
                updateSortOrder('attributesSOinput', 'attributesid');
            } else if ($(this).find('.variationsSOinput').length > 0) {
                apiAction = 'product_variations_updateSortOrder';
                updateSortOrder('variationsSOinput', 'variationsid');
            } else if ($(this).find('.relatedProductSOinput').length > 0) {
                apiAction = 'related_products_updateSortOrder';
                updateSortOrder('relatedProductSOinput', 'relatedid');
            }
    
            if (apiAction != null) {
                var jsonData = JSON.stringify(updatedData);
                $.get("api.php?action=" + apiAction, { jsonData: jsonData }, function(response) {
                    console.log(response);
                });
            } else {
                console.log('Failed to find classname');
            }
        }
    });
    


    // Function to handle batch image upload
    $('#batchUpload').on('change', function(event) {
        let files = event.target.files;
        let formData = new FormData();
        
        $.each(files, function(index, file) {
                formData.append('batchUpload[]', file);
        });
       
        // Add additional data if needed
        formData.append('products_id', products_id);
        
        $.ajax({
                url: 'upload_batch.php',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                        let imageData = JSON.parse(response);
                        updateUI(imageData);
                },
                error: function(xhr, status, error) {
                        console.error('Upload error:', status, error);
                }
        });
    });

    $('.imageDelete').on('click', function(event) {        
        var that = $(this);
        var method = "product_images_removeImage";
        $.get("api.php?action=" + method + "&products_id=" + products_id + "&image_id=" + that.data("imageid"), function(data, status){
            if (status == "success") {
                event.stopPropagation();
                that.parent().remove();
            }
        });    
    });
 /*   $("#variations_insert_btn").on('click',function(e) {
        var error = 0;
        var culprit = "";
        var model = $('#variations_model').val();            
        var gtin =  $('#variations_gtin').val();
        var image_id =  $('#variations_image_id').val();
        var price = $('#variations_price').val();    
                            var method = "product_variations_addToProduct";
                            attributeString = "";
                            var attributeSelects = $(".variationsAttributeSelect").each(function(){
                                el = $(this);
                                attributeString += "{" + el.data("optionid") + "}" + el.find(":selected").val()
                            });
        $("#variationsTable tbody tr").each(function(){
            var row = $(this).data("rownum");
            var elmstring = "#variationsTable_optionsValueName_" + row;
            if ($(elmstring).data("model") == model) {
                method = "product_variations_addToProduct";
                return false;
            }
        });
        $.get("api.php"
        + "?action=" + method
        + "&products_id=" + products_id
        + "&model=" + model
        + "&gtin=" + gtin
        + "&image_id=" + image_id
        + "&price=" + price
        + "&attributes=" + attributeString, function(data, status){
            if (status == "success") {
                if (data["Complete"] > 0) {                                        
                    variationsAddRow(data);                                
                    e.stopPropagation();                    
                }
            }
        });
    });
    
    
    $("#variationsTable").on("click", ".variationsOptionsDelete", function(e) {
        var that = $(this);        
        var method = "product_variations_removeVariation";
        $.get("api.php?action=" + method + "&products_id=" + products_id + "&products_variations_id=" + that.parent().data("variationsid"), function(data, status){
            if (status == "success") {
                e.stopPropagation();
                that.parent().fadeOut("slow",function(){
                    this.remove();
                });
            }
        });    
    });    
    
    $("#variationsTable").on("click",".variationsOptionsEdit",function(e) {
        var row = $(this).closest('tr').data('rownum');
        var model = $('#variationsTable_model_' + row).data('model');
        var gtin = $('#variationsTable_gtin_' + row).data('gtin');
        var value_price = $('#variationsTable_Price_' + row).text().trim();
        var variations_image_id = $('#variationsTable_image_id_' + row).text().trim();            
        
        $("#variations_model").val(model);
        $("#variations_gtin").val(gtin);
        $("#variations_price").val(value_price);
        $("#variations_image_id").val(variations_image_id);
        var attributes = $('#variationsTable_attributes_' + row).data("attributes");
        attributes = attributes.substring(1).split('{');
        attributesNameString = "";        
        for (let i = 0, n = attributes.length; i < n; i++) {
            let pair = attributes[i].split('}');
             $("#variations_select_" + pair[0]).val(pair[1]);
        }                
        
    });
*/
    $("#relatedProductsAddProduct").on('click',function(e) {
        relatedTableID = $('#related_products_table_select').val();
        if (relatedTableID == "999") {
            lastTableID = $("#relatedProducts").children().last().attr('data-tableid');                            
            relatedTableID = parseInt(lastTableID) + 1;                        
        }
        if ($("#relatedProductsTable-" + relatedTableID + " tbody").children().length > 0) {
            relatedSortOrder = $("#relatedProductsTable-" + relatedTableID + " tbody").children().length;
        } else {                    
            relatedSortOrder = 0;
        }
        $.get("api.php?action=related_products_addRelatedProduct&products_id=" + products_id + "&related_products_id=" + $('#related_products_select').val() + "&related_table_id=" + relatedTableID + "&relatedSortOrder=" + relatedSortOrder, function(data, status){
            if (status == "success") {
                if (data["Complete"] > 1) {
                    if (data["products"][0]) {
                        productIdNum = data["products"][0]["id"];
                        theElementID = "#relatedProductsRow" + productIdNum;
                        productName = data["products"][0]["title"];
                        productModel = data["products"][0]["model"];
                        relatedId = data["products"][0]["relatedId"];
                        variations = data["products"][0]["variations"];
                        relatedTableID = $('#related_products_table_select').val();
                        if (relatedTableID == "999") {
                            if ($("#relatedProducts").children().length > 0) {
                                lastTableID = $("#relatedProducts").children().last().attr('data-tableid');                        
                                 relatedTableID = parseInt(lastTableID) + 1;
                            } else {
                                relatedTableID = 0;                    
                            }
                            relatedProductsBuildEmptyTable(relatedTableID);                        
                            $("#relatedProductsTable-"  + relatedTableID + " tbody").sortable();
                            $("#related_products_table_select").append($('<option>', {
                                value: relatedTableID,
                                text: relatedTableID
                            }));
                            $("#related_products_table_select").val(relatedTableID);    
                        }
                        if ($("#relatedProductsTable-" + relatedTableID + " tbody").children().length > 0) {
                            relatedSortOrder = $("#relatedProductsTable-" + relatedTableID + " tbody").children().length;
                        } else {                    
                            relatedSortOrder = 0;
                        }
                        relatedProductsBuildAddProductRow(relatedId,relatedTableID,productIdNum,productModel,productName,relatedSortOrder,variations);
                        temp = $("#relatedProductsTable-"  + relatedTableID + " tbody");
                        $("#relatedProductsTable-"  + relatedTableID + " tbody").trigger("stop");
                        $("#relatedProductsTable-"  + relatedTableID + " tbody").sortable( "refresh" );        
                    }
                    e.stopPropagation();                    
                } else {
                    var searchTerm = data["products"][0]["model"].toLowerCase();
                    $('.relatedProductsRow').each(function() {
                        var product = $(this).text().toLowerCase();
                        if (product.indexOf(searchTerm) >= 0) {
                            $(this).addClass('danger');
                            $(this).popover({
                                title: 'Product found',                          
                                content: '<img src="images/friends-you-idiot.gif">',
                                html: true,
                                boundary: 'viewport',
                                boundaryPadding: 10,
                                flip: false,
                                fallbackPlacement: 'none',
                                placement: 'top',
                                trigger: 'focus'
                            }).on('click', function() {
                                $(this).removeClass('danger');
                                $(this).popover('destroy');                          
                            }).on('shown.bs.popover', function () {
                                var popover = $(this).data('bs.popover').tip();
                                var popoverTop = popover.offset().top;
                                var scrollTop = $(window).scrollTop();
                                var topOffset = popoverTop - scrollTop;

                                if (topOffset < 0) {
                                    $('html, body').animate({scrollTop: scrollTop + topOffset}, 500);
                                }
                            });
                            
                            $(this).popover('show');                      
                        }
                    });
                }
            }
        });        
    });

    $("#copyRelatedListFrom").on('click',function(e) {
        $.get("api.php?action=related_products_copyRelatedListFrom&products_id=" + products_id + "&source_id=" + $('#related_products_select').val() + "", function(data, status){
            if (status == "success") {
                if (data["Complete"] > 1) {
                    lastTable = -1;
                    $("#relatedProducts").empty();
                    for (x in data["products"]) {
                        productIdNum = data["products"][x]["id"];
                        productName = data["products"][x]["title"];
                        productModel = data["products"][x]["model"];
                        productPrice = data["products"][x]["price"];
                        relatedSortOrder = data["products"][x]["sortOrder"];
                        relatedId = data["products"][x]["relatedId"];
                        relatedTableID = data["products"][x]["relatedTableID"];
                        if (lastTable != relatedTableID) {                            
                            relatedProductsBuildEmptyTable(relatedTableID);
                            $("#related_products_table_select").children().last().before('<option value="' + relatedTableID + '">' + relatedTableID + '</option>');
                            lastTable = relatedTableID;
                        }
                        relatedProductsBuildAddProductRow(relatedId,relatedTableID,productIdNum,productModel,productName,relatedSortOrder);
                        $("#relatedProductsTable-"  + relatedTableID + "tbody").trigger("stop");
                        $("#relatedProductsTable-"  + relatedTableID + "tbody").sortable( "refresh" );                    
                        e.stopPropagation();
                    }
                    $("#table-draggable2 tbody").trigger("stop");
                }
            }
        });            
    });

    $("#relatedProductsSearchButton").on('click',function(e) {
        $.get("api.php?action=related_products_searchRelatedProduct&search_term=" + $('#relatedProductSearchinput').val() + "", function(data, status){
            if (status == "success") {                
                $("#related_products_select").empty();                
                for (x in data["products"]) {
                    theID = data["products"][x]["id"];                    
                    productName = data["products"][x]["title"];
                    $("#related_products_select").append($("<option>").attr('value',theID).text(productName));
                }                
            }
        });            
    });    

    $("#relatedProducts").on("click",".relatedProductsDelete",function(e) {
        var that = this;
        $.get("api.php?action=related_products_removeRelatedProduct&products_id=" + products_id + "&products_related_id=" + $(that).parent().attr("data-relatedId") + "", function(data, status){
            if (status == "success") {
                e.stopPropagation();
                $(that).parent().fadeOut("slow",function(){
                    this.remove();
                });
            }
        });    
    });
    //related_products_variations_toggle($products_id,$related_id,$variation_id,$status)
    $(".relatedVaritionToggleLabels").on("click",function(e) {    
            let products_id = $(this).data('products_id');
            let related_id = $(this).data('related_id');
            let variation_id = $(this).data('variation_id');
            let toggle_switch = $('#relatedVaritionToggle-' + products_id + '-' + variation_id).prop('checked');
            let toggle_status = toggle_switch == true ? 0 : 1;
            $.get("api.php?action=related_products_variations_toggle&products_id=" + products_id + "&related_id=" + related_id + "&variation_id=" + variation_id + "&toggle_status=" + toggle_status, function(data, status){
            if (status == "success") {
                e.stopPropagation();
                $(that).parent().parent().fadeTo('slow','.5');
            }
        });
    });

    $("#applyButton").on('click',function(e) {            
      
        for ( instance in CKEDITOR.instances ) CKEDITOR.instances[instance].updateElement();
        var form = $('#new_product_form')[0];
        var data = new FormData(form);
        $.ajax({
            type: "POST",
            enctype: 'multipart/form-data',
            url: "product_edit.php?cPath=" + cPath + "&pID=" + products_id + "&action=update_product",
            data: data,
            contentType: false,
            processData: false
        });
    });    
 });
