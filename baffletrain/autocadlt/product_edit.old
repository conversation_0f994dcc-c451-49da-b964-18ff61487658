<?php
/*
  $Id$

osCommerce, Open Source E-Commerce Solutions
http://www.oscommerce.com

Copyright (c) 2015 osCommerce

Released under the GNU General Public License
*/

require('includes/application_top.php');

require('includes/classes/currencies.php');
$currencies = new currencies();
require('includes/functions/googleCategoryfunctions.php');

require('ext/Google/ProductsSample.php');

 $products_blockFreeShip_yes = false;
 $products_blockFreeShip_no  = true;
 $products_digiProduct_yes = false;
 $products_digiProduct_no  = true;
 $products_request_quote_yes = false;
 $products_request_quote_no  = true;
 
function console_log($output, $with_script_tags = true) {
    $js_code = 'console.log(' . json_encode($output, JSON_HEX_TAG) . ');';
    if ($with_script_tags) {
        $js_code = '<script>' . $js_code . '</script>';
    }
    echo $js_code;
}


function tep_get_products_video_url($products_id, $language_id)
{
    $product_query = tep_db_query("select products_video_url from products_description where products_id = '" . (int) $products_id . "' and language_id = '" . (int) $language_id . "'");
    $product       = tep_db_fetch_array($product_query);
    
    return $product['products_video_url'];
}


function tep_get_models($models_array = '') // Function modified from tep_get_manufacturers()
{
    global $language, $first, $last;
    if (!is_array($models_array))
        $models_array = array();
    $models_query = tep_db_query("SELECT products_id,
                                         products_model 
                                  FROM products 
                                  ORDER BY products_model");
    $count        = 0;
    while ($models = tep_db_fetch_array($models_query)) {
        if ($count == 0) {
            $first = $models['products_model'];
        }
        $models_array[] = array(
            'id' => $models['products_id'],
            'text' => $models['products_model']
        );
        $count++;
        $last = $models['products_model'];
    }
    
    return $models_array;
}
// end mark

$action = (isset($_GET['action']) ? $_GET['action'] : '');
$debug = 'start_';
if (@tep_not_null($action)) {
    // Ultimate SEO URLs v2.2d
    // If the action will affect the cache entries
    if (preg_match("/(insert|update|setflag)/i", $action))
        include_once('includes/reset_seo_cache.php');
    switch ($action) {
//sort order
           case 'insert_product':
        case 'update_product':
			if (isset($_GET['pID']))
                $products_id = tep_db_prepare_input($_GET['pID']);
            $products_date_available = tep_db_prepare_input($_POST['products_date_available']);
            
            $products_date_available = (date('Y-m-d') < $products_date_available) ? $products_date_available : 'null';
            
            $sql_data_array                  = array(
                'products_quantity' => (int) tep_db_prepare_input($_POST['products_quantity']),
                'products_model' => tep_db_prepare_input($_POST['products_model']),
                'products_price' => tep_db_prepare_input($_POST['products_price']),
                'products_date_available' => $products_date_available,
                'products_weight' => (float) tep_db_prepare_input($_POST['products_weight']),
                'products_status' => tep_db_prepare_input($_POST['products_status']),
		'products_google_status' => tep_db_prepare_input($_POST['products_google_status']),
                'products_tax_class_id' => tep_db_prepare_input($_POST['products_tax_class_id']),
                'products_sort_order' => tep_db_prepare_input($_POST['products_sort_order']),
                'products_request_quote' => (int) tep_db_prepare_input($_POST['products_request_quote']),
                'manufacturers_id' => (int) tep_db_prepare_input($_POST['manufacturers_id'])
            );
            $sql_data_array['products_gtin'] = (@tep_not_null($_POST['products_gtin'])) ? str_pad(tep_db_prepare_input($_POST['products_gtin']), 14, '0', STR_PAD_LEFT) : 'null';
            $full_data_array = $sql_data_array;
            
            $products_image = new upload('products_image');
            $products_image->set_destination(DIR_FS_CATALOG_IMAGES);
            if ($products_image->parse() && $products_image->save()) {
                $sql_data_array['products_image'] = tep_db_prepare_input($products_image->filename);
			}
            $full_data_array['products_image_filename'] = tep_db_prepare_input($_POST['products_image_filename']);
			//echo 'fdr: ' . $full_data_array['products_image_filename'];
            //delete image thumbnails
            foreach (glob(DIR_FS_CATALOG_IMAGES . 'thumbs/*/' . $products_image->filename) as $filename) {
                @unlink($filename);
            }
            // end delete image thumbnails
            
            if ($action == 'insert_product') {
                $insert_sql_data = array(
                    'products_date_added' => 'now()'
                );
                
                $sql_data_array = array_merge($sql_data_array, $insert_sql_data);
                $full_data_array = array_merge($sql_data_array, $full_data_array);
                tep_db_perform(TABLE_PRODUCTS, $sql_data_array);
                $products_id = tep_db_insert_id();
                
                tep_db_query("insert into products_to_categories (products_id, categories_id) values ('" . (int) $products_id . "', '" . (int) $current_category_id . "')");
            } elseif ($action == 'update_product') {
                $update_sql_data = array(
                    'products_last_modified' => 'now()'
                );
                
                $sql_data_array = array_merge($sql_data_array, $update_sql_data);
                $full_data_array = array_merge($sql_data_array, $full_data_array);
                tep_db_perform(TABLE_PRODUCTS, $sql_data_array, 'update', "products_id = '" . (int) $products_id . "'");
            }
            
            $languages = tep_get_languages();
            for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                $language_id     = $languages[$i]['id'];
                /* cleanup  */
                $productURL      = str_replace(array(
                    'http://',
                    'https://'
                ), '', $_POST['products_url'][$language_id]);
                $productVideoURL = str_replace(array(
                    'http://',
                    'https://'
                ), '', $_POST['products_video_url'][$language_id]);
                
                $sql_data_array                             = array(
                    'products_name' => tep_db_prepare_input($_POST['products_name'][$language_id]),
                    'products_description' => tep_db_prepare_input($_POST['products_description'][$language_id]),
                    'products_url' => tep_db_prepare_input($productURL)
                );
                $sql_data_array['products_seo_description'] = tep_db_prepare_input($_POST['products_seo_description'][$language_id]);
                $sql_data_array['products_seo_keywords']    = tep_db_prepare_input($_POST['products_seo_keywords'][$language_id]);
                $sql_data_array['products_seo_title']       = tep_db_prepare_input($_POST['products_seo_title'][$language_id]);
                $sql_data_array['products_video_url']       = tep_db_prepare_input($productVideoURL);
                
                if ($action == 'insert_product') {
                    $insert_sql_data = array(
                        'products_id' => $products_id,
                        'language_id' => $language_id
                    );
                    
                    $sql_data_array = array_merge($sql_data_array, $insert_sql_data);
                    $full_data_array = array_merge($sql_data_array,$full_data_array);
                    tep_db_perform(TABLE_PRODUCTS_DESCRIPTION, $sql_data_array);
                } elseif ($action == 'update_product') {
					$full_data_array = array_merge($sql_data_array,$full_data_array);
                    tep_db_perform(TABLE_PRODUCTS_DESCRIPTION, $sql_data_array, 'update', "products_id = '" . (int) $products_id . "' and language_id = '" . (int) $language_id . "'");
                }
            }
			          // start indvship
			$tmp = tep_db_prepare_input($_POST['products_ship_price']);
			if (is_numeric($tmp))	$tmp_products_ship_price = round($tmp);
			$tmp = tep_db_prepare_input($_POST['products_ship_qty']);
			if (is_numeric($tmp))	$tmp_products_ship_qty = round($tmp);
			if ($tmp_products_ship_qty == 0) {
				$tmp_products_ship_qty = null;
			};
            // end indvship
            
          /*  $pi_sort_order = 0;
            $piArray       = array(
                0
            );
			foreach ($_POST as $key => $value) {
                // Update existing large product images
                if (preg_match('/^products_image_large_([0-9]+)$/', $key, $matches)) {
                    $pi_sort_order++;
                    
                    $sql_data_array = array(
                        'htmlcontent' => tep_db_prepare_input($_POST['products_image_htmlcontent_' . $matches[1]]),
                        'sort_order' => $pi_sort_order
                    );
                    
                    $t = new upload($key);
                    $t->set_destination(DIR_FS_CATALOG_IMAGES);
                    if ($t->parse() && $t->save()) {
                        $sql_data_array['image'] = tep_db_prepare_input($t->filename);
                    }
                    $full_data_array = array_merge($sql_data_array,$full_data_array);
                    tep_db_perform(TABLE_PRODUCTS_IMAGES, $sql_data_array, 'update', "products_id = '" . (int) $products_id . "' and id = '" . (int) $matches[1] . "'");
                    
                    $piArray[] = (int) $matches[1];
                } elseif (preg_match('/^products_image_large_new_([0-9]+)$/', $key, $matches)) {
                    // Insert new large product images
                    $sql_data_array = array(
                        'products_id' => (int) $products_id,
                        'htmlcontent' => tep_db_prepare_input($_POST['products_image_htmlcontent_new_' . $matches[1]])
                    );
                    
                    $t = new upload($key);
                    $t->set_destination(DIR_FS_CATALOG_IMAGES);
                    if ($t->parse() && $t->save()) {
                        $pi_sort_order++;
                        
                        $sql_data_array['image']      = tep_db_prepare_input($t->filename);
                        $sql_data_array['sort_order'] = $pi_sort_order;
                        $full_data_array = array_merge($sql_data_array,$full_data_array);
                        tep_db_perform(TABLE_PRODUCTS_IMAGES, $sql_data_array);
                        
                        $piArray[] = tep_db_insert_id();
                    }
                }
            }
          
            $product_images_query = tep_db_query("select image from products_images where products_id = '" . (int) $products_id . "' and id not in (" . implode(',', $piArray) . ")");
            if (tep_db_num_rows($product_images_query)) {
                while ($product_images = tep_db_fetch_array($product_images_query)) {
                    $duplicate_image_query = tep_db_query("select count(*) as total from products_images where image = '" . tep_db_input($product_images['image']) . "'");
                    $duplicate_image       = tep_db_fetch_array($duplicate_image_query);
                    
                    if ($duplicate_image['total'] < 2) {
                        if (file_exists(DIR_FS_CATALOG_IMAGES . $product_images['image'])) {
                            @unlink(DIR_FS_CATALOG_IMAGES . $product_images['image']);
                        }
                    }
                }
                
                tep_db_query("delete from products_images where products_id = '" . (int) $products_id . "' and id not in (" . implode(',', $piArray) . ")");
            }*/
            //indvship
            $sql_shipping_array    = array(
                'products_ship_key' => tep_db_prepare_input($_POST['products_ship_key']),
                'products_ship_methods_id' => tep_db_prepare_input($_POST['products_ship_methods_id']),
                'products_ship_price' => $tmp_products_ship_price,
                'products_ship_qty' => $tmp_products_ship_qty,
                'products_ship_flags' => tep_db_prepare_input($_POST['products_ship_flags_blockFreeShip']) . ',' . tep_db_prepare_input($_POST['products_ship_flags_digiProduct'])
            );
            $sql_shipping_id_array = array(
                'products_id' => (int) $products_id
            );
            $products_ship_query   = tep_db_query("SELECT * FROM products_shipping WHERE products_id = " . (int) $products_id);
            if (tep_db_num_rows($products_ship_query) > 0) {
                if (($_POST['products_ship_key'] == '') && ($_POST['products_ship_methods_id'] == '') && ($_POST['products_ship_price'] == '') && ($_POST['products_ship_qty'] == '') && ($_POST['products_ship_flags'] == '')) {
                    tep_db_query("DELETE FROM products_shipping where products_id = '" . (int) $products_id . "'");
                } else {
                    tep_db_perform('products_shipping', $sql_shipping_array, 'update', "products_id = '" . (int) $products_id . "'");
                }
            } else {
                if (($_POST['products_ship_key'] != '') || ($_POST['products_ship_methods_id'] != '') || ($_POST['products_ship_price'] != '') || ($_POST['products_ship_qty'] != '') || ($_POST['products_ship_flags_blockFreeShip'] != '') || ($_POST['products_ship_flags_digiProduct'] != '')) {
                    $sql_ship_array = array_merge($sql_shipping_array, $sql_shipping_id_array);
                    tep_db_perform('products_shipping', $sql_ship_array, 'insert');
                }
            }
            // end indvship 
			$full_data_array = array_merge($sql_data_array,$full_data_array);
            $full_data_array = array_merge($sql_shipping_id_array,$full_data_array);
            if (USE_CACHE == 'true') {
                tep_reset_cache_block('categories');
                tep_reset_cache_block('also_purchased');
            }
					
		
					
           // $tempLog->lwrite('Categories: Done ');
            tep_redirect(tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $products_id));
            break; 
    }
}

// check if the catalog image directory exists
if (is_dir(DIR_FS_CATALOG_IMAGES)) {
    if (!tep_is_writable(DIR_FS_CATALOG_IMAGES))
        $messageStack->add(ERROR_CATALOG_IMAGE_DIRECTORY_NOT_WRITEABLE, 'error');
} else {
    $messageStack->add(ERROR_CATALOG_IMAGE_DIRECTORY_DOES_NOT_EXIST, 'error');
}

require('includes/template_top.php');


// ---- Mark
if ($action == 'new_product') {
    $parameters                             = array(
        'products_name' => '',
        'products_description' => '',
        'products_url' => '',
        'products_id' => '',
        'products_quantity' => '',
        'products_model' => '',
        'products_image' => '',
        'products_larger_images' => array(),
        'products_price' => '',
        'products_weight' => '',
        'products_date_added' => '',
        'products_last_modified' => '',
        'products_date_available' => '',
        'products_status' => '',
		'products_google_status' => '',
        'products_tax_class_id' => '',
        'products_video_url' => '',
        'products_sort_order' => '',
        'products_request_quote' => '',
        'manufacturers_id' => ''
    );
    $parameters['products_gtin']            = '';
    $parameters['products_seo_description'] = '';
    $parameters['products_seo_keywords']    = '';
    $parameters['products_seo_title']       = '';
    
    $pInfo = new objectInfo($parameters);
    
    if (isset($_GET['pID']) && empty($_POST)) {
        //$product_query = tep_db_query("select pd.products_name, pd.products_description, pd.products_url, p.products_id, p.products_quantity, p.products_model, p.products_image, p.products_price, p.products_weight, p.products_date_added, p.products_last_modified, date_format(p.products_date_available, '%Y-%m-%d') as products_date_available, p.products_status, p.products_tax_class_id, p.manufacturers_id, p.products_gtin from products p, products_description pd where p.products_id = '" . (int)$_GET['pID'] . "' and p.products_id = pd.products_id and pd.language_id = '" . (int)$languages_id . "'");
        $product_query = tep_db_query("select pd.products_name, pd.products_description, pd.products_url, p.products_id, p.products_quantity, p.products_model, p.products_image, p.products_price, p.products_weight, p.products_date_added, p.products_last_modified, date_format(p.products_date_available, '%Y-%m-%d') as products_date_available, p.products_status, p.products_google_status, p.products_sort_order, p.products_tax_class_id, p.manufacturers_id, p.products_gtin, p.products_request_quote, pd.products_video_url from products p, products_description pd where p.products_id = '" . (int) $_GET['pID'] . "' and p.products_id = pd.products_id and pd.language_id = '" . (int) $languages_id . "'");
        $product       = tep_db_fetch_array($product_query);
        
        $pInfo->objectInfo($product);
        // start indvship
        $products_shipping_query = tep_db_query("SELECT * FROM products_shipping WHERE products_id=" . (int) $_GET['pID']);
        while ($products_shipping = tep_db_fetch_array($products_shipping_query)) {
            $products_ship_key        = $products_shipping['products_ship_key'];
            $products_ship_methods_id = $products_shipping['products_ship_methods_id'];
            $products_ship_price      = $products_shipping['products_ship_price'];
            $products_ship_qty        = $products_shipping['products_ship_qty'];
            $products_ship_flags      = $products_shipping['products_ship_flags'];
        }
        $shipping = array(
            'products_ship_methods_id' => $products_ship_methods_id,
            'products_ship_key' => $products_ship_key,
            'products_ship_price' => $products_ship_price,
            'products_ship_qty' => $products_ship_qty,
            'products_ship_flags' => $products_ship_flags
        );
        $pInfo->objectInfo($shipping);
        // end indvship
        
        
        if (!isset($pInfo->products_status)){
            $pInfo->products_status = '1';
	}
	if (!isset($pInfo->products_google_status)){
            $pInfo->products_google_status = '1';
	}
        $sFlagsA = preg_split("/[:,]/", $products_ship_flags);
        switch ($sFlagsA[0]) {
            case '1':
                $products_blockFreeShip_yes = true;
                $products_blockFreeShip_no  = false;
                break;
            case '0':
            default:
                $products_blockFreeShip_yes = false;
                $products_blockFreeShip_no  = true;
        }
        switch ($sFlagsA[1]) {
            case '1':
                $products_digiProduct_yes = true;
                $products_digiProduct_no  = false;
                break;
            case '0':
            default:
                $products_digiProduct_yes = false;
                $products_digiProduct_no  = true;
        }
        switch ($product['products_request_quote']) {
            case '1':
                $products_request_quote_yes = true;
                $products_request_quote_no  = false;
                break;
            case '0':
            default:
                $products_request_quote_yes = false;
                $products_request_quote_no  = true;
        } // end indvship
        $product_images_query = tep_db_query("select id, image, htmlcontent, sort_order from products_images where products_id = '" . (int) $product['products_id'] . "' order by sort_order");
        while ($product_images = tep_db_fetch_array($product_images_query)) {
            $pInfo->products_larger_images[] = array(
                'id' => $product_images['id'],
                'image' => $product_images['image'],
                'htmlcontent' => $product_images['htmlcontent'],
                'sort_order' => $product_images['sort_order']
            );
        }
    }
    
    $manufacturers_array = array(
        array(
            'id' => '',
            'text' => TEXT_NONE
        )
    );
    $manufacturers_query = tep_db_query("select manufacturers_id, manufacturers_name from manufacturers order by manufacturers_name");
    while ($manufacturers = tep_db_fetch_array($manufacturers_query)) {
        $manufacturers_array[] = array(
            'id' => $manufacturers['manufacturers_id'],
            'text' => $manufacturers['manufacturers_name']
        );
    }
    
    $tax_class_array = array(
        array(
            'id' => '0',
            'text' => TEXT_NONE
        )
    );
    $tax_class_query = tep_db_query("select tax_class_id, tax_class_title from tax_class order by tax_class_title");
    while ($tax_class = tep_db_fetch_array($tax_class_query)) {
        $tax_class_array[] = array(
            'id' => $tax_class['tax_class_id'],
            'text' => $tax_class['tax_class_title']
        );
    }
    
    $languages = tep_get_languages();
    
    if (!isset($pInfo->products_status)){
        $pInfo->products_status = '1';
    }
    switch ($pInfo->products_status) {
        case '0':
            $in_status  = false;
            $out_status = true;
            break;
        case '1':
        default:
            $in_status  = true;
            $out_status = false;
    }
    
    if (!isset($pInfo->products_google_status)){
        $pInfo->products_google_status = '1';
	}
    switch ($pInfo->products_google_status) {
        case '0':
            $in_google_status  = false;
            $out_google_status = true;
            break;
        case '1':
        default:
            $in_google_status = true;
            $out_google_status = false;
    }
    
    $form_action = (isset($_GET['pID'])) ? 'update_product' : 'insert_product'; ?>
	

	
<script type="text/javascript"><!--
var tax_rates = new Array();
<?php
    for ($i = 0, $n = sizeof($tax_class_array); $i < $n; $i++) {
        if ($tax_class_array[$i]['id'] > 0) {
            echo 'tax_rates["' . $tax_class_array[$i]['id'] . '"] = ' . tep_get_tax_rate_value($tax_class_array[$i]['id']) . ';' . "\n";
        }
    } ?>

function doRound(x, places) {
  return Math.round(x * Math.pow(10, places)) / Math.pow(10, places);
}

function getTaxRate() {
  var selected_value = document.forms["new_product"].products_tax_class_id.selectedIndex;
  var parameterVal = document.forms["new_product"].products_tax_class_id[selected_value].value;

  if ( (parameterVal > 0) && (tax_rates[parameterVal] > 0) ) {
    return tax_rates[parameterVal];
  } else {
    return 0;
  }
}

function updateGross() {
  var taxRate = getTaxRate();
  var grossValue = document.forms["new_product"].products_price.value;

  if (taxRate > 0) {
    grossValue = grossValue * ((taxRate / 100) + 1);
  }

  document.forms["new_product"].products_price_gross.value = doRound(grossValue, 4);
}

function updateNet() {
  var taxRate = getTaxRate();
  var netValue = document.forms["new_product"].products_price_gross.value;

  if (taxRate > 0) {
    netValue = netValue / ((taxRate / 100) + 1);
  }

  document.forms["new_product"].products_price.value = doRound(netValue, 4);
}
//--></script>
    <?php echo tep_draw_form('new_product', 'product_edit.php', 'cPath=' . $cPath . (isset($_GET['pID']) ? '&pID=' . $_GET['pID'] : '') . '&action=' . $form_action, 'post', 'enctype="multipart/form-data" id="new_product_form"'); ?>
   
   
   <table border="0" width="90%" cellspacing="0" cellpadding="2">
      <tr>
        <td colspan="4"><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td class="pageHeading" colspan="4"><?php echo sprintf(TEXT_NEW_PRODUCT, tep_output_generated_category_path($current_category_id)); ?></td>
            <td class="pageHeading" align="right"><?php echo tep_draw_separator('pixel_trans.gif', HEADING_IMAGE_WIDTH, HEADING_IMAGE_HEIGHT); ?></td>
          </tr>
        </table></td>
      </tr>
      <tr>
        <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      </tr>

         <tr>
            <td class="main"></td>


	<?php
	if (@tep_not_null($pInfo->products_image)) {
		$pi_query = tep_db_query("select image, htmlcontent from products_images where products_id = '" . (int) $pInfo->products_id . "' order by sort_order");
	} ?>

<?php
    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) { ?>

          <tr bgcolor="#eeeeee">
            <td class="main">Seo Title</td>
            <td class="main" colspan=4><?php echo tep_draw_input_field('products_seo_title[' . $languages[$i]['id'] . ']', (empty($pInfo->products_id) ? '' : tep_get_products_seo_title($pInfo->products_id, $languages[$i]['id'])), 'style="width: 500px;"'); ?></td>
          </tr>
<?php
    } ?>
         <tr>
            <td class="main"><?php echo TEXT_PRODUCTS_MANUFACTURER; ?></td>
            <td class="main"><?php echo tep_draw_pull_down_menu('manufacturers_id', $manufacturers_array, $pInfo->manufacturers_id); ?></td>
        
            <td class="main"><?php echo TEXT_PRODUCTS_MODEL; ?></td>
            <td class="main"><?php echo tep_draw_input_field('products_model', $pInfo->products_model); ?></td>
          </tr>
		  <tr>
               <td class="main"><?php echo 'GTIN:'; ?><script type="text/javascript"><!--

//--></script></td>
	  <?php // for ($i=0, $n=sizeof($languages); $i<$n; $i++) { ?>
	  <td class="main"> <?php echo tep_draw_input_field('products_gtin', $pInfo->products_gtin) . ' (UPC, EAN, ISBN or MPN)'; ?></td>
		</tr><tr><td colspan="4" style="max-width:1024px"><hr></td></tr>
		  <tr><td class="main">
		<script type="text/javascript">
		  $( document ).ready(function() {
			if (<?php if ($products_digiProduct_yes) {echo 1;} else {echo 0;} ?>){
				$('.indvShip').fadeTo('slow',.25);
					}
					$('#products_digiProduct_yes').click(function() {
						$('.indvShip').fadeTo('slow',.25);
					});
					$('#products_digiProduct_no').click(function() {
						$('.indvShip').fadeTo('slow',1);
					});
				});
      </script>
      
                <?php echo 'Digital Delivery:'; ?></td>
            
                <td class="main"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;No:&nbsp;' . tep_draw_radio_field('products_ship_flags_digiProduct', '0', $products_digiProduct_no, null, 'id="products_digiProduct_no"') . '&nbsp;Yes:&nbsp;' . tep_draw_radio_field('products_ship_flags_digiProduct', '1', $products_digiProduct_yes, null, 'id="products_digiProduct_yes"'); ?></td>
    
    </tr>
      <?php // start indvship  ?> 
          <tr class="indvShip">
            <td class="main">Group</td>
            <td class="main" valign=top><?php echo tep_draw_input_field('products_ship_key', $pInfo->products_ship_key);
    if (@tep_not_null($pInfo->products_ship_key))
        echo 'notnull';
    else
        echo 'null'; ?></td><td class="main">Current Keywords:</td>
            <td rowspan='4' class="main"> <table cellspacing="0" cellpadding="2" width="100%" border="0">
            <?php
    $i      = 0;
    $select = tep_db_query("SELECT * FROM `products_shipping` GROUP BY `products_ship_key` ORDER BY `products_ship_key` DESC");
    while ($keywords = tep_db_fetch_array($select)) {
        $i++;
        if ($i == 1) {
            echo "<tr><td>";
        } else {
            echo "<td>&nbsp;</td><td>";
        }
        echo "<a href='javascript:null();' onclick=\"document.forms['new_product'].products_ship_key.value='" . $keywords['products_ship_key'] . "';document.forms['new_product'].products_ship_price.value='" . $keywords['products_ship_price'] . "';document.forms['new_product'].products_ship_qty.value='" . $keywords['products_ship_qty'] . "';\">" . $keywords['products_ship_key'] . "";
        if ($i == 1) {
            echo "</td>";
        } else {
            echo "</td></tr>";
            $i = 0;
        }
    }
     ?></table></td>
    </tr> <!-- end Zipcode --> <!-- Indvship -->
          
    <tr class="indvShip">
            <td class="main" style="width:210px"><?php echo 'Indv. Shipping Price:'; ?></td>
            <td class="main"><?php echo tep_draw_input_field('products_ship_price', $pInfo->products_ship_price);
    if (@tep_not_null($pInfo->products_ship_price))
        echo 'notnull';
    else
        echo 'null'; ?></td>
    </tr>
    <tr class="indvShip">
            <td class="main"><?php echo 'Qty to qualify for free shipping:'; ?></td>
            <td class="main"><?php echo tep_draw_input_field('products_ship_qty', $pInfo->products_ship_qty);
    if (@tep_not_null($pInfo->products_ship_qty))
        echo 'notnull';
    else
        echo 'null'; ?></td>
          </tr>
    <tr class="indvShip">
             <td class="main">
                <?php echo 'Exempt free shipping > £200?:'; ?></td>
            
                <td class="main"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;No:&nbsp;' . tep_draw_radio_field('products_ship_flags_blockFreeShip', '0', $products_blockFreeShip_no) . '&nbsp;Yes:&nbsp;' . tep_draw_radio_field('products_ship_flags_blockFreeShip', '1', $products_blockFreeShip_yes); ?></td>
                
        <!-- end Indvship -->
            
          
    </tr>
    <tr class=""><td colspan="4" style="max-width:930px"><hr></td></tr>
          <tr>
            <td class="main"><?php echo TEXT_PRODUCTS_PRICE_NET; ?></td> 
          <td class="main"><?php echo tep_draw_input_field('products_price', $pInfo->products_price); ?>   </td>
 <td class="main">
				<?php echo 'Show "Request Quote" Button:'; ?></td><td class="main">
                <?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;No:&nbsp;' . tep_draw_radio_field('products_request_quote', '0', $products_request_quote_no) . '&nbsp;Yes:&nbsp;' . tep_draw_radio_field('products_request_quote', '1', $products_request_quote_yes); ?></td>
            

          </tr> 
          <tr>
            <td class="main"><?php echo TEXT_PRODUCTS_PRICE_GROSS; ?></td> 
            <td class="main"><?php echo tep_draw_input_field('products_price_gross', $pInfo->products_price); ?></td>
            <td class="main"><?php echo TEXT_PRODUCTS_TAX_CLASS; ?></td>
            <td class="main">
            <?php echo tep_draw_pull_down_menu('products_tax_class_id', $tax_class_array, 1, 'id="products_tax_class_id"'); ?></td> 
            
        
          </tr><tr><td colspan="4" style="max-width:930px"><hr></td></tr>
<script type="text/javascript"><!--
$( document ).ready(function() {
    $("input[name=products_price]").keyup(updateGross);    
    $("input[name=products_price_gross").keyup(updateNet);    
  //  $("[name=products_tax_class_id]").onchange(updateGross);    
    updateGross();
});

//--></script>
<?php
    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) { ?>
         <tr>
            <td  class="main" valign="top" colspan="4"><?php
        echo TEXT_PRODUCTS_IMAGE; ?></td>
		  
		<tr>
            
<?php
    } ?>
		 <div id="piGal" style="float: left;max-width:100%;min-width:300px">
        <td colspan="4" class="main">	
	 <div style="max-width:200px" >
			<?php echo '<br />' . (@tep_not_null($pInfo->products_image) ? '<a href="' . DIR_WS_CATALOG_IMAGES . $pInfo->products_image . '" target="_blank">
			<img class="img-responsive" src="' . DIR_WS_CATALOG_IMAGES . $pInfo->products_image . '"></a>' . 
			tep_draw_hidden_field('products_image_filename', $pInfo->products_image): '') . tep_draw_file_field('products_image'); ?>
		 </div>
	
		    </div>  
	 <div id="piGal" style="float: left;max-width:100%;min-width:300px">YouTube Video
	 <?php   for ($i = 0, $n = sizeof($languages); $i < $n; $i++) { 
			 echo tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']) . '&nbsp;' . tep_draw_input_field('products_video_url[' . $languages[$i]['id'] . ']', (isset($products_video_url[$languages[$i]['id']]) ? stripslashes($products_video_url[$languages[$i]['id']]) : tep_get_products_video_url($pInfo->products_id, $languages[$i]['id'])), 'size="55" '); 
         
   	    } ?>
      <?php
            if (@tep_not_null($pInfo->products_video_url)) { ?>
           <div class="videoWrapper">
                <div class="video_iframe">
                    <!-- a transparent image is preferable -->
                    <img class="videoRatio" src="<?php
                echo DIR_WS_CATALOG_IMAGES . 'placeholders/16x9.png'; ?>"/>
                    <iframe src="https://<?php
                echo $pInfo->products_video_url; ?>" frameborder="0" allowfullscreen="1"></iframe>
                </div>
            </div>
            <?php
            } ?>
   </div></td>
		 
		 
          </tr>  <?php
    // end indvship 
    if (isset($pInfo->products_tax_class_id)) {
        $taxSelection = $pInfo->products_tax_class_id;
    } else {
        $taxSelection = 1;
    } 
    //EPP: Attributes Table
    ?>
        </tr>
        <tr>
            <td colspan="4"><?php 
		
			$attributes_sql = "select * from products_attributes pa, products_options po, products_options_values pov, products_description pd WHERE pa.products_id = pd.products_id and po.products_options_id = pa.options_id and pov.products_options_values_id = pa.options_values_id and pd.language_id = '" . (int)$languages_id . "' and pa.products_id = '" . $pInfo->products_id . "' order by pa.products_attributes_sort_order, po.products_options_name, pov.products_options_values_name";
			$attributes = "select pa.* from products_attributes pa left join products_description pd on pa.products_id = pd.products_id and pd.language_id = '" . (int)$languages_id . "' and pa.products_id = '" . $pInfo->products_id . "' order by pd.products_name";
			//echo $attributes_sql;
			?>
			</td>
          </tr>
		        <tr>
            <td colspan="4"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
		  <tr><td colspan="4">
		  <div  class="col-xs-10">
		  

		<div class=" panel panel-default">
			<div class="panel-header">
			<?php if (!empty($pInfo->products_id)){ ?> 
			<div class="panel-body">
				<strong>Attributes</strong></div>		 
		   </div>
		   
		  <table class="table table-striped table-draggable" id="attributesTable">	
			<thead>
			<tr>
				<td>&nbsp;</td>
				<td>Option</td><td>Value</td><td align="center">Price</td><td align="center">Prefix</td><td align="center">Default</td><td align="center">DependsOn</td><td align="center">Sort Order</td><td align="center" colspan="2">Actions</td>
				</tr>
			</thead>
			<tbody>	
					
			<?php
			$rows = 0;
			$attributes_query = tep_db_query($attributes_sql);
			while ($attributes_values = tep_db_fetch_array($attributes_query)) {
				if(strpos($attributes_values['dependson_options_values_id'],',') !== false){
					$valueIds = explode(',',$attributes_values['dependson_options_values_id']);
					$dependson_options_values_name = "";
					foreach ($valueIds as $value){
						if ($dependson_options_values_name != ""){
							$dependson_options_values_name .=  ', ';
						}
						$dependson_options_values_name .= tep_values_name($value);
					}
					
				} else {
					$dependson_options_values_name = tep_values_name($attributes_values['dependson_options_values_id']);
				}
				$products_name_only = tep_get_products_name($attributes_values['products_id']);
				$options_name = $attributes_values['products_options_name'];
				$values_name = tep_values_name($attributes_values['options_values_id']);
				$dependson_options_name = tep_options_name($attributes_values['dependson_options_id']);
				
				//echo $attributes_values['dependson_options_id'] . ' > ' . $attributes_values['dependson_options_values_id'];
				
				if (@tep_not_null($attributes_values['dependson_options_id']) && @tep_not_null($attributes_values['dependson_options_values_id'])){
					$dependsOn_string = $dependson_options_name . ': ' . $dependson_options_values_name;
				} else {
					$dependsOn_string = "None";
				}			
				
				
				$rowId =  $attributes_values['products_options_id'] . '_' . $attributes_values['products_options_values_id'];
				?>
				<tr <?php echo 'data-attributesid="' . $attributes_values['products_attributes_id'] . '" data-rowNum="' . $rowId . '" class="" id="attributesTableRow_' . $rowId . '"'?>>
				 	<td class="portletTD"><div class="portlet">&nbsp;</div></td>
					<td <?php echo 'id="attributesTable_optionsName_' . $rowId . '" data-options_id="' . $attributes_values['products_options_id'] . '"';?> ><?php echo $options_name; ?></td>
					<td <?php echo 'id="attributesTable_optionsValueName_' . $rowId . '" data-values_id="' . $attributes_values['products_options_values_id'] . '"';?> ><?php echo $values_name; ?></td>
					<td <?php echo 'id="attributesTable_optionsValuePrice_' . $rowId . '"'?> align="center"><?php echo $attributes_values["options_values_price"]; ?></td>
					<td <?php echo 'id="attributesTable_optionsValuePricePrefix_' . $rowId . '"'?> align="center"><?php echo $attributes_values["price_prefix"]; ?></td>					
					<td <?php echo 'id="attributesTable_optionsValuePriceDefault_' . $rowId . '"'?> align="center"><?php echo $attributes_values["attribute_default"] ? 'Yes' : 'No'; ?></td>					
					<td <?php echo 'id="attributesTable_DependsOn_' . $rowId . '"' . ' data-dependson_options_id="' . $attributes_values['dependson_options_id'] . '" data-dependson_values_id="' . $attributes_values['dependson_options_values_id'] . '"'?> ><?php echo $dependsOn_string; ?></td>
					<td style="width:5%" class="" style="width:10px"><input class="indexSO attributesSOinput" type="text" name="" size="1" maxlength="4" value="<?php echo $attributes_values['products_attributes_sort_order'];?>"></td>
					<td class="attributesOptionsEdit listsOptionsEdit">e</td>
					<td class="attributesOptionsDelete listsOptionsDelete">x</td>
				</tr>
			 <?php 
				$rows++;
			 } ?> 
			 </tbody>
			 </table>
			 <div class="panel-footer"> 			
				<?php $options = tep_db_query("select * from products_options where language_id = '" . $languages_id . "' order by products_options_name");?>
					<div class="row">
						<div id="attributes_left_container" class="col-xs-6">
							<div id="attributes_options_container" class="form-inline form-group">
							<label>Attributes: </label><br />
								<select id="attributes_options_id" class="form-control"><option disabled="" selected="" value="">Options</option>
									<?php
										while ($options_values = tep_db_fetch_array($options)) {
										  echo '<option name="attributes_' . $options_values['products_options_name'] . '" value="' . $options_values['products_options_id'] . '">' . $options_values['products_options_name'] . '</option>';
										} 
									?>
								</select>		
								<div id="attributes_values_id_container" class="form-group">
									<select id="attributes_values_id" class="form-control" data-current_id="-1">
										<option disabled selected value>Values</option>
									</select>	
								</div>
								<div id="attributes_values_id_btns_container" class="btn-group"></div>
							</div>
						
						<div id="attributes_misc_container" class="form-inline form-group">
						<div id="attributes_value_price_container" class="form-group">
							<input type="text" name="attributes_value_price" id="attributes_value_price" size="6" placeholder="Price" class="form-control">&nbsp;&nbsp;
						</div>
						
						<div id="attributes_prefix_container" class="btn-group ">
							<button id="attributes_prefix_plus_btn" class="btn btn-success attributes_prefix_btns" type="button">+</button>
							<button id="attributes_prefix_minu_btn" class="btn btn-default attributes_prefix_btns" type="button">-</button>	
							<button id="attributes_prefix_mult_btn" class="btn btn-default attributes_prefix_btns" type="button">*</button>							               
							
						</div>		<input type="hidden" name="attributes_price_prefix" id="attributes_price_prefix" value="+"> 
						<div class="checkbox"><label><input type="checkbox" name="attributes_attribute_default" id="attributes_attribute_default">&nbsp;Default</label></div>
						</div>
					</div>
					
					<div id="attributes_dependson_container" class="col-xs-5">
						<label>Depends On: </label>
						<select id="attributes_dependson_options_id" data-productsid="<?php echo (int) $product['products_id'] ?>" class="form-control"><option disabled selected value>Options</option>
						<option>None</option>
							<?php
								$options = tep_db_query("select * from products_options where language_id = '" . $languages_id . "' order by products_options_name");
								while ($options_values = tep_db_fetch_array($options)) {
								  echo '<option name="attributes_dependson_' . $options_values['products_options_name'] . '" value="' . $options_values['products_options_id'] . '">' . $options_values['products_options_name'] . '</option>';
								} 
							?>
						</select>
				
					
						<select multiple id="attributes_dependson_values_id" class="form-control">
							<option disabled selected value>Values</option>						
						</select>
						<div id="attributes_dependson_values_id_btns_container" class="btn-group"></div>
					</div>
					<div id="attributes_submit" class="col-xs-1 form-group">		
						<label>Save: </label>
						<button id="attributes_insert_btn" class="btn btn-primary pull-right" type="button">Insert</button>   
					</div>
					</div>
				 </div>
			   </div>
<?php	   
//EPP: -- Attributes Script 
?>	
<script>
	$(document).ready(function(e) {		
		$("#attributes_insert_btn").click(function(e) {
			var error = 0;
			var culprit = "";
			var options_id = $('#attributes_options_id').val();
			var values_id = $('#attributes_values_id').val();	
			
			
			var dependson_options_id = $('#attributes_dependson_options_id').val();
			var dependson_values_id = $('#attributes_dependson_values_id').val();
			var value_price = $('#attributes_value_price').val();
			var price_prefix = encodeURIComponent($('#attributes_price_prefix').val());
           		var attribute_default = $('#attributes_attribute_default').prop("checked") == true ? 1 : 0;
            		var method = "product_attributes_addToProduct";
			$("#attributesTable tbody tr").each(function(){
				var row = $(this).data("rownum");
				var elmstring = "#attributesTable_optionsValueName_" + row;
				if ($(elmstring).data("values_id") == values_id) {
					 method = "product_attributes_addToProduct";
					 return false;
				}
			});
			$.get("api.php?action=" + method + "&products_id=<?php echo $pInfo->products_id ?>&options_id=" + options_id + "&values_id=" + values_id + "&value_price=" + value_price + "&price_prefix=" + price_prefix + "&attribute_default=" + attribute_default + "&dependson_options_id=" + dependson_options_id + "&dependson_values_id=" + dependson_values_id, function(data, status){
				if (status == "success") {
					if (data["Complete"] > 0) {										
						attributes_AddRow(data);								
						e.stopPropagation();					
					}
				}
			});
		});
		
		function attributes_AddRow(data) {	
			attribute_id = data["attributes"][0]["attribute_id"];
			options_name = data["attributes"][0]["options_name"];
			options_id = data["attributes"][0]["options_id"];
			values_id = data["attributes"][0]["values_id"];
			values_name = data["attributes"][0]["values_name"];
			value_price = data["attributes"][0]["value_price"];
			price_prefix = data["attributes"][0]["price_prefix"];
			attribute_default = data["attributes"][0]["attribute_default"] == "1" ? 'Yes' : 'No';
			dependson_options_id = data["attributes"][0]["dependson_options_id"];
			dependson_options_name = data["attributes"][0]["dependson_options_name"];
			dependson_values_id = data["attributes"][0]["dependson_values_id"];
			dependson_values_name = data["attributes"][0]["dependson_values_name"];
			
			var rowNum = options_id + '_' + values_id;
			
			var elements = $('<tr style="display:none" data-attributesid="' + attribute_id + '" data-rownum="' + rowNum + '" class="" id="attributesTableRow_' + rowNum + '">'
				+ '<td class="portletTD"><div class="portlet">&nbsp;</div></td>'
				+ '<td id="attributesTable_optionsName_' + rowNum + '" data-options_id="' + options_id + '">' + options_name + '</td>'
				+ '<td id="attributesTable_optionsValueName_' + rowNum + '" data-values_id="' + values_id + '">' + values_name + '</td>'
				+ '<td id="attributesTable_optionsValuePrice_' + rowNum + '" align="center">' + value_price + '</td>'
				+ '<td id="attributesTable_optionsValuePricePrefix_' + rowNum + '" align="center">' + price_prefix + '</td>'				
				+ '<td id="attributesTable_optionsValuePriceDefault_' + rowNum + '" align="center">' + attribute_default + '</td>'
				+ '<td id="attributesTable_DependsOn_' + rowNum + '" data-dependson_options_id="' + dependson_options_id + '" data-dependson_values_id="' + dependson_values_id + '">' + dependson_options_name + ': ' + dependson_values_name + '</td>'
				+ '<td style="width:5%" class=""><input class="indexSO attributesSOinput" type="text" name="" size="1" maxlength="4" value=""></td>'
				+ '<td class="attributesOptionsEdit listsOptionsEdit">e</td>'
				+ '<td class="attributesOptionsDelete listsOptionsDelete">x</td>'
			+ '</tr>');
			
			var row = $('#attributesTableRow_' + rowNum);					
			if (row.length) {
				row.replaceWith(elements);
				elements.fadeIn("slow");
			} else {
				elements.appendTo("#attributesTable").fadeIn("slow");
			}			
		}
		
		$("#attributesTable").on("click", ".attributesOptionsDelete", function(e) {
			var that = $(this);
			var method = "product_attributes_removeAttribute";
			$.get("api.php?action=" + method + "&products_id=<?php echo $pInfo->products_id ?>&products_attributes_id=" + that.parent().data("attributesid"), function(data, status){
				if (status == "success") {
					e.stopPropagation();
					that.parent().fadeOut("slow",function(){
						this.remove();
					});
				}
			});	
		});	
		
		
		$("#attributesTable").on("click",".attributesOptionsEdit",function(e) {
			var row = $(this).closest('tr').data('rownum');
			var options_id = $('#attributesTable_optionsName_' + row).data('options_id');
			var values_id = $('#attributesTable_optionsValueName_' + row).data('values_id');			
			var dependson_options_id = $('#attributesTable_DependsOn_' + row).data('dependson_options_id');
			var dependson_values_id = $('#attributesTable_DependsOn_' + row).data('dependson_values_id');
			var value_price = $('#attributesTable_optionsValuePrice_' + row).text().trim();
			var price_prefix = $('#attributesTable_optionsValuePricePrefix_' + row).text().trim();
            var attribute_default = $('#attributesTable_optionsValuePriceDefault_' + row).text().trim() == "Yes" ? 1 : 0;
			var $optionsSelect = $('#attributes_options_id');
			var $valuesSelect = $('#attributes_values_id');
			$optionsSelect.val(options_id);
			attributes_getValueList(null,values_id);
			$('#attributes_value_price').val(value_price);
			$('#attributes_price_prefix').val(price_prefix);			
			$('#attributes_prefix_plus_btn').trigger('change');
			$('#attributes_attribute_default').prop("checked", attribute_default);
			if ((dependson_options_id != null) && (dependson_values_id != null)){
				$('#attributes_dependson_options_id').val(dependson_options_id);
				attributes_dependsOn_getValueList(null,dependson_values_id.split(','));
			} else {
				$('#attributes_dependson_options_id').val(0);
				$('#attributes_dependson_values_id').html("<option disabled selected value>Values</option>");
			}			
		});	
		function attributes_updateValuesButtons(){
			var valuesSelectBtnsCont = $('#attributes_values_id_btns_container');
			var valuesSelectCont = $('#attributes_values_id_container');
			var valuesSelect = $('#attributes_values_id');
			var valuesSelectBtns = $(".valuesSelectBtns");
			var currentVal = valuesSelect.val();
			valuesSelectBtns.removeClass('active btn-success').addClass('btn-default');
			$("#valuesSelectBtn_" + currentVal).addClass('active btn-success');
		}
		
		function attributes_getValueList(e,selected_item){
			var valuesSelectBtnsCont = $('#attributes_values_id_btns_container');
			var valuesSelectCont = $('#attributes_values_id_container');
			var valuesSelect = $('#attributes_values_id');
			var options_select = $("#attributes_options_id");
			var method = "product_attributes_getValueList";
			if (valuesSelect.data("current_id") != options_select.val()){
				$.get("api.php?action=" + method + "&options_id=" + options_select.val(), function(data, status){				
					if (status == "success") {
						if (e){e.stopPropagation();}			
					//	attributes_values_id_btns_container = $("#attributes_values_id_btns_container");
						valuesSelectBtnsCont.empty();
						valuesSelect.empty().append($('<option disabled selected value>Values</option>'));
						var count = 0;
						var selected = "selected";
						for (x in data["attributes"]) {
							if (selected_item >= 0){
								if (selected_item == data["attributes"][x]["values_id"]){
									var selected = "selected";
								} else {
									var selected = "";
								}
							}
							valuesSelect.append($('<option id="valuesSelectItem_' +  count + '" value="' + data["attributes"][x]["values_id"] + '"' + selected  + '>' + data["attributes"][x]["values_name"] + '</option>'));
							valuesSelectBtnsCont.append($('<button type="button" id="valuesSelectBtn_' + data["attributes"][x]["values_id"] + '"' + ' data-value="' + data["attributes"][x]["values_id"] + '" class="btn btn-default valuesSelectBtns">' + data["attributes"][x]["values_name"] + '</button>'));
							var selected = "";												
						}
						attributes_updateValuesButtons();
						valuesSelectBtns = $(".valuesSelectBtns");
						if ((valuesSelectBtnsCont.width() > 450) || (valuesSelectBtnsCont.height() > 100)){
							valuesSelectBtnsCont.hide();
							valuesSelectCont.show();
						} else {
							valuesSelectBtnsCont.show();
							valuesSelectCont.hide();
							valuesSelectBtns.click(function(e) {
								valuesSelectBtns.removeClass('active btn-success').addClass('btn-default');
								$(this).removeClass('btn-default').addClass('active btn-success');
								valuesSelect.val($(this).data("value"));
							});
						}
					}					
					valuesSelect.prop('disabled', false);	
					valuesSelect.data("current_id",options_select.val());
				});
			} else if (selected_item >= 0){
				valuesSelect.val(selected_item);
				attributes_updateValuesButtons();
			}
		}
		
		$("#attributes_values_id").change(function(e, callback) {
			attributes_updateValuesButtons();					
			if (typeof callback === "function") callback();
		});
		
		$("#attributes_options_id").change(function(e, callback) {			
			attributes_getValueList(e);
			if (typeof callback === "function") callback();
		});        
		
		
		function attributes_dependsOn_getValueList(e,selected_items){
			//var dependson_valuesSelectBtnsCont = $('#attributes_dependson_values_id_btns_container');
			var dependson_valuesSelectCont = $('#attributes_dependson_values_id_container');
			var dependson_valuesSelect = $('#attributes_dependson_values_id');
			var dependson_options_select = $('#attributes_dependson_options_id');
			method = "product_attributes_getValueList";
			if (dependson_valuesSelect.data("current_id") != dependson_options_select.val()){
				$.get("api.php?action=" + method + "&options_id=" + dependson_options_select.val() + "&filter=" + dependson_options_select.data('productsid'), function(data, status){
					if (status == "success") {
						if (e){e.stopPropagation();}			
					//	attributes_dependson_values_id_btns_container = $("#attributes_dependson_values_id_btns_container");
						//dependson_valuesSelectBtnsCont.empty();
						dependson_valuesSelect.empty().append($('<option disabled selected value>Values</option>'));
						for (x in data["attributes"]) {
							dependson_valuesSelect.append($('<option value="' + data["attributes"][x]["values_id"] + '">' + data["attributes"][x]["values_name"] + '</option>'));
							//dependson_valuesSelectBtnsCont.append($('<button type="button" id="dependson_valuesSelectBtn_' + data["attributes"][x]["values_id"] + '" data-value="' + data["attributes"][x]["values_id"] + '" class="btn btn-default dependson_valuesSelectBtns">' + data["attributes"][x]["values_name"] + '</button>'));
						}					
					}
					dependson_valuesSelect.prop('disabled', false);
					dependson_valuesSelect.val(selected_items);
					if (typeof callback === "function") callback();
					valuesSelect.data("current_id",options_select.val());
				});
			} else if (selected_items != null){
				dependson_valuesSelect.val(selected_items);
				attributes_updateValuesButtons();
			}
		}
		
		
		
		
		$("#attributes_dependson_options_id").change(function(e, callback) {
			attributes_dependsOn_getValueList(e);
		});
		
	
		$(".attributes_prefix_btns").click(function(e) {
			$("#attributes_price_prefix").val($(this).text()).trigger('change');
				
		});	
		
		$('#attributes_price_prefix').on("change", function(e, callback) {
			$(".attributes_prefix_btns").removeClass("active btn-success").addClass('btn-default');
			var theText = $(this).val();
			if (theText == "+"){
				$("#attributes_prefix_plus_btn").addClass("active btn-success").removeClass('btn-default');
			} else if (theText == "-"){
				$("#attributes_prefix_minu_btn").addClass("active btn-success").removeClass('btn-default');
			}	
		});
		
	});
</script>

<?php } else { ?>
			<div class="panel-body">
				<strong>Save first to generate product id</strong>
				</div>
				<?php } ?> 
		   </div>
		</div>
			
<?php //EPP: Variations Table
 ?>
	<tr>
            <td colspan="4">
			
	
		 

              <ul id="piList">
<?php
    $pi_counter = 0;
    
    foreach ($pInfo->products_larger_images as $pi) {
        $pi_counter++;
        
        echo '                <li id="piId' . $pi_counter . '" class="ui-state-default" style="float:left; max-width:120px">'  . 
		'<span class="ui-icon ui-icon-arrowthick-2-n-s" style="float: right;"></span>' . 
		'<a id="large_image_' . $pi['id'] . '" data-imageid="' . $pi['id'] . '" class="imageDelete ui-icon ui-icon-trash" style="cursor:pointer;float: right;"></a>' . 
		'<strong>Image '  . $pi['id'] . '</strong><br />' . 
		'<a href="' . DIR_WS_CATALOG_IMAGES . $pi['image'] . '" target="_blank"><img class="img-responsive" src="' . DIR_WS_CATALOG_IMAGES . $pi['image'] . '"></a>' .
		tep_draw_file_field('products_image_large_' . $pi['id'],false,'style="width: 90px;overflow: hidden;"');
		
		//'<br /><br />' . TEXT_PRODUCTS_LARGE_IMAGE_HTML_CONTENT . '<br />' .
		//tep_draw_textarea_field('products_image_htmlcontent_' . $pi['id'], 'soft', '70', '3', $pi['htmlcontent']) . '</li>';
    } ?>
             </ul>
			<br style="clear:both">
              <a href="#" style="float: left;"></span><?php echo TEXT_PRODUCTS_ADD_LARGE_IMAGE; ?></a>

<div id="piDelConfirm" title="<?php echo TEXT_PRODUCTS_LARGE_IMAGE_DELETE_TITLE; ?>">
  <p><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span><?php echo TEXT_PRODUCTS_LARGE_IMAGE_CONFIRM_DELETE; ?></p>
</div>

<style type="text/css">
#piList { list-style-type: none; margin: 0; padding: 0; }
#piList li { margin: 5px 0; padding: 2px; }
</style>




<!-- Add this input field for batch image uploads -->
<input type="file" id="batchUpload" name="batchUpload[]" multiple />

<script>
// Function to handle batch image upload
$('#batchUpload').on('change', function(event) {
    let files = event.target.files;
    let formData = new FormData();
    
    $.each(files, function(index, file) {
        formData.append('batchUpload[]', file);
    });
    
    // Add additional data if needed
    formData.append('products_id', <?php echo $pInfo->products_id ?>);
    
    $.ajax({
        url: 'upload_batch.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            let imageData = JSON.parse(response);
            updateUI(imageData);
        },
        error: function(xhr, status, error) {
            console.error('Upload error:', status, error);
        }
    });
});

$('.imageDelete').on('click', function(event) {
     
var that = $(this);
			var method = "product_images_removeImage";
			$.get("api.php?action=" + method + "&products_id=<?php echo $pInfo->products_id ?>&image_id=" + that.data("imageid"), function(data, status){
				if (status == "success") {
					event.stopPropagation();
					that.parent().remove();
				}
			});	
});


// Function to update the UI with the newly uploaded images
function updateUI(imageData) {
    imageData.forEach(function(image) {
        let htmlContent = `
            <li id="piId${image.id}" class="ui-state-default" style="float:left; max-width:120px">
                <span class="ui-icon ui-icon-arrowthick-2-n-s" style="float: right;"></span>
                <a id="${'large_image_' + image.id});return false;" data-imageid="${image.id}" class="imageDelete ui-icon ui-icon-trash" style="cursor:pointer;float: right;"></a> 
                <strong>Image ${image.id}</strong><br />
                <a href="${image.url}" target="_blank"><img class="img-responsive" src="${image.url}"></a>
                <input type="file" name="products_image_large_${image.id}" style="width: 90px;overflow: hidden;">
            </li>`;
        $('#piList').append(htmlContent);
    });
}

$('#piList').sortable({
  containment: 'parent'
});

var piSize = <?php echo $pi_counter; ?>;

function addNewPiForm() {
  piSize++;

  $('#piList').append('<li id="piId' + piSize + '" class="ui-state-default"><span class="ui-icon ui-icon-arrowthick-2-n-s" style="float: right;"></span><a href="#" onclick="showPiDelConfirm(' + piSize + ');return false;" class="ui-icon ui-icon-trash" style="float: right;"></a><strong><?php echo TEXT_PRODUCTS_LARGE_IMAGE; ?></strong><br /><input type="file" name="products_image_large_new_' + piSize + '" /><br /><br /><?php echo TEXT_PRODUCTS_LARGE_IMAGE_HTML_CONTENT; ?><br /><textarea name="products_image_htmlcontent_new_' + piSize + '" wrap="soft" cols="70" rows="3"></textarea></li>');
}

var piDelConfirmId = 0;

$('#piDelConfirm').dialog({
  autoOpen: false,
  resizable: false,
  draggable: false,
  modal: true,
  buttons: {
    'Delete': function() {
      $('#piId' + piDelConfirmId).effect('blind').remove();
      $(this).dialog('close');
    },
    Cancel: function() {
      $(this).dialog('close');
    }
  }
});

function showPiDelConfirm(piId) {
  piDelConfirmId = piId;

  $('#piDelConfirm').dialog('open');
}
</script>
 </div>  
			
			
			<?php 
		
	$variations_sql = "select * from products_variations pv WHERE pv.products_id = '" . $pInfo->products_id . "' order by pv.sort_order";
	//$variations = "select pa.* from products_attributes_variations pa left join products_description pd on pa.products_id = pd.products_id and pd.language_id = '" . (int)$languages_id . "' and pa.products_id = '" . $pInfo->products_id . "' order by pd.products_name";
	//echo $variations_sql;
			?>
		</td>
        </tr>
		<tr>
	            <td colspan="4"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
	        </tr>
		<tr>
		  <td colspan="4">
		  <div  class="col-xs-10">
		<div class=" panel panel-default">
			<div class="panel-header">
			<?php if (!empty($pInfo->products_id)){ ?> 
			<div class="panel-body">
				<strong>Variations</strong></div></div>
		   
		  <table class="table table-striped table-draggable" id="variationsTable">	
			<thead>
			<tr>
				<td>&nbsp;</td><td>Model</td><td>GTIN</td><td>Image ID</td><td>Price</td><td align="center">Options</td><td align="center">Sort Order</td><td align="center" colspan="2">Actions</td>
				</tr>
			</thead>
			<tbody>	
					
					
			<?php
			$rows = 0;
			$variations_query = tep_db_query($variations_sql);
			$attributesNameString = "";	
			while ($variations_values = tep_db_fetch_array($variations_query)) {
				$attributes = explode('{', substr($variations_values['attributes'], strpos($variations_values['attributes'], '{')+1));
			  	$attributesNameString = "";
			  	for ($i=0, $n=sizeof($attributes); $i<$n; $i++) {			           
			            $pair = explode('}', $attributes[$i]);
				    $attributesNameString .= '<span style="font-weight:bold">' . tep_options_name($pair[0]) . ':</span> ';
				    $attributesNameString .= tep_values_name($pair[1]) . ' ';				     
			           
			        }        
								
				$rowId =  $variations_values['model'] . '_' . $variations_values['products_variations_id'];
				?>
				<tr <?php echo 'data-variationsid="' . $variations_values['products_variations_id'] . '" data-rowNum="' . $rowId . '" class="" id="variationsTableRow_' . $rowId . '"'?>>
				 	<td class="portletTD"><div class="portlet">&nbsp;</div></td>
					<td <?php echo 'id="variationsTable_model_' . $rowId . '" data-model="' . $variations_values['model'] . '"';?> ><?php echo $variations_values['model'] ?></td>
				 	<td <?php echo 'id="variationsTable_gtin_' . $rowId . '" data-gtin="' . $variations_values['gtin'] . '"';?> ><?php echo $variations_values['gtin'] ?></td>
				 	<td <?php echo 'id="variationsTable_image_id_' . $rowId . '"'?> align="center"><?php echo $variations_values['image_id']; ?></td>
					<td <?php echo 'id="variationsTable_Price_' . $rowId . '"'?> align="center"><?php echo $variations_values['price']; ?></td>
					<td <?php echo 'id="variationsTable_attributes_' . $rowId . '" data-attributes="' . $variations_values['attributes'] . '"';?> style="font-size:9px" ><?php echo $attributesNameString; ?></td>
							
					<td style="width:5%" class="" style="width:10px"><input class="indexSO variationsSOinput" type="text" name="" size="1" maxlength="4" value="<?php echo $variations_values['sort_order'];?>"></td>
					<td class="variationsOptionsEdit listsOptionsEdit">e</td>
					<td class="variationsOptionsDelete listsOptionsDelete">x</td>
				</tr>
				<?php 
				$rows++;
			} 	?> 
			</tbody>
			</table>
			<div class="panel-footer"> 
			<div id="variations_options_container" class="">
				<div class="row">
			<?php 
			$options_output = null;
			$input_output = '<div class="col-xs-12 form-inline"> 
			<div id="variations_model_container" class="form-group">
				<label class="control-label">&nbsp;&nbsp;Model:&nbsp;</label><input type="text" name="variations_model" id="variations_model" size="16" placeholder="model" class="form-control">
			</div>';
			$input_output .= '
			<div id="variations_gtin_container" class="form-group">
				<label class="control-label">&nbsp;&nbsp;GTIN&nbsp;</label><input type="text" name="variations_gtin" id="variations_gtin" size="18" placeholder="GTIN" class="form-control">
			</div>';
			$input_output .= '
			<div id="variations_image_id_container" class="form-group">
				<label class="control-label">&nbsp;&nbsp;Image Id:&nbsp;</label><input type="text" name="variations_image_id" id="variations_image_id" size="6" placeholder="Image Id" class="form-control">
			</div>';
			$input_output .= '<div id="variations_price_container" class="form-group">
				<label class="control-label">&nbsp;&nbsp;Price:&nbsp;</label><input type="text" name="variations_price" id="variations_price" size="6" placeholder="Price" class="form-control">
			</div></div><hr ><div class="col-xs-10 form-inline">';
				
		$products_options_name_query = tep_db_query("select distinct popt.products_options_id, popt.products_options_name from products_options popt, products_attributes patrib where patrib.products_id='" . $pInfo->products_id . "' and patrib.options_id = popt.products_options_id order by popt.products_options_name");
		if (tep_db_num_rows($products_options_name_query)) {
			while ($products_options_name = tep_db_fetch_array($products_options_name_query)) {
				$products_options_array = array();

				$fr_input = $fr_required = $fr_feedback = null;
				
				$products_options_query = tep_db_query("select pov.products_options_values_id, pov.products_options_values_name, pa.options_values_price, pa.price_prefix, pa.attribute_default, pa.dependson_options_id, pa.dependson_options_values_id from products_attributes pa, products_options_values pov where pa.products_id = '" . $pInfo->products_id . "' and pa.options_id = '" . (int)$products_options_name['products_options_id'] . "' and pa.options_values_id = pov.products_options_values_id order by pa.products_attributes_sort_order");
				

				if (is_string($_GET['products_id']) && isset($cart->contents[$_GET['products_id']]['attributes'][$products_options_name['products_options_id']])) {
					$selected_attribute = $cart->contents[$_GET['products_id']]['attributes'][$products_options_name['products_options_id']];
				} else {
					$selected_attribute = false;
				}
				$select_output_select = '<div class="form-group attribute_group form-inline ' . $fr_feedback . '">
					<label for="input_' . $products_options_name['products_options_id'] . '" class="control-label"> ' . $products_options_name['products_options_name'] . ': </label>
					<select name="id[' . $products_options_name['products_options_id'] . ']" data-optionid="' . $products_options_name['products_options_id'] . '" data-productid="' . $_GET['products_id'] . '" data-optionsid="' . $products_options_name['products_options_id'] . '" id="variations_select_' . $products_options_name['products_options_id'] . '" class="form-control variationsAttributeSelect" >';	
				$buttons_output = '<div id="attributes_values_id_btns_container" class="attributes_values_id_btns_container btn-group" data-optionid="' . $products_options_name['products_options_id'] . '" >';	
				
				$option_selected = false;
				$select_output_options = "";
				while ($products_options = tep_db_fetch_array($products_options_query)) {
					//$select_output .= '<!-- Does ' .  '/\}' . (int)$products_options_name['products_options_id'] . ':' . $products_options['products_options_values_id'] . '[^0-9]?/'  . ' match ' . $_GET['products_id'] . ':';
					$selected_option = false;
					if (strpos($_GET['products_id'],'{')){
						if (preg_match('/\{' . (int)$products_options_name['products_options_id'] . '}' . $products_options['products_options_values_id'] . '([^0-9]|$)/',$_GET['products_id'])){
							$selected_option = true;
						}
					} else if ($products_options['attribute_default']) {
						$selected_option = true;
					}
					if ($selected_option){
						//$select_output .= ' Yes -->';
						$selected_option = 'selected';
						$selected_button = 'active btn-success';
					} else {
						//$select_output .= ' No -->';
						$selected_option = '';
						$selected_button = '';
					}
					$optionsPrice = $currencies->display_price($products_options['options_values_price'], tep_get_tax_rate($product_info['products_tax_class_id']));
					$select_output_options .= '<option value="'. $products_options['products_options_values_id'] . '" data-productId="' . $_GET['products_id'] . '" data-priceIncrease="'. $products_options['options_values_price'] . '" '. $selected_option . ' data-dependson_optionsid="' . $products_options['dependson_options_id'] . '" data-dependsOn_valuesid="' . $products_options['dependson_options_values_id'] . '">' . $products_options['products_options_values_name'];
					if ($products_options['options_values_price'] != '0') {
						$select_output_options .= ' (' . $products_options['price_prefix'] . $optionsPrice .') ';
					}
					$select_output_options .= '</option>';
				
					//$buttons_output .= '<button type="button" data-optionid="' . $products_options['products_options_id'] . '" data-valueid="'. $products_options['products_options_values_id'] . '" data-productId="' . $_GET['products_id'] . '" data-priceIncrease="'. $products_options['options_values_price'] . '" class="btn btn-default valuesSelectBtns ' . $selected_button . '" data-buttonSet="" data-dependson_optionsid="' . $products_options['dependson_options_id'] . '" data-dependsOn_valuesid="' . $products_options['dependson_options_values_id'] . '">' . $products_options['products_options_values_name'] . '</button>';
				}
				
				if(!$option_selected){
					$select_output = $select_output_select . '<option disabled selected value>None</option>' . $select_output_options;
				}
					$select_output .=   '</select></div>';
				/*	$buttons_output .= "
					</div>";
				$buttons_output .= "";*/
				
				$options_output .= $select_output;
			}
			echo $input_output  . $options_output;
		}
	?>
</div>
<div class="col-xs-2">
	<div id="variations_submit" class="form-group">	
			<button id="variations_insert_btn" class="btn btn-primary pull-right" type="button">Insert</button>   
		</div>
	</div>
</div>

	
	
<?php //EPP: -- Variations Script	   
?>	

	
<script>
	$(document).ready(function(e) {		
		$("#variations_insert_btn").click(function(e) {
			var error = 0;
			var culprit = "";
			var model = $('#variations_model').val();			
			var gtin =  $('#variations_gtin').val();
			var image_id =  $('#variations_image_id').val();
			var price = $('#variations_price').val();			
            		var method = "product_variations_addToProduct";
            		attributeString = "";
            		var attributeSelects = $(".variationsAttributeSelect").each(function(){
	            		el = $(this);
            			attributeString += "{" + el.data("optionid") + "}" + el.find(":selected").val()
            		});
			$("#variationsTable tbody tr").each(function(){
				var row = $(this).data("rownum");
				var elmstring = "#variationsTable_optionsValueName_" + row;
				if ($(elmstring).data("model") == model) {
					 method = "product_variations_addToProduct";
					 return false;
				}
			});
			$.get("api.php?action="
			+ method + "&"
			+ "products_id=<?php echo $pInfo->products_id ?>&"			
			+ "model=" + model + "&"
			+ "gtin=" + gtin + "&"
			+ "image_id=" + image_id + "&"
			+ "price=" + price + "&"
			+ "attributes=" + attributeString, function(data, status){
				if (status == "success") {
					if (data["Complete"] > 0) {										
						variationsAddRow(data);								
						e.stopPropagation();					
					}
				}
			});
		});
		
		function variationsAddRow(data) {	
			variation_id = data["variations"][0]["products_variations_id"];
			attributes_text	= data["variations"][0]["attributes_text"];
			attributes = data["variations"][0]["attributes"];
			model = data["variations"][0]["model"];
			gtin = data["variations"][0]["gtin"];
			image_id = data["variations"][0]["image_id"];
			price = data["variations"][0]["price"];
			var rowNum = model + '_' + variation_id;
			
			var elements = $('<tr style="display:none" data-variationsId="' + variation_id + '" data-rownum="' + rowNum + '" class="" id="variationsTableRow_' + rowNum + '">'
				+ '<td class="portletTD"><div class="portlet">&nbsp;</div></td>'
				+ '<td id="variationsTable_model_' + rowNum + '" data-model="' + model + '">' + model + '</td>'
				+ '<td id="variationsTable_gtin_' + rowNum + '" data-gtin="' + gtin + '">' + gtin + '</td>'
				+ '<td id="image_id_' + rowNum + '">' + image_id + '</td>'
				+ '<td id="variationsTable_price_' + rowNum + '" align="center">' + price + '</td>'
				+ '<td id="variationsTable_attributes' + rowNum + '" data-attributes="' + attributes + '" valign="center">' + attributes_text + '</td>'
				+ '<td style="width:5%" class=""><input class="indexSO variationsSOinput" type="text" name="" size="1" maxlength="4" value=""></td>'
				+ '<td class="variationsOptionsEdit listsOptionsEdit">e</td>'
				+ '<td class="variationsOptionsDelete listsOptionsDelete">x</td>'
			+ '</tr>');
			
			var row = $('#variationsTableRow_' + rowNum);					
			if (row.length) {
				row.replaceWith(elements);
				elements.fadeIn("slow");
			} else {
				elements.appendTo("#variationsTable").fadeIn("slow");
			}			
		}
		
		$("#variationsTable").on("click", ".variationsOptionsDelete", function(e) {
			var that = $(this);
			var method = "product_variations_removeVariation";
			$.get("api.php?action=" + method + "&products_id=<?php echo $pInfo->products_id ?>&products_variations_id=" + that.parent().data("variationsid"), function(data, status){
				if (status == "success") {
					e.stopPropagation();
					that.parent().fadeOut("slow",function(){
						this.remove();
					});
				}
			});	
		});	
		
		
		$("#variationsTable").on("click",".variationsOptionsEdit",function(e) {
			var row = $(this).closest('tr').data('rownum');
			
			
			var model = $('#variationsTable_model_' + row).data('model');
			var gtin = $('#variationsTable_gtin_' + row).data('gtin');
			
			var value_price = $('#variationsTable_Price_' + row).text().trim();
			var variations_image_id = $('#variationsTable_image_id_' + row).text().trim();			
			
			$("#variations_model").val(model);
			$("#variations_gtin").val(gtin);
			$("#variations_price").val(value_price);
			$("#variations_image_id").val(variations_image_id);
            var attributes = $('#variationsTable_attributes_' + row).data("attributes");
			attributes = attributes.substring(1).split('{');
            attributesNameString = "";
			
			for (let i = 0, n = attributes.length; i < n; i++) {
			    let pair = attributes[i].split('}');
			    $("#variations_select_" + pair[0]).val(pair[1]);
			}				
			
		});
	});
	
	
	
</script>

<?php } else { ?>
			<div class="panel-body">
				<strong>Save first to generate product id</strong>
				</div>
				<?php } ?> 
		   </div>
			</div>		
			
			
			
			
			
			
			
			
			
			
			
						
			
			<tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          
<tr>
            <td colspan="4"><?php 
		
			$attribute_combinations_query = "select pa.* from products_attribute_combinations pa, products_description pd WHERE pa.products_id = pd.products_id and pd.language_id = '" . (int)$languages_id . "' and pa.products_id = '" . $pInfo->products_id . "' order by pd.products_name";
			//$attribute_combinations = "select pa.* from products_attribute_combinations pa left join products_description pd on pa.products_id = pd.products_id and pd.language_id = '" . (int)$languages_id . "' and pa.products_id = '" . $pInfo->products_id . "' order by pd.products_name";
			echo $attribute_combinations;
			?>
			</td>
          </tr>
		        <tr>
            <td colspan="4"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
		  
          
<?php



    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) { ?>
         <tr>
            <td class="main" valign="top"><?php
        echo tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']);
        
        if ($i == 0)
            echo TEXT_PRODUCTS_DESCRIPTION; ?></td></tr><tr>
            <td colspan=4><table border="0" cellspacing="0" cellpadding="0" style="width:1000px">
              <tr>
                <td class="main" valign="top">
      </td>
                <td class="main">
        
  <?php
        echo tep_draw_textarea_field_ckeditor('products_description[' . $languages[$i]['id'] . ']', 'soft', '117', '40', (empty($pInfo->products_id) ? '' : stripslashes(tep_get_products_description($pInfo->products_id, $languages[$i]['id'])))); ?></td>
              </tr>
            </table></td>
          </tr>
<?php
    } ?>
         <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <tr>
            <td class="main"><?php echo TEXT_PRODUCTS_QUANTITY; ?></td>
            <td class="main"><?php echo tep_draw_input_field('products_quantity', $pInfo->products_quantity); ?></td>
          </tr>
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>

          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
        
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <?php
    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) { ?>
         <tr bgcolor="#eeeeee">
            <td class="main" valign="top"><?php
        if ($i == 0)
            echo TEXT_PRODUCTS_SEO_DESCRIPTION; ?></td>
            <td colspan=3><table border="0" cellspacing="0" cellpadding="0">
              <tr>
                <td class="main" valign="top"><?php
					echo tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']); ?>&nbsp;</td>
                <td class="main"><?php
					echo tep_draw_textarea_field('products_seo_description[' . $languages[$i]['id'] . ']', 'soft', '70', '15', (empty($pInfo->products_id) ? '' : tep_get_products_seo_description($pInfo->products_id, $languages[$i]['id']))); ?></td>
              </tr>
            </table></td>
          </tr><tr>
            <td class="main"><?php
				echo 'More Information:'; ?></td>
            <td class="main" colspan=3><?php
				echo tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']) . '&nbsp;' . tep_draw_input_field('products_url[' . $languages[$i]['id'] . ']', tep_get_products_url($pInfo->products_id, $languages[$i]['id']), 'size="100" '); ?></td>
          </tr>
<?php
    } ?>
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>

          <?php
    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) { ?>
         <tr bgcolor="#eeeeee">
            <td class="main" valign="top"><?php
        if ($i == 0)
				echo TEXT_PRODUCTS_SEO_KEYWORDS; ?></td>
            <td colspan=3><table border="0" cellspacing="0" cellpadding="0">
              <tr>
                <td class="main" valign="top"><?php
					echo tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']); ?>&nbsp;</td>
                <td class="main" valign="top"><?php
					echo tep_draw_input_field('products_seo_keywords[' . $languages[$i]['id'] . ']', tep_get_products_seo_keywords($pInfo->products_id, $languages[$i]['id']), 'placeholder="' . PLACEHOLDER_COMMA_SEPARATION . '" style="width: 300px;"'); ?></td>
              </tr>
            </table></td>
          </tr>
	<?php
	}
	?>
             <tr>
            <td class="main"><?php echo TEXT_PRODUCTS_WEIGHT; ?></td>
            <td class="main"><?php echo tep_draw_input_field('products_weight', $pInfo->products_weight); ?></td>
              </tr>
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <tr>
            <td class="main"><?php echo TEXT_EDIT_SORT_ORDER; ?></td>
            <td class="main"><?php echo tep_draw_input_field('products_sort_order', $pInfo->products_sort_order, 'size="2"'); ?></td>
          </tr>
		  
		       <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
		  	  
             <tr>
       <?php //EPP: Related Script	 
       ?>   
            <td class="main"><?php echo 'Related Products:'; ?></td><td>
	
		  
		<style>
			#relatedProductsModuleContainer {
				border: dotted 2px lightgrey;
				background-color: darkgrey;
			}
			.listsOptionsDelete {
				color:red;
				text-align:center;
				font-weight:bold;
				font-family:OCR A Std, monospace;
				font-size:1.5em;
				cursor: pointer;
			}
			.listsOptionsEdit{
				color:Green;
				text-align:center;
				font-weight:bold;
				font-family:OCR A Std, monospace;
				font-size:1.5em;
				cursor: pointer;
			}
			.relatedProductsRow {
				display:none;
			}
			.listsSOtd {			
				text-align: center;
			}
			#attributes_options_id_cont {
				visibility: hidden;
			}
			.portlet {
				border: dotted 2px #000;
				padding: 0.3em;
				display:inline-block;	
				height:100%;
				width:10px;
				margin:2px;
				position:relative;
			}
			.portletTD {
				padding: 0;
				width:0px;	
				position:relative;
			}
			.material-switch > input[type="checkbox"] {
				display: none;   
			}

			.material-switch > label {
				cursor: pointer;
				height: 0px;
				position: relative; 
				width: 40px;  
			}

			.material-switch > label::before {
				background: rgb(0, 0, 0);
				box-shadow: inset 0px 0px 10px rgba(0, 0, 0, 0.5);
				border-radius: 8px;
				content: '';
				height: 16px;
				margin-top: -8px;
				position:absolute;
				opacity: 0.3;
				transition: all 0.4s ease-in-out;
				width: 40px;
			}
			.material-switch > label::after {
				background: rgb(255, 255, 255);
				border-radius: 16px;
				box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.3);
				content: '';
				height: 24px;
				left: -4px;
				margin-top: -8px;
				position: absolute;
				top: -4px;
				transition: all 0.3s ease-in-out;
				width: 24px;
			}
			.material-switch > input[type="checkbox"]:checked + label::before {
				background: inherit;
				opacity: 0.5;
			}
			.material-switch > input[type="checkbox"]:checked + label::after {
				background: inherit;
				left: 20px;
			}
		</style>
		
		<script>
		
	$(document).ready(function(){
		var apiAction = null;
		var fixHelperModified = function(e, tr) {
			var $originals = tr.children();
			var $helper = tr.clone();
			$helper.children().each(function(index) {
				$(this).width($originals.eq(index).width())
			});
			return $helper;
		},
		updateIndex = function(e, ui) {
			var updatedData = {};
			// Make an HTTP GET request with the JSON data
			
			if ($(this).find('.attributesSOinput').length > 0){
				apiAction = 'product_attributes_updateSortOrder';
				$('.indexSO.attributesSOinput', ui.item.parent()).each(function (i) {	
					$(this).val(i).trigger("change");			
					// Get the value of the input field
					var updatedValue = $(this).val();
					// Get the data-attributesid of the parent row
					var attributesId = $(this).closest('tr').data('attributesid');
					// Store the updated value in the object using attributesId as the key
					updatedData[attributesId] = updatedValue;	
				});
			} else if ($(this).find('.variationsSOinput').length > 0){
				apiAction = 'product_variations_updateSortOrder';
				$('.indexSO.variationsSOinput', ui.item.parent()).each(function (i) {
					$(this).val(i).trigger("change");			
					// Get the value of the input field
					var updatedValue = $(this).val();
					// Get the data-attributesid of the parent row
					var attributesId = $(this).closest('tr').data('variationsid');
					// Store the updated value in the object using attributesId as the key
					updatedData[attributesId] = updatedValue;	
				});
			} else if ($(this).find('.relatedProductSOinput').length > 0){
				apiAction = 'related_products_updateSortOrder';								
				$('.indexSO.relatedProductSOinput', ui.item.parent()).each(function (i) {	
					$(this).val(i).trigger("change");			
					// Get the value of the input field
					var updatedValue = $(this).val();
					// Get the data-attributesid of the parent row
					var attributesId = $(this).closest('tr').data('relatedid');
					// Store the updated value in the object using attributesId as the key
					updatedData[attributesId] = updatedValue;	
				});


			}
			
			
			var jsonData = JSON.stringify(updatedData);
			
			if (apiAction != null){
				$.get("api.php?action=" + apiAction, { jsonData: jsonData }, function(response) {
					console.log(response);
				});
			} else {
				console.log('Failed to find classname');
			}
			
		};

		$(".table-draggable tbody").sortable({
			helper: fixHelperModified,
			stop: updateIndex
		});
		
	$(".table-draggable tbody").sortable({
		distance: 5,
		delay: 100,
		opacity: 0.6,
		cursor: 'move',
		connectWith: '.connectedSortable',
		cancel: ".relatedVaritionRow",
		handle: ".portlet",
		update: function() {}
	});

	function relatedProductsBuildEmptyTable(relatedTableID){		
		return	$("#relatedProducts").append(''
			+ '<table id="relatedProductsTable-' + relatedTableID + '" width="100%" cellspacing="3" cellpadding="3" class="table-draggable table table-striped table-hover table-condensed small" data-tableId="' + relatedTableID + '">'
			+ '		<thead class="thead-dark">'
            + '		    <tr>'
            + '		        <th>#</th>'
            + '		        <th>Product</th>'
            + '		        <th class="text-right">Action</th>'
			+ '		    </tr>'
			+ '		 </thead>'
			+ '   <tbody id="relatedProductsTable-' + relatedTableID + '" class="connectedSortable">'
			+ '	 </tbody>'
			+ '</table>'
		).fadeIn("slow");
	}
	
function relatedProductsBuildAddProductRow(relatedId, relatedTableId, productId, productModel, productName, sortOrder, variations) {
    var $table = $("#relatedProductsTable-" + relatedTableId);
    
    if (variations.length > 0) {
        var $mainRow = $('<tr class="table-active small" id="relatedProductsRow' + productId + '" data-id="' + productId + '" data-relatedId="' + relatedId + '">'
            + '<td class="portletTD"><div class="portlet">&nbsp;</div></td>'
            + '<td colspan="3" style="padding:0;">'
            + '<table id="relatedProductsVariationsTable-' + relatedTableId + '-' + productId + '" cellspacing="0" cellpadding="3" class="table table-hover table-condensed table-sm" width="100%" data-relatedid="' + relatedTableId + '">'
            + '<tbody>'
            + '<tr class="table-active" data-id="' + productId + '" data-relatedId="' + relatedId + '">'
            + '<td style="width:5%" class="relatedProductsSOtd"><input class="indexSO relatedProductSOinput" type="text" size="1" maxlength="4" value="' + sortOrder + '"></td>'
            + '<td style="width:15%">' + productModel + '</td>'
            + '<td style="width:80%" colspan="2">' + productName + '</td>'
            + '</tr>'
            + '</tbody>'
            + '<tbody>').fadeIn("slow");

        variations.forEach(function(variation) {
            $mainRow.find('tbody:last').append(
                '<tr class="table-active relatedVaritionRow small" id="variationProductsRow' + variation.products_variations_id + '" data-id="' + variation.products_variations_id + '" data-variationId="' + variation.products_variations_id + '">'
                + '<td style="width:5%" class="variationtd"></td>'
                + '<td style="width:15%">' + variation.model + '</td>'
                + '<td style="width:80%">' + variation.product_name_suffix.replace(', ', ' ') + '</td>'
                + '<td class="variationProductsDelete listsOptionsToggle">'
                + '<div class="material-switch pull-right">'
                + '<input class="relatedVaritionToggles" id="relatedVaritionToggle-' + productId + '-' + variation.products_variations_id + '" type="checkbox" checked data-products_id="' + productId + '" data-related_id="' + relatedId + '" data-variation_id="' + variation.products_variations_id + '"/>'
                + '<label class="relatedVaritionToggleLabels label-success" for="relatedVaritionToggle-' + productId + '-' + variation.products_variations_id + '"></label>'
                + '</div></td>'
                + '</tr>'
            ).fadeIn("slow");
        });

        $mainRow.append('</tbody></table></td><td class="relatedProductsDelete listsOptionsDelete">x</td></tr>');
        $table.append($mainRow).fadeIn("slow");
    } else {
        $table.append(
            '<tr class="table-active small" id="relatedProductsRow' + productId + '" data-id="' + productId + '" data-relatedId="' + relatedId + '">'
            + '<td class="portletTD"><div class="portlet">&nbsp;</div></td>'
            + '<td style="width:5%" class="relatedProductsSOtd"><input class="indexSO relatedProductSOinput" type="text" size="1" maxlength="4" value="' + sortOrder + '"></td>'
            + '<td style="width:15%">' + productModel + '</td>'
            + '<td style="width:80%">' + productName + '</td>'
            + '<td class="relatedProductsDelete listsOptionsDelete">x</td>'
            + '</tr>'
        );
    }
}

	
	/*
	function relatedProductsBuildAddProductRow(relatedId,relatedTableID,productIdNum,productModel,productName,relatedSortOrder) {
		return $("#relatedProductsTable-" + relatedTableID).append(
			$('<tr class="table-active" id="relatedProductsRow' + productIdNum + '" data-id="' + productIdNum + '" data-relatedId="' + relatedId +'">'
				+ '		<td style="width:5%" class="relatedProductsSOtd">'
				+ '			<input class="indexSO relatedProductSOinput" type="text" name="" size="1" maxlength="4" value="' + relatedSortOrder + '"></td>'
				+ '		<td style="width:15%">' + productModel + ' </td>'
				+ '		<td style="width:80%">' + productName + ' </td>'
				+ '		<td class="relatedProductsDelete listsOptionsDelete">x</td>'
				+ '</tr>'
			).fadeIn("slow")
		);
	}*/
	$(document).on("click","#relatedProductsAddProduct",function(e) {
		relatedTableID = $('#related_products_table_select').val();
		if (relatedTableID == "999") {
			lastTableID = $("#relatedProducts").children().last().attr('data-tableid');							
			relatedTableID = parseInt(lastTableID) + 1;						
		}
		if ($("#relatedProductsTable-" + relatedTableID + " tbody").children().length > 0) {
				relatedSortOrder = $("#relatedProductsTable-" + relatedTableID + " tbody").children().length;
			} else {					
				relatedSortOrder = 0;
			}
		$.get("api.php?action=related_products_addRelatedProduct&products_id=<?php echo $pInfo->products_id ?>&related_products_id=" + $('#related_products_select').val() + "&related_table_id=" + relatedTableID + "&relatedSortOrder=" + relatedSortOrder, function(data, status){
			if (status == "success") {
				if (data["Complete"] > 1) {
					if (data["products"][0]) {
						productIdNum = data["products"][0]["id"];
						theElementID = "#relatedProductsRow" + productIdNum;
						productName = data["products"][0]["title"];
						productModel = data["products"][0]["model"];
						relatedId = data["products"][0]["relatedId"];
						variations = data["products"][0]["variations"];
						relatedTableID = $('#related_products_table_select').val();
						if (relatedTableID == "999") {
							if ($("#relatedProducts").children().length > 0) {
							lastTableID = $("#relatedProducts").children().last().attr('data-tableid');						
							relatedTableID = parseInt(lastTableID) + 1;
							} else {
								relatedTableID = 0;					
							}
							relatedProductsBuildEmptyTable(relatedTableID);						
							$("#relatedProductsTable-"  + relatedTableID + " tbody").sortable();
							$("#related_products_table_select").append($('<option>', {
								value: relatedTableID,
								text: relatedTableID
							}));
							$("#related_products_table_select").val(relatedTableID);	
						}
						if ($("#relatedProductsTable-" + relatedTableID + " tbody").children().length > 0) {
							relatedSortOrder = $("#relatedProductsTable-" + relatedTableID + " tbody").children().length;
						} else {					
							relatedSortOrder = 0;
						}
						relatedProductsBuildAddProductRow(relatedId,relatedTableID,productIdNum,productModel,productName,relatedSortOrder,variations);
						temp = $("#relatedProductsTable-"  + relatedTableID + " tbody");
						$("#relatedProductsTable-"  + relatedTableID + " tbody").trigger("stop");
						$("#relatedProductsTable-"  + relatedTableID + " tbody").sortable( "refresh" );		
					}
					e.stopPropagation();					
				} else {
					var searchTerm = data["products"][0]["model"].toLowerCase();
					$('.relatedProductsRow').each(function() {
						  var product = $(this).text().toLowerCase();
						  if (product.indexOf(searchTerm) >= 0) {
							$(this).addClass('danger');
							$(this).popover({
							  title: 'Product found',						  
							  content: '<img src="images/friends-you-idiot.gif">',
							  html: true,
							  boundary: 'viewport',
							  boundaryPadding: 10,
							  flip: false,
							  fallbackPlacement: 'none',
							  placement: 'top',
							  trigger: 'focus'
							}).on('click', function() {
								$(this).removeClass('danger');
								$(this).popover('destroy');						  
							}).on('shown.bs.popover', function () {
								var popover = $(this).data('bs.popover').tip();
								var popoverTop = popover.offset().top;
								var scrollTop = $(window).scrollTop();
								var topOffset = popoverTop - scrollTop;
	
								if (topOffset < 0) {
									$('html, body').animate({scrollTop: scrollTop + topOffset}, 500);
								}
							});
							
							$(this).popover('show');					  
						  }
					});
					
				}
			}
		});		
	});
	
	$(document).on("click","#copyRelatedListFrom",function(e) {
		$.get("api.php?action=related_products_copyRelatedListFrom&products_id=<?php echo $pInfo->products_id ?>&source_id=" + $('#related_products_select').val() + "", function(data, status){
			if (status == "success") {
				if (data["Complete"] > 1) {
					lastTable = -1;
					$("#relatedProducts").empty();
					for (x in data["products"]) {
						productIdNum = data["products"][x]["id"];
						productName = data["products"][x]["title"];
						productModel = data["products"][x]["model"];
						productPrice = data["products"][x]["price"];
						relatedSortOrder = data["products"][x]["sortOrder"];
						relatedId = data["products"][x]["relatedId"];
						relatedTableID = data["products"][x]["relatedTableID"];
						if (lastTable != relatedTableID) {							
							relatedProductsBuildEmptyTable(relatedTableID);
							$("#related_products_table_select").children().last().before('<option value="' + relatedTableID + '">' + relatedTableID + '</option>');
							lastTable = relatedTableID;
						}
						relatedProductsBuildAddProductRow(relatedId,relatedTableID,productIdNum,productModel,productName,relatedSortOrder);
						$("#relatedProductsTable-"  + relatedTableID + "tbody").trigger("stop");
						$("#relatedProductsTable-"  + relatedTableID + "tbody").sortable( "refresh" );					
						e.stopPropagation();
					}
					$("#table-draggable2 tbody").trigger("stop");
				}
			}
		});			
	});
	
	$(document).on("click","#relatedProductsSearchButton",function(e) {
		$.get("api.php?action=related_products_searchRelatedProduct&search_term=" + $('#relatedProductSearchinput').val() + "", function(data, status){
			if (status == "success") {				
				$("#related_products_select").empty();				
				for (x in data["products"]) {
					theID = data["products"][x]["id"];					
					productName = data["products"][x]["title"];
					$("#related_products_select").append($("<option>").attr('value',theID).text(productName));
				}				
			}
		});			
	});	

	$("#relatedProducts").on("click",".relatedProductsDelete",function(e) {
		var that = this;
		$.get("api.php?action=related_products_removeRelatedProduct&products_id=<?php echo $pInfo->products_id ?>&products_related_id=" + $(that).parent().attr("data-relatedId") + "", function(data, status){
			if (status == "success") {
				e.stopPropagation();
				$(that).parent().fadeOut("slow",function(){
					this.remove();
				});
			}
		});	
	});//related_products_variations_toggle($products_id,$related_id,$variation_id,$status)
	$(".relatedVaritionToggleLabels").on("click",function(e) {	
			let products_id = $(this).data('products_id');
			let related_id = $(this).data('related_id');
			let variation_id = $(this).data('variation_id');
			let toggle_switch = $('#relatedVaritionToggle-' + products_id + '-' + variation_id).prop('checked');
			let toggle_status = toggle_switch == true ? 0 : 1;
			$.get("api.php?action=related_products_variations_toggle&products_id=" + products_id + "&related_id=" + related_id + "&variation_id=" + variation_id + "&toggle_status=" + toggle_status, function(data, status){
			if (status == "success") {
				e.stopPropagation();
				$(that).parent().parent().fadeTo('slow','.5');
			}
		});
	});
	
	
	
	//$("#relatedProducts").on("change",".relatedProductSOinput",function(e) {
	//	var that = this;			
	//	$.get("api.php?action=related_products_updateSortOrder&products_id=<?php echo $pInfo->products_id ?>&products_related_id=" + $(that).parent().parent().attr("data-relatedId") + "&related_product_sort_order=" + $(that).val(), function(data, status){
	//		if (status == "success") {}
	//	});	
	//});	
	<?php if (isset($_GET['pID'])) { ?>
		$("#applyButton").click(function(e) {
			
			for ( instance in CKEDITOR.instances )
				CKEDITOR.instances[instance].updateElement();

			var form = $('#new_product_form')[0];
			var data = new FormData(form);
			$.ajax({
			  type: "POST",
			  enctype: 'multipart/form-data',
			  url: "product_edit.php?cPath=<?php echo $cPath . "&pID=" . $_GET['pID'] ?>&action=update_product",
			  data: data,
			  contentType: false,
			  processData: false

			});
		});	
	<?php } else { ?>
		$("#applyButton").disable();
	<?php } ?>
});
</script>
<?php //EPP: -- Related table	 
?>   
	<tr>
         <td id="relatedProducts" colspan=5 class="main"> 
    <?php  	
		$lastTable = -1;
		
		$get_related_products_query = tep_db_query("SELECT * FROM products as p, products_related as r, products_description as d WHERE r.products_related_related_products_id=p.products_id AND d.products_id=p.products_id AND r.products_related_products_id=" . (int) $_GET['pID'] . " ORDER BY r.products_related_table_id, r.products_related_sort_order");
        
	
		  
		while ($products_related = tep_db_fetch_array($get_related_products_query)) {
			//print_rr($products_related['products_name']);			
			$related_variations_q = tep_db_query("select * from products_variations_to_products_related WHERE products_id = '" . $products_related['products_id'] . "' AND products_related_id='" . $products_related['products_related_id']  . "'");
			
			while ($related_variations = tep_db_fetch_array($related_variations_q)) {
				$related_variations_status[$related_variations['products_related_id']][$related_variations['products_variations_id']] = $related_variations['status'];
			}
			print_rr($related_variations_status,"select * from products_variations_to_products_related WHERE products_id = '" . $products_related['products_id'] . "' AND products_related_id='" . $products_related['products_related_id']  . "'");
			if ($lastTable != $products_related['products_related_table_id']) {
				if ($products_related['products_related_table_id'] > 0) {?>					
					</tbody></table>				
		<?php	} 
					++$lastTable;?>
					<span><strong>Table <?php echo $lastTable ?></strong> </span> <span class="pull-right"> Code: <input class="relatedProductCodeInput" type="text" name="" size="30" maxlength="30" value="<?php echo "relatedProductsModuleContainer-" . $lastTable;?>"></span>
				<table height="1px" id="relatedProductsTable-<?php echo $products_related['products_related_table_id'] ?>" cellspacing="3" cellpadding="3" class="table-draggable table table-striped table-hover table-condensed" width="100%" data-tableid="<?php echo $products_related['products_related_table_id'] ?>">
				  <thead class="thead-dark">
					<tr>
						<th></th>
					  <th>#</th>
					  <th>Product</th>              
					  <th class="text-right">Action</th>
								  </tr>
				  </thead>
				  <tbody id="relatedProductsTable-<?php echo $products_related['products_related_table_id'] ?>" class="connectedSortable">
<?php 		}	?>
		
			<?php   
			
			$variations = new tcs_product_attributes($products_related['products_id']); ?>							
	<?php	if ($variations->has_variations){?>			
<?php				$related_variations_q = tep_db_query("select * from products_variations_to_products_related WHERE products_id = '" . $pInfo->products_id . "' AND products_related_id='" . $products_related['products_related_id']  . "'");
					$variationList = $variations->get_variations();	?>
					<tr class="table-active small" id="relatedProductsRow<?php echo $products_related['products_id'];?>" data-id="<?php echo $products_related['products_id'] ;?>" data-relatedId="<?php echo $products_related['products_related_id'] ;?>">
						<td class="portletTD"><div class="portlet">&nbsp;</div></td>
						<td colspan="3" style="padding:0;">							
						<table id="relatedProductsVariationsTable-<?php echo $products_related['products_related_table_id'] . '-' . $products_related['products_id'] ?>" cellspacing="0" cellpadding="3" class="table table-hover table-condensed table-sm" width="100%" data-relatedid="<?php echo $products_related['products_related_table_id'] ?>">
							 <tbody>		  
								<tr class="table-active " id="relatedProductsRow<?php echo $products_related['products_id'];?>" data-id="<?php echo $products_related['products_id'] ;?>" data-relatedId="<?php echo $products_related['products_related_id'] ;?>">
									<td style="width:5%" class="relatedProductsSOtd  " style="width:10px"><input class="indexSO relatedProductSOinput" type="text" name="" size="1" maxlength="4" value="<?php echo $products_related['products_variation_sort_order'];?>"></td>
									<td style="width:15%" class=""> <?php echo $products_related['products_model'];?> </td>
									<td style="width:80%" class=""colspan=2> <?php echo $products_related['products_name'];?> </td>
									
								</tr>
						</tbody>
						 <tbody>
		  
						<?php
						//print_rr($variationList,'arrrrfffff');
						foreach($variationList as $i=>$variation){
									
					?>								
							<tr class="table-active relatedVaritionRow small" id="variationProductsRow<?php echo $products_variation['products_id'];?>" data-id="<?php echo $products_variation['products_id'] ;?>" data-variationId="<?php echo $products_variation['products_variation_id'] ;?>">
								
								<td style="width:5%" class="variationtd" style="width:10px"></td>
								<td style="width:15%"> <?php echo $variation['model'];?> </td>
								<td style="width:80%"> <?php echo str_replace(', ',' ',$variation['product_name_suffix']);?> </td>
								<td class="variationProductsDelete listsOptionsToggle"> 
								<div class="material-switch pull-right">
								<?php
								$check_status = @$related_variations_status[$products_related['products_related_id']][$variation['products_variations_id']];
								print_rr($check_status,'check_status');
								if (!isset($check_status) || $check_status == 1){
									$check_status_out = " checked ";
								} else {
									$check_status_out = "";
								}
								?>								
									<input class="relatedVaritionToggles" id="relatedVaritionToggle-<?php echo $products_related['products_id'] . '-' . $variation['products_variations_id']; ?>" name="relatedVaritionToggle-<?php echo $products_related['products_id'] . '-' . $variation['products_variations_id'] . '"' . $check_status_out . ' data-products_id="' . $products_related['products_id'] . '" data-related_id="' . $products_related['products_related_id'] . '" data-variation_id="' . $variation['products_variations_id'] . '"'?> type="checkbox"/>
									<label class="relatedVaritionToggleLabels label-success" for="relatedVaritionToggle-<?php echo $products_related['products_id'] . '-' . $variation['products_variations_id']; ?>" <?php echo ' data-products_id="' . $products_related['products_id'] . '" data-related_id="' . $products_related['products_related_id'] . '" data-variation_id="' . $variation['products_variations_id'] . '"'?> > </label>
                        </div></td>
								</tr>	
				<?php		} ?>
								</table>
							</td>
							<td class="relatedProductsDelete listsOptionsDelete">x</td>
						</tr>
				<?php
				} else { ?>			
						<tr class="table-active small" id="relatedProductsRow<?php echo $products_related['products_id'];?>" data-id="<?php echo $products_related['products_id'] ;?>" data-relatedId="<?php echo $products_related['products_related_id'] ;?>">
							<td class="portletTD"><div class="portlet">&nbsp;</div></td>
							<td style="width:5%"class="relatedProductsSOtd" style="width:10px"><input class="indexSO relatedProductSOinput" type="text" name="" size="1" maxlength="4" value="<?php echo $products_related['products_related_sort_order'];?>"></td>
							<td style="width:15%"> <?php echo $products_related['products_model'];?> </td>
							<td style="width:80%"> <?php echo $products_related['products_name'];?> </td>
							<td class="relatedProductsDelete listsOptionsDelete">x</td>
						</tr>
					<?php
				}   ?>
				</td>
			</tr>      
			<?php
			$position++;
			}
?>    
 </tbody></table>
		
            </td></tr> 
		<tr>
			<td id="relatedProductsToolbar" colspan="5" class="main">
				<table cellspacing="3" cellpadding="3" class="table ">          
					<tbody>  
						<tr> 
							<td align="left" class="text-left">   
								&nbsp;<input  id="relatedProductSearchinput" type="text" name="" size="20" maxlength="100" value="">
								&nbsp;<button id="relatedProductsSearchButton" class="btn btn-primary" type="button">Search</button>&nbsp;
								&nbsp;<select id="related_products_select" style="width: 400px;"></select>&nbsp;
								&nbsp;Table:&nbsp;<select id="related_products_table_select" style="">
								<?php 
								for ($i=0;$i <= $lastTable;$i++) {
									echo '<option value="' .  $i . '">' . $i . '</option>';										
								}
								?>
								<option value="999">Add New...</option></select>&nbsp;
								&nbsp;<button id="relatedProductsAddProduct" class="btn btn-primary" type="button">Insert</button>&nbsp;
								&nbsp;<button id="copyRelatedListFrom" class="btn btn-primary" type="button">Get Product List</button>&nbsp;
							<?php // &nbsp;<button id="linkRelatedListFrom" class="btn btn-primary" type="button">Link</button>&nbsp; ?>
							<?php // 	&nbsp;<button id="relatedAddTable" class="btn btn-primary" type="button">Add Table</button>&nbsp;?>
							</td>
						</tr>
					</tbody>
				 </table>
			 </td>
		 </tr>
		 <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
            <tr>
            <?php echo TEXT_PRODUCTS_STATUS; ?></td>
            <td class="main"><?php echo tep_draw_radio_field('products_status', '1', $in_status) . '&nbsp;' . TEXT_PRODUCT_AVAILABLE . '&nbsp;' . tep_draw_radio_field('products_status', '0', $out_status) . '&nbsp;' . TEXT_PRODUCT_NOT_AVAILABLE; ?></td>
				<td class="main">
            
            
            
            <?php echo 'Google Status:'; ?></td>
            <td class="main"><?php echo tep_draw_radio_field('products_google_status', '1', $in_google_status) . '&nbsp;' . 'List' . '&nbsp;' . tep_draw_radio_field('products_google_status', '0', $out_google_status) . '&nbsp;' . 'Do not list'; ?></td>
      </tr>
      <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <tr>
            <td class="main"><?php echo TEXT_PRODUCTS_DATE_AVAILABLE; ?></td>
            <td class="main"><?php echo tep_draw_input_field('products_date_available', $pInfo->products_date_available, 'id="products_date_available"') . ' <small>(YYYY-MM-DD)</small>'; ?></td>
          </tr>
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
      <tr>
        <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      </tr>
      <tr>
        <td class="smallText" align="right"><?php echo tep_draw_hidden_field('products_date_added', (@tep_not_null($pInfo->products_date_added) ? $pInfo->products_date_added : date('Y-m-d'))) . tep_draw_button(IMAGE_SAVE, 'disk', null, 'primary') . tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('categories.php', 'cPath=' . $cPath . (isset($_GET['pID']) ? '&pID=' . $_GET['pID'] : ''))); ?></td>
      </tr>
    </table></td></tr>
    </table>

<script type="text/javascript">
$('#products_date_available').datepicker({
  dateFormat: 'yy-mm-dd'
});
</script>
<style>
.navbar-form {
    display: flex;
    align-items: center;
    flex-grow: 1;
}

.navbar-form .form-control {
    width: auto; /* Allows the input to expand */
    flex-grow: 1; /* Makes the input fill available space */
}

.container-fluid {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
</style>
  
<nav class="navbar navbar-default navbar-fixed-bottom form-inline">
    <div class="container-fluid">
        <div class="navbar-header">
		  <?php 
		  echo tep_draw_button(IMAGE_SAVE, 'disk', null, 'primary');
		  echo tep_draw_hidden_field('products_date_added', (@tep_not_null($pInfo->products_date_added) ? $pInfo->products_date_added : date('Y-m-d')));?>
		  <button type="button" class="btn btn-default" id="applyButton">Apply</button>
		 </div>
        <div class="navbar-form navbar-left flex-fill"><label><?php echo TEXT_PRODUCTS_NAME; ?></label>
            <?php
        echo tep_draw_input_field('products_name[' . $languages[$i]['id'] . ']', (empty($pInfo->products_id) ? '' : tep_get_products_name($pInfo->products_id, $languages[$i]['id'])), 'class="form-control" '); ?>
		</div>
        <div class="btn-group navbar-right">
		  <?php echo '<a href="'. HTTPS_SERVER . '\\-p-' .  $pInfo->products_id . '.html" class="btn btn-default" id="publicButton"  target="_blank">Open in website</a>';?>
		  <?php //echo tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('categories.php', 'cPath=' . $cPath . (isset($_GET['pID']) ? '&pID=' . $_GET['pID'] : ''))); ?>
		</div>
    </div>
</nav>
</form>
<?php
}


require('includes/template_bottom.php');
require('includes/application_bottom.php'); ?>