<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2014 osCommerce

  Released under the GNU General Public License
*/

  require('includes/application_top.php');
  
  require('includes/functions/cache.php');          

function mark_loadFile($url) {
    $ch = curl_init();
	$headers = array( 
		"Accept-Version: 3"
	); 
    curl_setopt($ch, CURLOPT_HEADER, 0);
	//curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_URL, $url);

    $data = curl_exec($ch);
    curl_close($ch);

    return $data;
}

  //$OSCOM_Hooks->register('orders');

  
//mark
function outputClippyAddress($source, $idSuffix) {
	$company = tep_output_string_protected($source['company']);
	if (isset($source['firstname']) && @tep_not_null($source['firstname'])){
		$firstname = tep_output_string_protected($source['firstname']);
		$lastname = tep_output_string_protected($source['lastname']);
		}
	elseif (isset($source['name']) && @tep_not_null($source['name'])){
		$firstname = tep_output_string_protected($source['name']);
		$lastname = '';
	}else{
		$firstname = '';
		$lastname = '';
	}

	if (isset($source['street_address']) && @tep_not_null($source['street_address'])){
		$street = tep_output_string_protected($source['street_address']);
		if (isset($source['suburb']) && @tep_not_null($source['suburb'])){
			$street.= ', ' . tep_output_string_protected($source['suburb']);
			}
		}
	elseif (isset($source['suburb']) && @tep_not_null($source['suburb'])){
		$street = tep_output_string_protected($source['suburb']);
		}

	if (isset($source['city']) && @tep_not_null($source['city'])){
		$city = tep_output_string_protected($source['city']);
		if (isset($source['state']) && @tep_not_null($source['state'])){
			$city.= ', ' . tep_output_string_protected($source['state']);
			}
		}
	elseif (isset($source['state']) && @tep_not_null($source['state'])){
		$city = tep_output_string_protected($source['state']);
		}

	if (isset($source['country_id']) && @tep_not_null($source['country_id'])){
		$country = tep_get_country_name($source['country_id']);
		if (isset($source['zone_id']) && @tep_not_null($source['zone_id'])){
			$state = tep_get_zone_code($source['country_id'], $source['zone_id'], $state);
			}
		}
	elseif (isset($source['country']) && @tep_not_null($source['country'])){
		$country = tep_output_string_protected($source['country']);
		}else{
		$country = '';
		}

	$postcode = tep_output_string_protected($source['postcode']);
	$zip = $postcode;
	$address = '<span class="clipTarget" id="companyCopyTarget' .  $idSuffix . '">' . $company . '</span><img src="images/icons/bullet_chevron.png" id="my-button_text" class="clipboardButton" data-clipboard-target="#companyCopyTarget' .  $idSuffix . '"><br />';
	if (isset($firstname) && @tep_not_null($firstname)){
		if (isset($lastname) && @tep_not_null($lastname)){
				$address .= '<span class="clipTarget" id="firstNameCopyTarget' .  $idSuffix . '">' . $firstname . '</span> <span class="clipTarget" id="lastNameCopyTarget' .  $idSuffix . '">' . $lastname . '<img src="images/icons/bullet_chevron.png" id="my-button_text" class="clipboardButton" data-clipboard-target="#firstNameCopyTarget' .  $idSuffix . '"> <img src="images/icons/bullet_chevron.png" id="my-button_text" class="clipboardButton" data-clipboard-target="#lastNameCopyTarget' .  $idSuffix . '">';
			}
		  else
			{
				$name = explode(' ',$firstname);
				$address .= '<span class="clipTarget" id="nameCopyTarget' .  $idSuffix . '">';
				for ($i=0, $n=sizeof($name); $i<$n; $i++) {
					if($i > 0) $address .= ' ';
					$address .= '<span class="clipTarget" id="name' . $i . $idSuffix . '">' . $name[$i] . '</span><img src="images/icons/bullet_chevron.png" id="my-button_text" class="clipboardButton" data-clipboard-target="#name' . $i . $idSuffix . '">';
				}
				$address .= '</span><img src="images/icons/bullet_chevron.png" id="my-button_text" class="clipboardButton" data-clipboard-target="#nameCopyTarget'. $idSuffix . '"><br />';
			}
		}

	$address .= '<span class="clipTarget" id="addressCopyTarget' .  $idSuffix . '"><span class="clipTarget" id="streetCopyTarget' .  $idSuffix . '">' . $street . '</span><img src="images/icons/bullet_chevron.png" id="my-button_text" class="clipboardButton" data-clipboard-target="#streetCopyTarget' .  $idSuffix . '"><br />
<span class="clipTarget" id="cityCopyTarget' .  $idSuffix . '">' . $city . '</span><img src="images/icons/bullet_chevron.png" id="my-button_text" class="clipboardButton" data-clipboard-target="#cityCopyTarget' .  $idSuffix . '"><br>
<span class="clipTarget" id="zipCopyTarget' .  $idSuffix . '">' . $zip . '</span></span>
<img src="images/icons/bullet_chevron.png" id="my-button_text" class="clipboardButton" data-clipboard-target="#zipCopyTarget' .  $idSuffix . '"><img src="images/icons/bullet_chevron.png" id="my-button_text" class="clipboardButton" data-clipboard-target="#addressCopyTarget' .  $idSuffix . '"><br />';
	$address .= '<span class="clipTarget" id="countryCopyTarget' .  $idSuffix . '">' . $country . '</span><img src="images/icons/bullet_chevron.png" id="my-button_text" class="clipboardButton" data-clipboard-target="#countryCopyTarget' .  $idSuffix . '"><br />';
	return $address;
	}



function days_until($date){
    return (isset($date)) ? floor((strtotime($date) - time())/60/60/24) : FALSE;
}
//endmark

  require('includes/classes/currencies.php');
  $currencies = new currencies();

  $orders_statuses = array();
  $orders_status_array = array();
  $orders_status_query = tep_db_query("select orders_status_id, orders_status_name from " . TABLE_ORDERS_STATUS . " where language_id = '" . (int)$languages_id . "'");
  while ($orders_status = tep_db_fetch_array($orders_status_query)) {
    $orders_statuses[] = array('id' => $orders_status['orders_status_id'],
                               'text' => $orders_status['orders_status_name']);
    $orders_status_array[$orders_status['orders_status_id']] = $orders_status['orders_status_name'];
  }

  $action = (isset($_GET['action']) ? $_GET['action'] : '');

  if (@tep_not_null($action)) {
    switch ($action) {
      case 'update_order':
        $oID = tep_db_prepare_input($_GET['oID']);
        $status = tep_db_prepare_input($_POST['status']);
        $comments = tep_db_prepare_input($_POST['comments']);

        $order_updated = false;
        $check_status_query = tep_db_query("select customers_name, customers_email_address, orders_status, date_purchased from " . TABLE_ORDERS . " where orders_id = '" . (int)$oID . "'");
        $check_status = tep_db_fetch_array($check_status_query);

        if ( ($check_status['orders_status'] != $status) || @tep_not_null($comments)) {
          tep_db_query("update " . TABLE_ORDERS . " set orders_status = '" . tep_db_input($status) . "', last_modified = now() where orders_id = '" . (int)$oID . "'");

          $customer_notified = '0';
          if (isset($_POST['notify']) && ($_POST['notify'] == 'on')) {
            $notify_comments = '';
            if (isset($_POST['notify_comments']) && ($_POST['notify_comments'] == 'on')) {
              $notify_comments = sprintf(EMAIL_TEXT_COMMENTS_UPDATE, $comments) . "\n\n";
            }

            $email = STORE_NAME . "\n" . EMAIL_SEPARATOR . "\n" . EMAIL_TEXT_ORDER_NUMBER . ' ' . $oID . "\n" . EMAIL_TEXT_INVOICE_URL . ' ' . tep_catalog_href_link('account_history_info.php', 'order_id=' . $oID, 'SSL') . "\n" . EMAIL_TEXT_DATE_ORDERED . ' ' . tep_date_long($check_status['date_purchased']) . "\n\n" . $notify_comments . sprintf(EMAIL_TEXT_STATUS_UPDATE, $orders_status_array[$status]);

            tep_mail($check_status['customers_name'], $check_status['customers_email_address'], EMAIL_TEXT_SUBJECT, $email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);

            $customer_notified = '1';
          }

          tep_db_query("insert into " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, orders_status_id, date_added, customer_notified, comments) values ('" . (int)$oID . "', '" . tep_db_input($status) . "', now(), '" . tep_db_input($customer_notified) . "', '" . tep_db_input($comments)  . "')");

          $order_updated = true;
        }

        if ($order_updated == true) {
         $messageStack->add_session(SUCCESS_ORDER_UPDATED, 'success');
        } else {
          $messageStack->add_session(WARNING_ORDER_NOT_UPDATED, 'warning');
        }

        tep_redirect(tep_href_link('orders.php', tep_get_all_get_params(array('action')) . 'action=edit'));
        break;
      case 'deleteconfirm':
        $oID = tep_db_prepare_input($_GET['oID']);

        tep_remove_order($oID, $_POST['restock']);

        tep_redirect(tep_href_link('orders.php', tep_get_all_get_params(array('oID', 'action'))));
        break;
		// MAZ BOF SWITCH DEBIT CARDS BMC Delete CC info Start
// Remove CVV Number
    case 'deleteccinfo':
      $oID = tep_db_prepare_input($_GET['oID']);
	  include('includes/functions/encrypt.php');
		  if ( strtolower(CC_ENC) == 'true' ) {
			$key = 'a8B176J8oB6pZAF32YLivKmO';
			$cipher_data = $check_cc['cc_number'];
			$decipher_data = changedataout($cipher_data,$key);
			$ccnum = changedatain(substr($decipher_data,0,8) . '00000000',$key);
		  }
	  tep_db_query("update " . TABLE_ORDERS . " set cc_issue = '000' where orders_id = '" . tep_db_input($oID) . "'");
      tep_db_query("update " . TABLE_ORDERS . " set cc_number = '". tep_db_input(substr($ccnum,0,8)) . "00000000' where orders_id = '" . tep_db_input($oID) . "'");

      tep_redirect(tep_href_link('orders.php', tep_get_all_get_params(array('oID', 'action'))));
      break;
// MAZ EOF SWITCH BMC Delete CC Info End

    }
  }

  if (($action == 'edit') && isset($_GET['oID'])) {
    $oID = tep_db_prepare_input($_GET['oID']);

    $orders_query = tep_db_query("select orders_id from " . TABLE_ORDERS . " where orders_id = '" . (int)$oID . "'");
    $order_exists = true;
    if (!tep_db_num_rows($orders_query)) {
      $order_exists = false;
      $messageStack->add(sprintf(ERROR_ORDER_DOES_NOT_EXIST, $oID), 'error');
    }
  }

  if (!function_exists('tep_draw_button')) { // v2.2rc2a compatibility
    function tep_draw_button($title = null, $icon = null, $link = null, $priority = null, $params = null) {
      static $button_counter = 1;

      $types = array('submit', 'button', 'reset');

      if ( !isset($params['type']) ) {
        $params['type'] = 'submit';
      }

      if ( !in_array($params['type'], $types) ) {
        $params['type'] = 'submit';
      }

      if ( ($params['type'] == 'submit') && isset($link) ) {
        $params['type'] = 'button';
      }

      if (!isset($priority)) {
        $priority = 'secondary';
      }

      $button = '<span class="tdbLink">';

      if ( ($params['type'] == 'button') && isset($link) ) {
        $button .= '<a id="tdb' . $button_counter . '" href="' . $link . '"';

        if ( isset($params['newwindow']) ) {
          $button .= ' target="_blank"';
        }
      } else {
        $button .= '<button id="tdb' . $button_counter . '" type="' . tep_output_string($params['type']) . '"';
      }

      if ( isset($params['params']) ) {
        $button .= ' ' . $params['params'];
      }

      $button .= '>' . $title;

      if ( ($params['type'] == 'button') && isset($link) ) {
        $button .= '</a>';
      } else {
        $button .= '</button>';
      }

      $button .= '</span><script type="text/javascript">$("#tdb' . $button_counter . '").button(';

      $args = array();

      if ( isset($icon) ) {
        if ( !isset($params['iconpos']) ) {
          $params['iconpos'] = 'left';
        }

        if ( $params['iconpos'] == 'left' ) {
          $args[] = 'icons:{primary:"ui-icon-' . $icon . '"}';
        } else {
          $args[] = 'icons:{secondary:"ui-icon-' . $icon . '"}';
        }
      }

      if (empty($title)) {
        $args[] = 'text:false';
      }

      if (!empty($args)) {
        $button .= '{' . implode(',', $args) . '}';
      }

      $button .= ').addClass("ui-priority-' . $priority . '").parent().removeClass("tdbLink");</script>';

      $button_counter++;

      return $button;
    }
  }

  include('includes/classes/order.php');

  //$OSCOM_Hooks->call('orders', 'orderAction');

  require('includes/template_top.php');

  $base_url = ($request_type == 'SSL') ? HTTPS_SERVER . DIR_WS_HTTPS_ADMIN : HTTP_SERVER . DIR_WS_ADMIN;
?>
<script type="text/javascript">
$( document ).ready(function() {
	var clipboard = new Clipboard('.clipboardButton');

	clipboard.on('success', function(e) {
		console.info('Action:', e.action);
		console.info('Text:', e.text);
		console.info('Trigger:', e.trigger);

		e.clearSelection();
	});

	clipboard.on('error', function(e) {
		console.error('Action:', e.action);
		console.error('Trigger:', e.trigger);
	});
});
</script>
<script>
// v2.2rc2a compatibility
if ( typeof jQuery == 'undefined' ) {
  document.write('<scr' + 'ipt src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.1/jquery.min.js"></scr' + 'ipt>');
}
</script>

<script>
if ( typeof jQuery.ui == 'undefined' ) {
  document.write('<link rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.4/themes/redmond/jquery-ui.css" />');
  document.write('<scr' + 'ipt src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.4/jquery-ui.min.js"></scr' + 'ipt>');

/* Custom jQuery UI */
  document.write('<style>.ui-widget { font-family: Lucida Grande, Lucida Sans, Verdana, Arial, sans-serif; font-size: 11px; } .ui-dialog { min-width: 500px; }</style>');
}
</script>

<?php
  if (($action == 'edit') && ($order_exists == true)) {
    $order = new order($oID);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
      <tr>
        <td width="100%"><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td class="pageHeading"><?php echo 'Order Number: ' . $oID; ?></td>
            <td class="pageHeading"><?php echo HEADING_TITLE; ?></td>
            <td class="pageHeading" align="right" style="text-align:right"><?php echo tep_draw_separator('pixel_trans.gif', 1, HEADING_IMAGE_HEIGHT); ?></td>
<?php /* MAZ BOF SWITCH BMC Delete CC Info Start -- original line -- <td class="pageHeading" align="right" style="text-align:right"><?php echo '<a href="' . tep_href_link('orders.php', tep_get_all_get_params(array('action'))) . '">' . tep_image_button('button_back.gif', IMAGE_BACK) . '</a>'; ?></td>  */ ?>
	        <td class="smallText" align="right" style="text-align:right"><?php echo tep_draw_button('Delete CC Info', 'document', tep_href_link('orders.php', 'oID=' . $_GET['oID'] . '&action=deleteccinfo')); ?></td>
<?php /* MAZ EOF SWITCH BMC Delete CC Info End  */ ?>
            <td class="smallText" align="right" style="text-align:right"><?php echo tep_draw_button(IMAGE_ORDERS_INVOICE, 'document', tep_href_link('orders_invoice.php', 'oID=' . $_GET['oID']), null, array('newwindow' => true)) . tep_draw_button(IMAGE_ORDERS_PACKINGSLIP, 'document', tep_href_link('orders_packingslip.php', 'oID=' . $_GET['oID']), null, array('newwindow' => true)) . tep_draw_button(IMAGE_BACK, 'triangle-1-w', tep_href_link('orders.php', tep_get_all_get_params(array('action')))); ?></td>
          </tr>
        </table></td>
      </tr>
      <tr>
        <td><table width="100%" border="0" cellspacing="0" cellpadding="2">
          <tr>
            <td colspan="3"><?php echo tep_draw_separator(); ?></td>
          </tr>
          <tr>
            <td valign="top"><table width="100%" border="0" cellspacing="0" cellpadding="2">
              <tr>
                <td class="main" valign="top"><strong><?php echo ENTRY_CUSTOMER; ?></strong></td>
                <td class="main">
			<?php //echo tep_address_format($order->customer['format_id'], $order->customer, 1, '', '<br />'); </td>?>
			<script type="text/javascript">
				$( document ).ready(function() {
					$('.clipboardButton').mouseenter(function(){
						$('.clipTarget').removeClass('clipboardButtonHighlight');
						$target = $(this).attr('data-clipboard-target');
						$($target).addClass('clipboardButtonHighlight');
					});
					$('.clipboardButton').mouseleave(function(){
						$target = $(this).attr('data-clipboard-target');
						$('.clipTarget').removeClass('clipboardButtonHighlight');
					});
					$('.clipboardButton').click(function(){
						$target = $(this).attr('data-clipboard-target');
						$($target).addClass("flash");
    
						setTimeout( function(){
							$($target).removeClass("flash");
						}, 1000);	// Timeout must be the same length as the CSS3 transition or longer (or you'll mess up the transition)
						$('.clipTarget').removeClass('clipboardButtonHighlight');
					});
				});
			</script>				
			<?php echo outputClippyAddress($order->customer, 'customer'); 	?>
			</td>
				
              </tr>
            
              <tr>
                <td class="main"><strong><?php echo ENTRY_TELEPHONE_NUMBER; ?></strong></td>
                <td class="main"><span id="telephoneCopyTarget" class="clipTarget" ><?php echo $order->customer['telephone']; ?></span><img src="images/icons/bullet_chevron.png" id="my-button_text" class="clipboardButton" data-clipboard-target="#telephoneCopyTarget"></td>
              </tr>
			    <tr>
                <td class="main"><strong><?php echo 'Mobile Number:'; ?></strong></td>
                <td class="main"><span id="faxnumberCopyTarget" class="clipTarget" ><?php echo $order->customer['mobile']; ?></span><img src="images/icons/bullet_chevron.png" id="my-button_text" class="clipboardButton" data-clipboard-target="#faxnumberCopyTarget"></td>
              </tr>
              <tr>
                <td class="main"><strong><?php echo ENTRY_EMAIL_ADDRESS; ?></strong></td>
                <td class="main"><span id="emailCopyTarget" class="clipTarget" ><?php echo $order->customer['email_address']?></span><img src="images/icons/bullet_chevron.png" id="my-button_text" class="clipboardButton" data-clipboard-target="#emailCopyTarget"></td>
              </tr>
            </table></td>
            <td valign="top"><table width="100%" border="0" cellspacing="0" cellpadding="2">
              <tr>
                <td class="main" valign="top"><strong><?php echo ENTRY_SHIPPING_ADDRESS; ?></strong></td>
              <?php  // <td class="main"><?php echo tep_address_format($order->delivery['format_id'], $order->delivery, 1, '', '<br />');</td> ?>
				<td class="main"><?php echo outputClippyAddress($order->delivery, 'shipping'); ?></td>
              </tr>
            </table></td>
            <td valign="top"><table width="100%" border="0" cellspacing="0" cellpadding="2">
              <tr>
                <td class="main" valign="top"><strong><?php echo ENTRY_BILLING_ADDRESS; ?></strong></td>
                <?php //<td class="main"><?php echo tep_address_format($order->billing['format_id'], $order->billing, 1, '', '<br />'); </td>?>
             	<td class="main"><?php echo outputClippyAddress($order->billing, 'billing'); ?></td>
              </tr>
            </table></td>
          </tr>
        </table></td>
      </tr>
      <tr>
        <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      </tr>
      <tr>
        <td><table class="paymentTable" border="0" cellspacing="0" cellpadding="2">
          <tr>
            <td class="main"><strong><?php echo ENTRY_PAYMENT_METHOD; ?></strong></td>
            <td class="main"><?php echo $order->info['payment_method']; ?></td>
          </tr>
<?php
    if (@tep_not_null($order->info['cc_type']) || @tep_not_null($order->info['cc_owner']) || @tep_not_null($order->info['cc_number'])) {
?><?php
// MAZ BOF SWICTH BMC CC Mod Start
if ($order->info['cc_number'] != '0000000000000000') { 
  if ( strtolower(CC_ENC) == 'true' ) {
    $key = 'a8B176J8oB6pZAF32YLivKmO';
    $cipher_data = $order->info['cc_number'];
    $order->info['cc_number'] = changedataout($cipher_data,$key);
  }
}
// MAZ EOF SWITCH BMC CC Mod End
?>

          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <tr>
            <td class="main"><?php echo ENTRY_CREDIT_CARD_TYPE; ?></td>
            <td class="main2"><?php echo $order->info['cc_type']; ?></td>
		<?php /*    <td class="main2"><?php echo $order->info['cc_po_no']; ?></td>
			<td class="main"><?php echo 'Purchase Order Number'; ?></td>        */ ?>     
          <td rowspan="5"><?php
			if (substr($order->info['cc_number'],0,8) != "00000000"){
				try {
					$cache_output = '';		
					$cached = true;
					//$testurl = "https://api.bincodes.com/bin/?format=json&api_key=aad8232ca958ee709c2093d6d14203f2&bin=" . substr($order->info['cc_number'],0,6);
						
					if( !read_cache( $cache_output, 'ccnum-' . substr($order->info['cc_number'],0,8) . '.cache', false ) ) {
						//$theJSON  = mark_loadFile('https://lookup.binlist.net/' . substr($order->info['cc_number'],0,8));
						$theJSON = file_get_contents("https://api.bincodes.com/cc/?format=json&api_key=aad8232ca958ee709c2093d6d14203f2&cc=" . $order->info['cc_number']);
						$cache_output = $theJSON;	
						$cached = false;
						write_cache ( $cache_output, 'ccnum-' . substr($order->info['cc_number'],0,8) . '.cache' );
					}
					
					$creditAPI = json_decode($cache_output);
					
					if ($cached == true){
						$cached = 'Cached ';
					} else {
						$cached = 'Live ';
					}
					echo '<table width="100%" border="0" cellspacing="0" cellpadding="2">'
					. '<td  class="main">' . $cached . ' <a href="https://bincodes.com">BIMCodes.com</a> data: </td><td class="main2"></td></tr>'
					. '<td  class="main">Bank: </td><td class="main2">' . $creditAPI->{'Bank'}. '</td></tr>'
					. '<td  class="main">Card: </td><td class="main2">' . $creditAPI->{'card'}. '</td></tr>'
					. '<td  class="main">Type: </td><td class="main2">' . $creditAPI->{'type'}. '</td></tr>'
					. '<td  class="main">Level: </td><td class="main2">' . $creditAPI->{'level'}. '</td></tr>'
					. '<td  class="main">Country:</td><td class="main2">' . $creditAPI->{'country'}. '</td></tr>'
					. '<td  class="main" colspan="2"><a class="btn btn-primary btn-xs" href="https://bincheck.io/bin-checker">bincheck Website</a>' 
					. '</table>';
					//echo $testurl;
				} catch(Exception $e){
					echo 'binList.net data lookup failed';
				}
			}
			?>
			</td></tr>
          <tr>
            <td class="main"><?php echo ENTRY_CREDIT_CARD_OWNER; ?></td>
            <td class="main2"><?php echo $order->info['cc_owner']; ?></td>
		<?php /*	<td class="main"><?php echo 'Purchase Order Contact'; ?></td>
            <td class="main2"><?php echo $order->info['cc_po_contact_name']; ?></td> */ ?>
          </tr>
		  
          <tr>
            <td class="main"><?php echo ENTRY_CREDIT_CARD_NUMBER; ?></td>
			
        <td class="main2"><?php echo chunk_split($order->info['cc_number'], 4, ' ');  ?></td>
	<?php /*	<td class="main"><?php echo 'Purchase Order Department'; ?></td>
<td class="main"><?php echo $order->info['cc_po_department']; ?></td> */ ?>
          </tr>
		  <?php /* MAZ BOF SWITCH BMC CC MOD Start  */ ?>
		<tr>
            <td class="main"><?php echo 'Credit Card Start:'; ?></td>
            <td class="main2"><?php echo $order->info['cc_start']; ?></td>
        </tr>
        <tr>
          <td class="main"><?php echo ENTRY_CREDIT_CARD_EXPIRES; ?></td>
          <td class="main2"><?php echo $order->info['cc_expires']; ?></td>
        </tr>
        <tr>
          <td class="main"><?php echo 'CVV:'; ?></td>
          <td class="main2"><?php echo $order->info['cc_cvv']; ?></td>
        </tr> 
        <tr>
          <td class="main"><?php echo 'Issue Number:'; ?></td>
            <td class="main2"><?php echo $order->info['cc_issue']; ?></td>
        </tr>
		  
<?php
    }

//institutional purchase order payment begins
	if(@tep_not_null($order->info['ip_order_no']) || @tep_not_null($order->info['ip_requested_by']) || @tep_not_null($order->info['ip_contact_person'])) {
?>
			<tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <tr>
            <td class="main"><?php echo ENTRY_INSTITUTIONAL_PURCHASE_ORDER_NO; ?></td>
            <td class="main"><?php echo $order->info['ip_order_no']; ?></td>
          </tr>
          <tr>
            <td class="main"><?php echo ENTRY_INSTITUTIONAL_PURCHASE_REQUESTED_BY; ?></td>
            <td class="main"><?php echo $order->info['ip_requested_by']; ?></td>
          </tr>
          <tr>
            <td class="main"><?php echo ENTRY_INSTITUTIONAL_PURCHASE_CONTACT_PERSON; ?></td>
            <td class="main"><?php echo $order->info['ip_contact_person']; ?></td>
          </tr>
<?php
	
		//institutional purchase order ends ?>
		  
		  
		  
<?php
// MAZ purchaseorder start
    } else if( (($order->info['account_name']) || ($order->info['account_number']) || ($order->info['po_number'])) ) {
?>
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <tr>
            <td class="main"><?php echo 'Accounts Payable Email'; ?></td>
            <td class="main"><?php echo $order->info['account_name']; ?></td>
          </tr>
          <tr>
            <td class="main"><?php echo 'Contact Name'; ?></td>
            <td class="main"><?php echo $order->info['account_number']; ?></td>
          </tr>
          <tr>
            <td class="main"><?php echo'PO Number'; ?></td>
            <td class="main"><?php echo $order->info['po_number']; ?></td>
          </tr>
<?php
    }
?>
        </table></td>
      </tr>
      <tr>
        <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      </tr>
      <tr>
        <td><table class="table table-striped table-condensed small" border="0" width="100%" cellspacing="0" cellpadding="2">
		<thead>
          <tr>
            <th colspan="2"><?php echo TABLE_HEADING_PRODUCTS; ?></th>
            <th><?php echo TABLE_HEADING_PRODUCTS_MODEL; ?></th>
            <th align="right" style="text-align:right"><?php echo TABLE_HEADING_TAX; ?></th>
            <th align="right" style="text-align:right"><?php echo TABLE_HEADING_PRICE_EXCLUDING_TAX; ?></th>
            <th align="right" style="text-align:right"><?php echo TABLE_HEADING_PRICE_INCLUDING_TAX; ?></th>
            <th align="right" style="text-align:right"><?php echo TABLE_HEADING_TOTAL_EXCLUDING_TAX; ?></th>
            <th align="right" style="text-align:right"><?php echo TABLE_HEADING_TOTAL_INCLUDING_TAX; ?></th>
          </tr><tbody>
<?php
    for ($i=0, $n=sizeof($order->products); $i<$n; $i++) {
      echo '          <tr>' . "\n" .
           '            <td valign="top" align="right" style="text-align:right">' . $order->products[$i]['qty'] . '&nbsp;x</td>' . "\n" .
           '            <td valign="top">' . $order->products[$i]['name'];

      if (isset($order->products[$i]['attributes']) && (sizeof($order->products[$i]['attributes']) > 0)) {
        for ($j = 0, $k = sizeof($order->products[$i]['attributes']); $j < $k; $j++) {
          echo '<br /><nobr><small>&nbsp;<i> - ' . $order->products[$i]['attributes'][$j]['option'] . ': ' . $order->products[$i]['attributes'][$j]['value'];
          echo '</i></small></nobr>';
        }
      }

      echo '            </td>' . "\n" .
           '            <td valign="top">' . $order->products[$i]['model'] . '</td>' . "\n" .
           '            <td align="right" style="text-align:right" valign="top">' . tep_display_tax_value($order->products[$i]['tax']) . '%</td>' . "\n" .
           '            <td align="right" style="text-align:right" valign="top"><strong>' . $currencies->format($order->products[$i]['final_price'], true, $order->info['currency'], $order->info['currency_value']) . '</strong></td>' . "\n" .
           '            <td align="right" style="text-align:right" valign="top"><strong>' . $currencies->format(tep_add_tax($order->products[$i]['final_price'], $order->products[$i]['tax'], true), true, $order->info['currency'], $order->info['currency_value']) . '</strong></td>' . "\n" .
           '            <td align="right" style="text-align:right" valign="top"><strong>' . $currencies->format($order->products[$i]['final_price'] * $order->products[$i]['qty'], true, $order->info['currency'], $order->info['currency_value']) . '</strong></td>' . "\n" .
           '            <td align="right" style="text-align:right" valign="top"><strong>' . $currencies->format(tep_add_tax($order->products[$i]['final_price'], $order->products[$i]['tax'], true) * $order->products[$i]['qty'], true, $order->info['currency'], $order->info['currency_value']) . '</strong></td>' . "\n";
      echo '          </tr>' . "\n";
    }
?>
          <tr>
            <td align="right" colspan="8"><table border="0" cellspacing="0" cellpadding="2">
<?php
    for ($i = 0, $n = sizeof($order->totals); $i < $n; $i++) {
      echo '              <tr>' . "\n" .
           '                <td align="right" style="text-align:right" class="smallText">' . $order->totals[$i]['title'] . '</td>' . "\n" .
           '                <td align="right" style="text-align:right" class="smallText">' . $order->totals[$i]['text'] . '</td>' . "\n" .
           '              </tr>' . "\n";
    }
?>
            </tbody></table></td>
          </tr>
        </table></td>
      </tr>
      <tr class="invoiceFooter">
        <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      </tr>
      <tr class="">
        <td class="main"><table width="99%" border="1" cellspacing="0" cellpadding="5">
      <tr>
            <td class="smallText" align="center" style="white-space:nowrap"><strong><?php echo TABLE_HEADING_DATE_ADDED; ?></strong></td>
            <td class="smallText invoiceFooter" style="white-space:nowrap" align="center"><strong><?php echo TABLE_HEADING_CUSTOMER_NOTIFIED; ?></strong></td>
            <td class="smallText" align="center"><strong><?php echo TABLE_HEADING_STATUS; ?></strong></td>
            <td class="smallText" align="center" width="80%"><strong><?php echo TABLE_HEADING_COMMENTS; ?></strong></td>
          </tr>
<?php
    $orders_history_query = tep_db_query("select orders_status_id, date_added, customer_notified, comments from " . TABLE_ORDERS_STATUS_HISTORY . " where orders_id = '" . tep_db_input($oID) . "' order by date_added");
    if (tep_db_num_rows($orders_history_query)) {
      while ($orders_history = tep_db_fetch_array($orders_history_query)) {
        echo '          <tr>' . "\n" .
             '            <td class="smallText" style="white-space:nowrap" align="center">' . tep_datetime_short($orders_history['date_added']) . '</td>' . "\n" .
             '            <td class="smallText invoiceFooter" align="center">';
        if ($orders_history['customer_notified'] == '1') {
          echo tep_image('images/icons/tick.gif', ICON_TICK) . "</td>\n";
        } else {
          echo tep_image('images/icons/cross.gif', ICON_CROSS) . "</td>\n";
        }
        echo '            <td class="smallText">' . $orders_status_array[$orders_history['orders_status_id']] . '</td>' . "\n" .
             '            <td class="smallText">' . nl2br(tep_db_output($orders_history['comments'])) . '&nbsp;</td>' . "\n" .
             '          </tr>' . "\n";
      }
    } else {
        echo '          <tr>' . "\n" .
             '            <td class="smallText" colspan="10">' . TEXT_NO_ORDER_HISTORY . '</td>' . "\n" .
             '          </tr>' . "\n";
    }
?>
        </table></td>
      </tr>
      <tr>
        <td class="main invoiceFooter"><br /><strong><?php echo TABLE_HEADING_COMMENTS; ?></strong></td>
      </tr>
      <tr>
        <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '5'); ?></td>
      </tr>
      <tr><?php echo tep_draw_form('status', 'orders.php', tep_get_all_get_params(array('action')) . 'action=update_order'); ?>
        <td class="main invoiceFooter"><?php echo tep_draw_textarea_field('comments', 'soft', '60', '5'); ?></td>
      </tr>
      <tr class="invoiceFooter">
        <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      </tr>
      <tr class="invoiceFooter">
        <td><table border="0" cellspacing="0" cellpadding="2">
          <tr>
            <td><table border="0" cellspacing="0" cellpadding="2">
              <tr>
                <td class="main"><strong><?php echo ENTRY_STATUS; ?></strong> <?php echo tep_draw_pull_down_menu('status', $orders_statuses, $order->info['orders_status']); ?></td>
              </tr>
              <tr>
                <td class="main"><strong><?php echo ENTRY_NOTIFY_CUSTOMER; ?></strong> <?php echo tep_draw_checkbox_field('notify', '', true); ?></td>
                <td class="main"><strong><?php echo ENTRY_NOTIFY_COMMENTS; ?></strong> <?php echo tep_draw_checkbox_field('notify_comments', '', true); ?></td>
              </tr>
            </table></td>
            <td class="smallText" valign="top"><?php echo tep_draw_button(IMAGE_UPDATE, 'disk', null, 'primary'); ?></td>
          </tr>
        </table></td>
      </form></tr>
      <tr class="invoiceFooter">
<?php /* MAZ BOF SWITCH BMC Delete CC Info Start -- Original line -- <td colspan="2" align="right" style="text-align:right"><?php echo '<a href="' . tep_href_link('orders_invoice.php', 'oID=' . $_GET['oID']) . '" TARGET="_blank">' . tep_image_button('button_invoice.gif', IMAGE_ORDERS_INVOICE) . '</a> <a href="' . tep_href_link('orders_packingslip.php', 'oID=' . $_GET['oID']) . '" TARGET="_blank">' . tep_image_button('button_packingslip.gif', IMAGE_ORDERS_PACKINGSLIP) . '</a> <a href="' . tep_href_link('orders.php', tep_get_all_get_params(array('action'))) . '">' . tep_image_button('button_back.gif', IMAGE_BACK) . '</a>'; ?></td>  */ ?>
        <td colspan="2" align="right" style="text-align:right"><?php echo '<a href="' . tep_href_link('orders.php', 'oID=' . $_GET['oID'] . '&action=deleteccinfo') . '">Delete CC Info</a>' . '<a href="' . tep_href_link('orders_invoice.php', 'oID=' . $_GET['oID']) . '" TARGET="_blank">' . tep_image_button('button_invoice.gif', IMAGE_ORDERS_INVOICE) . '</a> <a href="' . tep_href_link('orders_packingslip.php', 'oID=' . $_GET['oID']) . '" TARGET="_blank">' . tep_image_button('button_packingslip.gif', IMAGE_ORDERS_PACKINGSLIP) . '</a> <a href="' . tep_href_link('orders.php', tep_get_all_get_params(array('action'))) . '">' . tep_image_button('button_back.gif', IMAGE_BACK) . '</a>'; ?></td>
<?php /* MAZ EOF SWITCH BMC Delete CC Info End  */ ?>
      </tr>
<?php
  } else {
?>
      <tr>
        <td width="100%"><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td class="pageHeading"><?php echo HEADING_TITLE; ?></td>
            <td class="pageHeading" align="right" style="text-align:right"><?php echo tep_draw_separator('pixel_trans.gif', 1, HEADING_IMAGE_HEIGHT); ?></td>
            <td align="right" style="text-align:right"><table border="0" width="100%" cellspacing="0" cellpadding="0">
              <tr><?php echo tep_draw_form('orders', 'orders.php', '', 'get'); ?>
                <td class="smallText" align="right" style="text-align:right"><?php echo HEADING_TITLE_SEARCH . ' ' . tep_draw_input_field('oID', '', 'size="12"') . tep_draw_hidden_field('action', 'edit'); ?></td>
              <?php echo tep_hide_session_id(); ?></form></tr>
              <tr><?php echo tep_draw_form('status', 'orders.php', '', 'get'); ?>
                <td class="smallText" align="right" style="text-align:right"><?php echo HEADING_TITLE_STATUS . ' ' . tep_draw_pull_down_menu('status', array_merge(array(array('id' => '', 'text' => TEXT_ALL_ORDERS)), $orders_statuses), '', 'onchange="this.form.submit();"'); ?></td>
              <?php echo tep_hide_session_id(); ?></form></tr>
            </table></td>
          </tr>
        </table></td>
      </tr>
      <tr>
        <td><table  border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td valign="top"><table class="table table-striped table-condensed small" border="0" width="100%" cellspacing="0" cellpadding="2">
            <tr><thead>
                <th><?php echo TABLE_HEADING_CUSTOMERS; ?></th>
                <th align="right" style="text-align:right"><?php echo TABLE_HEADING_ORDER_TOTAL; ?></th>
                <th align="center"><?php echo TABLE_HEADING_DATE_PURCHASED; ?></th>
                <th align="right" style="text-align:right"><?php echo TABLE_HEADING_STATUS; ?></th>
                <th align="right" style="text-align:right"><?php echo TABLE_HEADING_ACTION; ?>&nbsp;</th>
              </tr></thead><tbody>
<?php
    if (isset($_GET['cID'])) {
      $cID = tep_db_prepare_input($_GET['cID']);
      $orders_query_raw = "select o.orders_id, o.customers_name, o.customers_id, o.payment_method, o.date_purchased, o.last_modified, o.currency, o.currency_value, s.orders_status_name, ot.text as order_total from " . TABLE_ORDERS . " o left join " . TABLE_ORDERS_TOTAL . " ot on (o.orders_id = ot.orders_id), " . TABLE_ORDERS_STATUS . " s where o.customers_id = '" . (int)$cID . "' and o.orders_status = s.orders_status_id and s.language_id = '" . (int)$languages_id . "' and ot.class = 'ot_total' order by orders_id DESC";
    } elseif (isset($_GET['status']) && is_numeric($_GET['status']) && ($_GET['status'] > 0)) {
      $status = tep_db_prepare_input($_GET['status']);
      $orders_query_raw = "select o.orders_id, o.customers_name, o.payment_method, o.date_purchased, o.last_modified, o.currency, o.currency_value, s.orders_status_name, ot.text as order_total from " . TABLE_ORDERS . " o left join " . TABLE_ORDERS_TOTAL . " ot on (o.orders_id = ot.orders_id), " . TABLE_ORDERS_STATUS . " s where o.orders_status = s.orders_status_id and s.language_id = '" . (int)$languages_id . "' and s.orders_status_id = '" . (int)$status . "' and ot.class = 'ot_total' order by o.orders_id DESC";
    } else {
      $orders_query_raw = "select o.orders_id, o.customers_name, o.payment_method, o.date_purchased, o.last_modified, o.currency, o.currency_value, s.orders_status_name, ot.text as order_total from " . TABLE_ORDERS . " o left join " . TABLE_ORDERS_TOTAL . " ot on (o.orders_id = ot.orders_id), " . TABLE_ORDERS_STATUS . " s where o.orders_status = s.orders_status_id and s.language_id = '" . (int)$languages_id . "' and ot.class = 'ot_total' order by o.orders_id DESC";
    }
    $orders_split = new splitPageResults($_GET['page'], MAX_DISPLAY_SEARCH_RESULTS, $orders_query_raw, $orders_query_numrows);
    $orders_query = tep_db_query($orders_query_raw);
    while ($orders = tep_db_fetch_array($orders_query)) {
    if ((!isset($_GET['oID']) || (isset($_GET['oID']) && ($_GET['oID'] == $orders['orders_id']))) && !isset($oInfo)) {
        $oInfo = new objectInfo($orders);
      }

      if (isset($oInfo) && is_object($oInfo) && ($orders['orders_id'] == $oInfo->orders_id)) {
        echo '              <tr class="success" onclick="document.location.href=\'' . tep_href_link('orders.php', tep_get_all_get_params(array('oID', 'action')) . 'oID=' . $oInfo->orders_id . '&action=edit') . '\'">' . "\n";
      } else {
        echo '              <tr onclick="document.location.href=\'' . tep_href_link('orders.php', tep_get_all_get_params(array('oID')) . 'oID=' . $orders['orders_id']) . '\'">' . "\n";
      }
?>
                <td><?php echo '<a href="' . tep_href_link('orders.php', tep_get_all_get_params(array('oID', 'action')) . 'oID=' . $orders['orders_id'] . '&action=edit') . '">' . tep_image('images/icons/preview.gif', ICON_PREVIEW) . '</a>&nbsp;' . $orders['customers_name']; ?></td>
                <td align="right" style="text-align:right"><?php echo strip_tags($orders['order_total']); ?></td>
                <td align="center"><?php echo tep_datetime_short($orders['date_purchased']); ?></td>
                <td align="right" style="text-align:right"><?php echo $orders['orders_status_name']; ?></td>
                <td align="right" style="text-align:right"><?php if (isset($oInfo) && is_object($oInfo) && ($orders['orders_id'] == $oInfo->orders_id)) { echo tep_image('images/icon_arrow_right.gif', ''); } else { echo '<a href="' . tep_href_link('orders.php', tep_get_all_get_params(array('oID')) . 'oID=' . $orders['orders_id']) . '">' . tep_image('images/icon_info.gif', IMAGE_ICON_INFO) . '</a>'; } ?>&nbsp;</td>
              </tr>
<?php
    }
?>
              <tr>
                <td colspan="5"><table border="0" width="100%" cellspacing="0" cellpadding="2">
                  <tr>
                    <td class="smallText" valign="top"><?php echo $orders_split->display_count($orders_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_GET['page'], TEXT_DISPLAY_NUMBER_OF_ORDERS); ?></td>
                    <td class="smallText" align="right" style="text-align:right"><?php echo $orders_split->display_links($orders_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $_GET['page'], tep_get_all_get_params(array('page', 'oID', 'action'))); ?></td>
                  </tr>
				 </tbody>
                </table></td>
              </tr><tr><td class="smallText">
<?// Mark - added button to Set all 'processing' to 'delivered'.?>
<?php echo tep_draw_form('sql_interface', 'sql_interface2.php', 'post') . tep_draw_hidden_field('action', 'process'); ?>
                        <?php echo "Set all 'processing' to 'delivered': " . tep_image_submit('button_send.gif', 'Send') . tep_draw_separator('pixel_trans.gif', '10', '1'); ?></form>
		<?//end mark?>
			   </td></tr>
            </table></td>
<?php
  $heading = array();
  $contents = array();

  switch ($action) {
    case 'delete':
      $heading[] = array('text' => '<strong>' . TEXT_INFO_HEADING_DELETE_ORDER . '</strong>');

      $contents = array('form' => tep_draw_form('orders', 'orders.php', tep_get_all_get_params(array('oID', 'action')) . 'oID=' . $oInfo->orders_id . '&action=deleteconfirm'));
      $contents[] = array('text' => TEXT_INFO_DELETE_INTRO . '<br /><br /><strong>' . $cInfo->customers_firstname . ' ' . $cInfo->customers_lastname . '</strong>');
      $contents[] = array('text' => '<br />' . tep_draw_checkbox_field('restock') . ' ' . TEXT_INFO_RESTOCK_PRODUCT_QUANTITY);
      $contents[] = array('align' => 'center', 'text' => '<br />' . tep_draw_button(IMAGE_DELETE, 'trash', null, 'primary') . tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('orders.php', tep_get_all_get_params(array('oID', 'action')) . 'oID=' . $oInfo->orders_id)));
      break;
    default:
      if (isset($oInfo) && is_object($oInfo)) {
        $heading[] = array('text' => '<strong>[' . $oInfo->orders_id . ']&nbsp;&nbsp;' . tep_datetime_short($oInfo->date_purchased) . '</strong>');

// MAZ BOF SWITCH DEBIT CARDS BMC Delete CC Info Start
        $contents[] = array('align' => 'center', 'text' => '<a href="' . tep_href_link('orders.php', tep_get_all_get_params(array('oID', 'action')) . 'oID=' . $oInfo->orders_id . '&action=edit') . '">' . tep_image_button('button_edit.gif', IMAGE_EDIT) . '</a> <a href="' . tep_href_link('orders.php', tep_get_all_get_params(array('oID', 'action')) . 'oID=' . $oInfo->orders_id . '&action=delete') . '">' . tep_image_button('button_delete.gif', IMAGE_DELETE) . '</a> <a href="' . tep_href_link('orders.php', tep_get_all_get_params(array('oID', 'action')) . 'oID=' . $oInfo->orders_id . '&action=deleteccinfo') . '">Delete CC Info</a>');
// MAZ EOF SWITCH BMC Delete CC Info End
        $contents[] = array('align' => 'center', 'text' => '<a href="' . tep_href_link('orders_invoice.php', 'oID=' . $oInfo->orders_id) . '" TARGET="_blank">' . tep_image_button('button_invoice.gif', IMAGE_ORDERS_INVOICE) . '</a> <a href="' . tep_href_link('orders_packingslip.php', 'oID=' . $oInfo->orders_id) . '" TARGET="_blank">' . tep_image_button('button_packingslip.gif', IMAGE_ORDERS_PACKINGSLIP) . '</a>');
        $contents[] = array('text' => '<br />' . TEXT_DATE_ORDER_CREATED . ' ' . tep_date_short($oInfo->date_purchased));
        if (@tep_not_null($oInfo->last_modified)) $contents[] = array('text' => TEXT_DATE_ORDER_LAST_MODIFIED . ' ' . tep_date_short($oInfo->last_modified));
        $contents[] = array('text' => '<br />' . TEXT_INFO_PAYMENT_METHOD . ' '  . $oInfo->payment_method);
      }
      break;
  }

  if ( (@tep_not_null($heading)) && (@tep_not_null($contents)) ) {
    echo '            <td width="25%" valign="top">' . "\n";

    $box = new box;
    echo $box->infoBox($heading, $contents);

    echo '            </td>' . "\n";
  }
?>
          </tr>
        </table></td>
      </tr>
    </table>
<?php
  }

  require('includes/template_bottom.php');
  require('includes/application_bottom.php');
?>
