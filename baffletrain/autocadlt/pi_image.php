// save this as upload_pi_image.php
<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_FILES['pi_image'])) {
        $errors = [];
        $file_name = $_FILES['pi_image']['name'];
        $file_tmp = $_FILES['pi_image']['tmp_name'];
        $file_type = $_FILES['pi_image']['type'];
        $file_ext = strtolower(end(explode('.', $_FILES['pi_image']['name'])));

        $extensions = ["jpeg", "jpg", "png"];

        if (in_array($file_ext, $extensions) === false) {
            $errors[] = "Extension not allowed, please choose a JPEG or PNG file.";
        }

        if (empty($errors) == true) {
            move_uploaded_file($file_tmp, "images/" . $file_name);
            echo json_encode(["success" => true, "filename" => $file_name]);
        } else {
            echo json_encode(["success" => false, "errors" => $errors]);
        }
    }
}
?>
