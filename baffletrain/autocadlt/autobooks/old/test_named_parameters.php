<?php
/**
 * Test script to verify named parameter generation logic
 */

echo "<h2>Testing Named Parameter Generation Logic</h2>\n";

// Test the parameter generation logic directly
class TestDatabase {
    /**
     * Generate a named parameter that matches the column name
     *
     * @param string $column The column name
     * @param string $context The context (insert, set, where, etc.)
     * @param int|null $index Optional index for uniqueness
     * @return string The named parameter (e.g., :column_name, :set_column_name, :where_column_name_0)
     */
    public function generateColumnParamName(string $column, string $context = '', ?int $index = null): string {
        // Clean column name to be safe for parameter names
        $cleanColumn = preg_replace('/[^a-zA-Z0-9_]/', '_', $column);

        // Build parameter name
        $paramName = ':';
        if (!empty($context)) {
            $paramName .= $context . '_';
        }
        $paramName .= $cleanColumn;

        // Add index if provided for uniqueness
        if ($index !== null) {
            $paramName .= '_' . $index;
        }

        return $paramName;
    }

    public function testInsertParameterGeneration(array $data): array {
        $namedParams = [];
        $placeholders = [];

        foreach (array_keys($data) as $column) {
            $paramName = $this->generateColumnParamName($column);
            $placeholders[] = $paramName;
            $namedParams[$paramName] = $data[$column];
        }

        return [
            'placeholders' => $placeholders,
            'params' => $namedParams,
            'query_part' => implode(', ', $placeholders)
        ];
    }

    public function testUpdateParameterGeneration(array $data): array {
        $namedParams = [];
        $setParts = [];

        foreach ($data as $column => $value) {
            $paramName = $this->generateColumnParamName($column, 'set');
            $setParts[] = "`$column` = $paramName";
            $namedParams[$paramName] = $value;
        }

        return [
            'set_parts' => $setParts,
            'params' => $namedParams,
            'query_part' => implode(', ', $setParts)
        ];
    }

    public function testWhereParameterGeneration(array $conditions): array {
        $namedParams = [];
        $whereParts = [];

        foreach ($conditions as $index => $condition) {
            list($column, $operator, $value) = $condition;
            $paramName = $this->generateColumnParamName($column, 'where', $index);
            $whereParts[] = "`{$column}` {$operator} {$paramName}";
            $namedParams[$paramName] = $value;
        }

        return [
            'where_parts' => $whereParts,
            'params' => $namedParams,
            'query_part' => implode(' AND ', $whereParts)
        ];
    }
}

try {
    $testDb = new TestDatabase();

    // Test 1: Insert parameter generation
    echo "<h3>Test 1: Insert Parameter Generation</h3>\n";
    $insertData = [
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'age' => 30,
        'created_at' => '2024-01-01 12:00:00'
    ];

    $insertResult = $testDb->testInsertParameterGeneration($insertData);
    echo "<strong>Insert Data:</strong><br>\n";
    echo "<pre>" . print_r($insertData, true) . "</pre>\n";
    echo "<strong>Generated Parameters:</strong><br>\n";
    echo "<pre>" . print_r($insertResult['params'], true) . "</pre>\n";
    echo "<strong>Query Part:</strong><br>\n";
    echo "<code>VALUES (" . $insertResult['query_part'] . ")</code><br>\n";
    echo "✓ Insert parameters generated successfully<br><br>\n";

    // Test 2: Update parameter generation
    echo "<h3>Test 2: Update Parameter Generation</h3>\n";
    $updateData = [
        'name' => 'John Smith',
        'age' => 31,
        'updated_at' => '2024-01-02 12:00:00'
    ];

    $updateResult = $testDb->testUpdateParameterGeneration($updateData);
    echo "<strong>Update Data:</strong><br>\n";
    echo "<pre>" . print_r($updateData, true) . "</pre>\n";
    echo "<strong>Generated Parameters:</strong><br>\n";
    echo "<pre>" . print_r($updateResult['params'], true) . "</pre>\n";
    echo "<strong>Query Part:</strong><br>\n";
    echo "<code>SET " . $updateResult['query_part'] . "</code><br>\n";
    echo "✓ Update parameters generated successfully<br><br>\n";

    // Test 3: Where parameter generation
    echo "<h3>Test 3: Where Parameter Generation</h3>\n";
    $whereConditions = [
        ['email', '=', '<EMAIL>'],
        ['age', '>', 25],
        ['name', 'LIKE', 'John%'],
        ['status', 'IN', ['active', 'pending']]
    ];

    $whereResult = $testDb->testWhereParameterGeneration($whereConditions);
    echo "<strong>Where Conditions:</strong><br>\n";
    echo "<pre>" . print_r($whereConditions, true) . "</pre>\n";
    echo "<strong>Generated Parameters:</strong><br>\n";
    echo "<pre>" . print_r($whereResult['params'], true) . "</pre>\n";
    echo "<strong>Query Part:</strong><br>\n";
    echo "<code>WHERE " . $whereResult['query_part'] . "</code><br>\n";
    echo "✓ Where parameters generated successfully<br><br>\n";

    // Test 4: Complex column names
    echo "<h3>Test 4: Complex Column Names</h3>\n";
    $complexData = [
        'user.name' => 'Test User',
        'profile-image' => 'image.jpg',
        'meta_data' => '{"key": "value"}',
        'created@timestamp' => time()
    ];

    $complexResult = $testDb->testInsertParameterGeneration($complexData);
    echo "<strong>Complex Column Data:</strong><br>\n";
    echo "<pre>" . print_r($complexData, true) . "</pre>\n";
    echo "<strong>Generated Parameters (cleaned):</strong><br>\n";
    echo "<pre>" . print_r($complexResult['params'], true) . "</pre>\n";
    echo "<strong>Query Part:</strong><br>\n";
    echo "<code>VALUES (" . $complexResult['query_part'] . ")</code><br>\n";
    echo "✓ Complex column names handled successfully<br><br>\n";

    echo "<h3>✅ All parameter generation tests completed successfully!</h3>\n";
    echo "<p><strong>Summary:</strong> The named parameter system now generates parameters that match column names, making debugging easier and providing better IDE support.</p>\n";

} catch (Exception $e) {
    echo "<h3>❌ Test failed with error:</h3>\n";
    echo "<div style='color: red; background: #ffe6e6; padding: 10px; border: 1px solid #ff0000;'>\n";
    echo "<strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "<br>\n";
    echo "<strong>File:</strong> " . $e->getFile() . "<br>\n";
    echo "<strong>Line:</strong> " . $e->getLine() . "<br>\n";
    echo "</div>\n";
}

echo "<hr>\n";
echo "<p><em>Test completed at " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
