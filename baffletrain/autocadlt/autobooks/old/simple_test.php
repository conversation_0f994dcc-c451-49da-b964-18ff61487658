<?php
// Simple test to check if the autoloader can find the data_importer class
define('API_RUN', true);

// Define the constants manually for testing
define('FS_SYS_CLASSES', __DIR__ . '/system/classes/');
define('FS_CLASSES', __DIR__ . '/resources/classes/');
define('FS_API', __DIR__ . '/api/');
define('FS_COMPONENTS', __DIR__ . '/resources/components/');
define('FS_FUNCTIONS', __DIR__ . '/resources/functions/');
define('FS_VIEWS', __DIR__ . '/resources/views/');
define('FS_SYS_FUNCTIONS', __DIR__ . '/system/functions/');

// Load the autoloader
require_once(__DIR__ . "/system/autoloader.php");

echo "Testing autoloader...\n";
echo "FS_SYS_CLASSES: " . FS_SYS_CLASSES . "\n";

// Check if the file exists
$file_path = FS_SYS_CLASSES . 'data_importer.class.php';
echo "Looking for file: " . $file_path . "\n";
echo "File exists: " . (file_exists($file_path) ? 'YES' : 'NO') . "\n";

// Test loading the data_importer class
echo "Attempting to load system\\data_importer...\n";

try {
    // Try to use the class
    if (class_exists('system\\data_importer')) {
        echo "SUCCESS: Class exists!\n";
        
        // Test the static method call
        echo "Testing static method call...\n";
        $result = system\data_importer::process_json_data([], [], [], true, "test");
        echo "SUCCESS: Static method call worked!\n";
        echo "Result: " . print_r($result, true) . "\n";
    } else {
        echo "ERROR: Class does not exist\n";
    }
} catch (Error $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "EXCEPTION: " . $e->getMessage() . "\n";
}
