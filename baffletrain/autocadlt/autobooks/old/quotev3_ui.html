<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Autodesk Quote Management</title>
    <link rel="stylesheet" href="quotev3_ui.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Autodesk Quote Management</h1>
        </header>
        
        <div class="tabs">
            <button class="tab-btn active" data-tab="create-quote">Create Quote</button>
            <button class="tab-btn" data-tab="view-quotes">View Quotes</button>
        </div>
        
        <div class="tab-content">
            <div id="create-quote" class="tab-pane active">
                <div class="quote-form-container">
                    <div class="form-sidebar">
                        <h3>Quote Sections</h3>
                        <ul class="section-nav">
                            <li class="active" data-section="general-info">General Information</li>
                            <li data-section="agent-info">Agent Information</li>
                            <li data-section="customer-info">Customer Information</li>
                            <li data-section="line-items">Line Items</li>
                            <li data-section="additional-info">Additional Information</li>
                        </ul>
                    </div>
                    <div class="form-content">
                        <form id="quote-form">
                            <div id="general-info" class="form-section active">
                                <h2>General Information</h2>
                                <div class="form-fields"></div>
                                <div class="form-navigation">
                                    <button type="button" class="next-btn">Next</button>
                                </div>
                            </div>
                            
                            <div id="agent-info" class="form-section">
                                <h2>Agent Information</h2>
                                <div class="form-fields"></div>
                                <div class="form-navigation">
                                    <button type="button" class="prev-btn">Previous</button>
                                    <button type="button" class="next-btn">Next</button>
                                </div>
                            </div>
                            
                            <div id="customer-info" class="form-section">
                                <h2>Customer Information</h2>
                                <div class="form-fields"></div>
                                <div class="form-navigation">
                                    <button type="button" class="prev-btn">Previous</button>
                                    <button type="button" class="next-btn">Next</button>
                                </div>
                            </div>
                            
                            <div id="line-items" class="form-section">
                                <h2>Line Items</h2>
                                <div class="line-items-container">
                                    <div class="line-item-actions">
                                        <div class="line-item-type">
                                            <label for="line-item-type">Line Item Type:</label>
                                            <select id="line-item-type">
                                                <option value="new">New</option>
                                                <option value="renewal">Renewal</option>
                                                <option value="switch">Switch</option>
                                                <option value="extension">Extension</option>
                                                <option value="trueup">True-Up</option>
                                            </select>
                                        </div>
                                        <button type="button" id="add-line-item">Add Line Item</button>
                                    </div>
                                    <div id="line-items-list"></div>
                                </div>
                                <div class="form-navigation">
                                    <button type="button" class="prev-btn">Previous</button>
                                    <button type="button" class="next-btn">Next</button>
                                </div>
                            </div>
                            
                            <div id="additional-info" class="form-section">
                                <h2>Additional Information</h2>
                                <div class="form-fields"></div>
                                <div class="form-navigation">
                                    <button type="button" class="prev-btn">Previous</button>
                                    <button type="submit" class="submit-btn">Submit Quote</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div id="view-quotes" class="tab-pane">
                <div class="quotes-filter">
                    <div class="filter-group">
                        <label for="status-filter">Status:</label>
                        <select id="status-filter">
                            <option value="all">All</option>
                            <option value="draft">Draft</option>
                            <option value="quoted">Quoted</option>
                            <option value="expired">Expired</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="search-filter">Search:</label>
                        <input type="text" id="search-filter" placeholder="Search quotes...">
                    </div>
                    <button id="apply-filters">Apply Filters</button>
                </div>
                
                <div class="quotes-table-container">
                    <table id="quotes-table">
                        <thead>
                            <tr>
                                <th>Quote Number</th>
                                <th>Customer</th>
                                <th>Status</th>
                                <th>Created Date</th>
                                <th>Expiration Date</th>
                                <th>Total Amount</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="quotes-table-body">
                            <!-- Quote rows will be populated here -->
                        </tbody>
                    </table>
                </div>
                
                <div class="pagination">
                    <button id="prev-page">Previous</button>
                    <span id="page-info">Page 1 of 1</span>
                    <button id="next-page">Next</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Templates -->
    <template id="line-item-template">
        <div class="line-item">
            <div class="line-item-header">
                <h3>Line Item: <span class="line-item-title"></span></h3>
                <div class="line-item-actions">
                    <button type="button" class="collapse-line-item">Collapse</button>
                    <button type="button" class="remove-line-item">Remove</button>
                </div>
            </div>
            <div class="line-item-content">
                <!-- Line item fields will be populated here -->
            </div>
        </div>
    </template>
    
    <script src="quotev3_ui.js" type="module"></script>
</body>
</html>
