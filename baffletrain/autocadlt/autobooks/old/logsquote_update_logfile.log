[quote_update] [2025-05-29 23:00:26] [autodesk_quote.class.php:515] Starting quote change processing at 2025-05-29 23:00:26
[quote_update] [2025-05-29 23:00:26] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-05-29 23:00:26] [autodesk_quote.class.php:538] Incoming payload: Array
(
    [id] => cfc25ef1-1f4d-4fa7-baa3-009c3ce90757
    [topic] => quote-status
    [event] => changed
    [sender] => Autodesk Quote Status
    [environment] => prd
    [payload] => Array
        (
            [quoteNumber] => Q-730836
            [transactionId] => 5d9710ff-024c-5bf5-abd5-7075a4a2c369
            [quoteStatus] => Expired
            [message] => Quote# Q-730836 status changed to Expired.
            [modifiedAt] => 2025-05-29T23:00:18.002Z
        )

    [publishedAt] => 2025-05-29T23:00:23.000Z
    [csn] => 5103159758
)

[quote_update] [2025-05-29 23:00:26] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-05-29 23:00:26] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-05-29 23:00:26] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-05-29 23:00:26] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-05-29 23:00:26] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-05-29 23:00:26] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-730836', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-730836', quote_status = 'Expired';

[quote_update] [2025-05-29 23:00:26] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-730836', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-730836', quote_status = 'Expired';

[quote_update] [2025-05-29 23:00:26] [autodesk_quote.class.php:613] response: Array
(
    [0] => Array
        (
            [status] => success
            [message] => autodesk_quotes updated successfull
            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-730836', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-730836', quote_status = 'Expired';

            [affected_rows] => 2
        )

)

[quote_update] [2025-05-29 23:00:26] [autodesk_quote.class.php:617] [
    {
        "status": "success",
        "message": "autodesk_quotes updated successfull",
        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-730836', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-730836', quote_status = 'Expired';\n",
        "affected_rows": 2
    }
]
[quote_update] [2025-05-29 23:00:30] [autodesk_quote.class.php:515] Starting quote change processing at 2025-05-29 23:00:30
[quote_update] [2025-05-29 23:00:30] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-05-29 23:00:30] [autodesk_quote.class.php:538] Incoming payload: Array
(
    [id] => 479cdd57-91f1-42f2-acf1-d799ef7547d7
    [topic] => quote-status
    [event] => changed
    [sender] => Autodesk Quote Status
    [environment] => prd
    [payload] => Array
        (
            [quoteNumber] => Q-731411
            [transactionId] => 80aa023a-0cd3-51e1-ba01-62b0ca9127c9
            [quoteStatus] => Expired
            [message] => Quote# Q-731411 status changed to Expired.
            [modifiedAt] => 2025-05-29T23:00:22.446Z
        )

    [publishedAt] => 2025-05-29T23:00:28.000Z
    [csn] => 5103159758
)

[quote_update] [2025-05-29 23:00:30] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-05-29 23:00:30] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-05-29 23:00:30] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-05-29 23:00:30] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-05-29 23:00:30] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-05-29 23:00:30] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-731411', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-731411', quote_status = 'Expired';

[quote_update] [2025-05-29 23:00:30] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-731411', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-731411', quote_status = 'Expired';

[quote_update] [2025-05-29 23:00:30] [autodesk_quote.class.php:613] response: Array
(
    [0] => Array
        (
            [status] => success
            [message] => autodesk_quotes updated successfull
            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-731411', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-731411', quote_status = 'Expired';

            [affected_rows] => 2
        )

)

[quote_update] [2025-05-29 23:00:30] [autodesk_quote.class.php:617] [
    {
        "status": "success",
        "message": "autodesk_quotes updated successfull",
        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-731411', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-731411', quote_status = 'Expired';\n",
        "affected_rows": 2
    }
]
[quote_update] [2025-05-29 23:00:36] [autodesk_quote.class.php:515] Starting quote change processing at 2025-05-29 23:00:36
[quote_update] [2025-05-29 23:00:36] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-05-29 23:00:36] [autodesk_quote.class.php:538] Incoming payload: Array
(
    [id] => c49d9dcb-fe01-4852-bbfc-80dea717bfdf
    [topic] => quote-status
    [event] => changed
    [sender] => Autodesk Quote Status
    [environment] => prd
    [payload] => Array
        (
            [quoteNumber] => Q-732462
            [transactionId] => 7b80d63a-b01d-5e96-8336-b6b043e81cf5
            [quoteStatus] => Expired
            [message] => Quote# Q-732462 status changed to Expired.
            [modifiedAt] => 2025-05-29T23:00:23.812Z
        )

    [publishedAt] => 2025-05-29T23:00:34.000Z
    [csn] => 5103159758
)

[quote_update] [2025-05-29 23:00:36] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-05-29 23:00:36] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-05-29 23:00:36] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-05-29 23:00:36] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-05-29 23:00:36] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-05-29 23:00:36] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-732462', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-732462', quote_status = 'Expired';

[quote_update] [2025-05-29 23:00:36] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-732462', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-732462', quote_status = 'Expired';

[quote_update] [2025-05-29 23:00:36] [autodesk_quote.class.php:613] response: Array
(
    [0] => Array
        (
            [status] => success
            [message] => autodesk_quotes updated successfull
            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-732462', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-732462', quote_status = 'Expired';

            [affected_rows] => 2
        )

)

[quote_update] [2025-05-29 23:00:36] [autodesk_quote.class.php:617] [
    {
        "status": "success",
        "message": "autodesk_quotes updated successfull",
        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-732462', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-732462', quote_status = 'Expired';\n",
        "affected_rows": 2
    }
]
[quote_update] [2025-05-29 23:00:43] [autodesk_quote.class.php:515] Starting quote change processing at 2025-05-29 23:00:43
[quote_update] [2025-05-29 23:00:43] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-05-29 23:00:43] [autodesk_quote.class.php:538] Incoming payload: Array
(
    [id] => 68b7e8b6-dc00-4c46-a600-3463da427b91
    [topic] => quote-status
    [event] => changed
    [sender] => Autodesk Quote Status
    [environment] => prd
    [payload] => Array
        (
            [quoteNumber] => Q-732475
            [transactionId] => 7fe76c49-b29e-5a37-9d0d-0cc34609aeec
            [quoteStatus] => Expired
            [message] => Quote# Q-732475 status changed to Expired.
            [modifiedAt] => 2025-05-29T23:00:30.242Z
        )

    [publishedAt] => 2025-05-29T23:00:41.000Z
    [csn] => 5103159758
)

[quote_update] [2025-05-29 23:00:43] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-05-29 23:00:43] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-05-29 23:00:43] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-05-29 23:00:43] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-05-29 23:00:43] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-05-29 23:00:43] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-732475', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-732475', quote_status = 'Expired';

[quote_update] [2025-05-29 23:00:43] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-732475', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-732475', quote_status = 'Expired';

[quote_update] [2025-05-29 23:00:43] [autodesk_quote.class.php:613] response: Array
(
    [0] => Array
        (
            [status] => success
            [message] => autodesk_quotes updated successfull
            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-732475', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-732475', quote_status = 'Expired';

            [affected_rows] => 2
        )

)

[quote_update] [2025-05-29 23:00:43] [autodesk_quote.class.php:617] [
    {
        "status": "success",
        "message": "autodesk_quotes updated successfull",
        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-732475', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-732475', quote_status = 'Expired';\n",
        "affected_rows": 2
    }
]
