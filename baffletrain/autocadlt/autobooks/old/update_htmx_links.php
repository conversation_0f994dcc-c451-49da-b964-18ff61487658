<?php
/**
 * <PERSON><PERSON><PERSON> to update HTMX links in templates to remove the "get_view/" prefix
 * 
 * This script will scan all template files and update HTMX attributes to remove
 * the "get_view/" prefix from URLs.
 */

// Define the directory to scan
$viewsDir = __DIR__ . '/resources/views';
$componentsDir = __DIR__ . '/resources/components';

// Define the patterns to search for
$patterns = [
    // Match hx-get="APP_ROOT get_view/something"
    '/(hx-get=["\']\s*(?:\{\{\s*APP_ROOT\s*\}\}|<\?=\s*APP_ROOT\s*\?>)\s*get_view\/)/',
    // Match hx-post="APP_ROOT get_view/something"
    '/(hx-post=["\']\s*(?:\{\{\s*APP_ROOT\s*\}\}|<\?=\s*APP_ROOT\s*\?>)\s*get_view\/)/',
    // Match hx-get="get_view/something"
    '/(hx-get=["\']\s*get_view\/)/',
    // Match hx-post="get_view/something"
    '/(hx-post=["\']\s*get_view\/)/',
];

// Define the replacements
$replacements = [
    // Replace with APP_ROOT (without get_view/)
    '$1',
    // Replace with APP_ROOT (without get_view/)
    '$1',
    // Replace with just the attribute (without get_view/)
    '$1',
    // Replace with just the attribute (without get_view/)
    '$1',
];

// Function to scan directory recursively
function scanDirectory($dir, $patterns, $replacements) {
    $files = glob($dir . '/*');
    $count = 0;
    
    foreach ($files as $file) {
        if (is_dir($file)) {
            $count += scanDirectory($file, $patterns, $replacements);
        } else {
            // Only process PHP, edge, and blade files
            if (preg_match('/\.(php|edge\.php|blade\.php)$/', $file)) {
                $content = file_get_contents($file);
                $newContent = preg_replace($patterns, $replacements, $content);
                
                if ($content !== $newContent) {
                    file_put_contents($file, $newContent);
                    echo "Updated: $file\n";
                    $count++;
                }
            }
        }
    }
    
    return $count;
}

// Run the update
echo "Updating HTMX links in templates...\n";
$viewsCount = scanDirectory($viewsDir, $patterns, $replacements);
$componentsCount = scanDirectory($componentsDir, $patterns, $replacements);
$totalCount = $viewsCount + $componentsCount;

echo "Update complete. Modified $totalCount files.\n";
echo "- Views: $viewsCount\n";
echo "- Components: $componentsCount\n";
