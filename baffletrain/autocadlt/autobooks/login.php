<?php
use system\users;
use edge\Edge;
require_once('system/startup_sequence.php');
$error = '';


if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user = new users();
    if ($user->login($_POST['username'], $_POST['password'])) {
        header('Location: ' . APP_ROOT);
        exit;
    } else {
        $error = 'Invalid username or password';
    }
}
?>
<?php edge::render('layout-blank',['view' => 'login']); ?>