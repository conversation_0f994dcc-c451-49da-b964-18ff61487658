Header always set Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval'; script-src   'self' 'unsafe-inline' 'unsafe-eval' https://unpkg.com/htmx-ext-class-tools@2.0.1/class-tools.js https://cdn.jsdelivr.net https://cdn.tailwindcss.com/ https://unpkg.com/htmx.org@2.0.3 https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' 'unsafe-eval' https://rsms.me https://cdnjs.cloudflare.com; object-src 'none'; base-uri 'self' 'unsafe-inline' 'unsafe-eval'; connect-src 'self' 'unsafe-inline' 'unsafe-eval'; font-src 'self' 'unsafe-inline' 'unsafe-eval' https://rsms.me; frame-src 'self' 'unsafe-inline' 'unsafe-eval'; img-src 'self' 'unsafe-inline' 'unsafe-eval' https://tailwindui.com; manifest-src 'self' 'unsafe-inline' 'unsafe-eval'; media-src 'self' 'unsafe-inline' 'unsafe-eval'; worker-src 'none';



RewriteEngine On
# Set RewriteBase to the application directory
RewriteBase /baffletrain/autocadlt/autobooks/

# Prevent direct access to system/ folder - internal rewrite to index.php
# Use R=200 to force a 200 response instead of redirect
RewriteRule ^system/?(.*)$ index.php [QSA,L,E=REDIRECT_STATUS:200]

# Skip rewrite for image files and favicons
RewriteRule \.(jpg|jpeg|png|gif|ico|svg|webp|js|css)$ - [L]

# Rewrite all other requests to index.php, preserving the original request URI
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]
