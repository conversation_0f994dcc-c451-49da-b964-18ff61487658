<?php

namespace autodesk_api;

class autodesk_subscriptions
{
    private autodesk_api_interface $api;
    private autodesk_subscription $subscription;
    public autodesk_authenticator $auth;
    public $debugLog;
    public $subscription_column_mapping;

    public function __construct($api, $auth)
    {
        $this->api = $api;
        $this->auth = $auth;
        $this->subscription = new autodesk_subscription($api);
        $this->subscription_column_mapping = autodesk_subscription::$subscription_column_mapping;
    }

    public function api_get_expiring($time = null)
    {
        $date = new \DateTime();
        $now = $date->format('Y-m-d');
        $when = $date->modify('+29 day')->format('Y-m-d');
        $criteria["filter[endDate]"] = "{$now}..{$when}";
        //print_rr($criteria, 'criteria');
        return $this->api->search_autodesk_subscriptions($criteria);
    }

    public function search($criteria)
    {
        return $this->api->search_autodesk_subscriptions($criteria);
    }

    private function database_get($db_data = [], $criteria = [], $get_distinct = false)    {
        if (!is_array($criteria)) {
            throw new \Exception('Expected $criteria to be an array.');
        }

        if (count($db_data) == 0) $db_data = [
            'table_id' => 'subscriptions',
            'db_table' => 'subs',
            'columns' => []
        ];


        $table_data_schema = [
            "subs" => [
                "extra_fields" => [
                    "enddatediff" => [
                        "sql" => "CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END AS subs_enddatediff",
                    ]
                ],
                'query' => "FROM autodesk_subscriptions subs"
            ],
            "soldto" => [
                'query' => "LEFT JOIN autodesk_accounts soldto ON subs.soldTo_id = soldto.id"
            ],
            'lastquote' => [
                'extra_fields' => [
                    "qitem_quote_id" => [
                        "sql" => "latest_qitem.quote_id AS qitem_quote_id",
                    ],
                    "lastquote_quote_status" => [
                        "sql" => "latest_quote.quote_status AS lastquote_quote_status",
                    ],
                    "lastquote_quote_number" => [
                        "sql" => "latest_quote.quote_number AS lastquote_quote_number",
                    ],
                    "lastquote_quoted_date" => [
                        "sql" => "latest_quote.quoted_date AS lastquote_quoted_date",
                    ],
                    "qitem_id" => [
                        "sql" => "latest_qitem.id AS qitem_id",
                    ]

                ],
                'query' => "LEFT JOIN (
                            SELECT
                        qi.subscription_id,
                        q.id AS quote_id,
                        q.quote_status,
                        q.quote_number,
                        q.quoted_date,
                        qi.id AS qitem_id
                    FROM
                        autodesk_quote_line_items qi
                    JOIN autodesk_quotes q ON q.id = qi.quote_id
                    WHERE
                        qi.subscription_id IS NOT NULL
                        AND q.quote_status NOT IN ('Expired', 'Cancelled')
                    ORDER BY
                        qi.subscription_id, q.quoted_date DESC, qi.id DESc
                ) AS lastquote ON subs.subscriptionId = lastquote.subscription_id
                LEFT JOIN autodesk_quote_line_items latest_qitem ON latest_qitem.id = lastquote.qitem_id
                LEFT JOIN autodesk_quotes latest_quote ON latest_quote.id = lastquote.quote_id"
            ],
            "endcust" => [
                'query' => "LEFT JOIN autodesk_accounts endcust ON subs.endCustomer_id = endcust.id"
            ],
            "solpro" => [
                'query' => "LEFT JOIN autodesk_accounts solpro ON subs.solutionProvider_id = solpro.id"
            ],
            "resell" => [
                'query' => "LEFT JOIN autodesk_accounts resell ON subs.nurtureReseller_id = resell.id"
            ],
            "hist" => [
                'query' => "LEFT JOIN autodesk_history hist ON subs.last_email_history_id = hist.id"
            ],
        ];

        $default_criteria = [
            "search_columns" => ["subs.subscriptionReferenceNumber", "soldto.account_csn", "soldto.name", "solpro.account_csn", "solpro.name", "solpro.address1", "solpro.address2", "solpro.address3", "solpro.city", "solpro.stateProvince", "solpro.postalCode", "solpro.country", "solpro.stateProvinceCode", "solpro.countryCode", "resell.account_csn", "resell.name", "endcust.account_csn", "endcust.name", "endcust.address1", "endcust.address2", "endcust.address3", "endcust.city", "endcust.stateProvince", "endcust.postalCode", "endcust.country", "endcust.primaryAdminFirstName", "endcust.primaryAdminLastName", "endcust.primaryAdminEmail", "endcust.teamId", "endcust.teamName", "endcust.first", "endcust.last", "endcust.email"]
        ];
        $query = build_select_query($db_data, $table_data_schema, array_merge($default_criteria, $criteria), $get_distinct);

        print_rr(i: $query, l: 'finalq', fl: true);
        if ($query['column_count'] == 1) return tcs_db_query_col($query['text']);
        return tcs_db_query($query['text']);
    }

    public function get_all($db_data = [], $criteria = [])    {
        if (!is_array($criteria)) {
            throw new \Exception('Expected $criteria to be an array.');
        }
        return $this->database_get($db_data, $criteria);
    }

    public function get_distinct($db_data = [], $criteria = [])
    {
        if (!is_array($criteria)) {
            throw new \Exception('Expected $criteria to be an array.');
        }
        return $this->database_get($db_data, $criteria, true);
    }

    public function get_renewable()
    {
        $columns = [
            'endcust_name',
            'endcust_primary_admin_email',
            'endcust_primary_admin_first_name',
            'endcust_primary_admin_last_name',
            'subs_id',
            'subs_offeringName',
            'subs_status',
            'subs_opportunityNumber',
            'subs_subscriptionReferenceNumber',
            'subs_quantity',
            'subs_term',
            'subs_enddatediff',
            'subs_startDate',
            'subs_endDate',
            'hist_date',
            'hist_triggered_by',
            'subs_tcs_unsubscribe'
        ];

        $criteria = [
            "where" => "(DATEDIFF(subs.endDate, NOW()) BETWEEN -29 AND 89)"
        ];
        return $this->database_get($columns, $criteria);
    }

    public function get_last_email_sent($id)
    {
        $query = "select * from autodesk_subscription_history where subscription_id = :id order by date_sent desc limit 1";
        try {
            $result_array = tcs_db_query($query, [":id" => $id]);
        } catch (\Exception $e) {
            return false;
        }
        return $result_array[0];
    }

    // id Primary 	int(11) 			No 	None 		AUTO_INCREMENT 	Change Change 	Drop Drop
    // 2 	subscription_id 	int(11) 			No 	None 			Change Change 	Drop Drop
    // 3 	date_sent 	date 			No 	None 			Change Change 	Drop Drop
    // 4 	triggered_by 	varchar(10) 	utf8_general_ci 		No 	None 			Change Change 	Drop Drop
    // 5 	result

    public function send_email($to, $from, $subject, $email_body)   {
        // Send the email
        $headers = "From: {$from}\r\n";
      //  $headers .= "Bcc: <EMAIL>\r\n";
        $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
        if (mail($to, $subject, $email_body, $headers)) {
            echo "Sent, $to";
        } else {
            echo "Failed";
        }
    }

    public function replace_placeholders($email, $sub)  {
        preg_match_all('/{{\s*([^,}]+)(?:,(.+?))?\s*}}/', $email, $matches, PREG_SET_ORDER);
        foreach ($matches as $match) {
            //echo "checking for {$match[0]} with key {$match[1]}";
            $placeholder = $match[0];
            $key = $match[1];
            $expression = isset($match[2]) ? $match[2] : null;
            if (array_key_exists($key, $sub)) {
                if ($expression) {
                    $conditions = explode(',', $expression);
                    foreach ($conditions as $condition) {
                        [$condition_key, $replacement] = explode(':', $condition);
                        $condition_key = trim(str_replace(["'", '"'], '', $condition_key));
                        $replacement = trim(str_replace(["'", '"'], '', $replacement));
                        if (strtolower($sub[$key]) === strtolower($condition_key)) {
                            $email = str_replace($placeholder, $replacement, $email);
                            break;
                        }
                    }
                } else {
                    $email = str_replace($placeholder, $sub[$key], $email);
                }
            } else {
                $email = str_replace($placeholder, '', $email);
            }
        }
        return $email;
    }

    public function send_reminder_email($sub, $rule = "Manual", $to = false)
    {
        // Fetch the email template content
        $file_path = FS_APP_ROOT . 'resources/views/subscriptions/email_history/reminder_email.view.php';
        $email_content = file_get_contents($file_path);

        if ($email_content === false) {
            throw new \Exception("Failed to load email template from {$file_path}");
        }
        $email_content = $this->replace_placeholders($email_content, $sub);
        // Split the content by lines
        $lines = explode("\n", $email_content);
        // The subject is the first line, and the email body is the rest
        $from = trim($lines[0]);
        //echo $from;
        $subject = trim($lines[1]);
        $email_body = implode("\n", array_slice($lines, 2));
        $result = "sent";
        if ($to) {
            $result = $this->send_email($to, $from ?? '<EMAIL>', $subject, $email_body);
        } else {
            $to = $sub['endcust_primaryAdminEmail'];
            $result = $this->send_email($to, $from ?? '<EMAIL>', $subject, $email_body);
        }
        //$to = $sub['email'];
        $query = "INSERT INTO `autodesk_subscription_history`(
                `id`,
                `subscription_id`,
                `subscription_reference_number`,
                `media`,
                `message`,
                `user_id`,
                `date`,
                `triggered_by`,
                `result`
            )
            VALUES(
                NULL,
                '{$sub['subs_id']}',
                '{$sub['subs_subscriptionReferenceNumber']}',
                'E-Mail',
                'Automatic E-Mail sent by Autobooks',
                1,
                NOW(),
                '{$rule}',
                '{$result}')
        ";
        tep_db_query($query);
        $insert_id = tep_db_insert_id();
        tep_db_query("UPDATE `autodesk_subscriptions` SET `last_email_history_id` = '{$insert_id}' WHERE `autodesk_subscriptions`.`id` = {$sub['subs_id']}");
        return $result;
    }

    //public function get_all() {
    //
    //}get_subscriptions_from_api()

    //      "id":"d54ddc7f022695781865b01828722584","status":"processing","password":"Nzg2ZTVhNjQtNzdjYy00MDNhLTlhNmItM2U5OTliZmI4NWJj"}
    public function get_from_api()  { /* Configuration*/
        $enc_file_path = FS_DOC_ROOT . '/feeds/subscriptions.csv.zip.enc';
        $csv_file_path = FS_DOC_ROOT . '/feeds/subscriptions.csv'; /* Define the path where you want to save the CSV file*/
        try {
            $get_subscriptions = $this->api->get_autodesk_subscriptions();
            if ($get_subscriptions['status'] == 'fail') {
                return ['status' => 'fail', 'failing' => 'get_autodesk_subscriptions', 'response' => $get_subscriptions, 'log' => $this->debugLog];
            }
            //print_rr($get_subscriptions['response'];
            $response = $get_subscriptions['body'];
            $this->database_update_api_export_data($response);
        } catch (\Exception $e) {
            $response = $e->getMessage();
            return ['status' => 'fail', 'response' => $response, 'log' => $this->debugLog];
        }

        return ["status" => "Requested, it's the webhook's problem now", 'log' => $this->debugLog];
    }

    public function download_autodesk_subscription_export($file_uri)  { /* Configuration*/
        $enc_file_path = FS_DOC_ROOT . 'feeds/subscriptions.csv.zip.enc'; /* Define the path where you want to save the CSV file*/
        $csv_file_path = FS_DOC_ROOT . 'feeds/subscriptions.csv';
        $download = false;/* Check if the file exists and is less than a day old*/
        autodesk_api::log_message("Looking for file: " . $csv_file_path . "");
        if (file_exists($csv_file_path)) {
            $file_age = time() - filemtime($csv_file_path);
            autodesk_api::log_message("file found: " . $csv_file_path . " - age: " . $file_age);
            if ($file_age > 86400) $download = true;
        } else {
            $download = true;
        }
        autodesk_api::log_message("Download: " . $download);
        if ($download) {
            try {
                $input_file = file_get_contents($file_uri);                            /* Download the CSV file and save it to the specified path                */
                file_put_contents($enc_file_path, $input_file);
            } catch (\Exception $e) {
                $response = $e->getMessage();
                return ['status' => 'fail', 'response' => $response];
            }
        }
        try {
            $info = $this->database_get_api_export_data();
            //return ['status' => 'success', 'response' => print_r($info, true)];
            $decrypted_file = $this->auth->decryptFile($enc_file_path, $csv_file_path, $info->password);
        } catch (\Exception $e) {
            $response = $e->getMessage();
            return ['status' => 'fail', 'response' => $response];
        }
        return ['status' => 'success', 'response' => $decrypted_file];
    }

    private function database_update_api_export_data($body)
    {
        $jsonData = json_encode($body);

        // Check if JSON encoding was successful
        if ($jsonData === false) {
            // Log the error or throw an \Exception
            error_log("Failed to encode body to JSON: " . json_last_error_msg());
            return false;
        }

        $query_sql = "INSERT INTO autodesk_storage (`autodesk_storage_key`, `autodesk_storage_data`)
                      VALUES (:autodesk_storage_key, :autodesk_storage_data)
                      ON DUPLICATE KEY UPDATE `autodesk_storage_data` = :autodesk_storage_data_update";

        try {
            return tep_db_query($query_sql, null, [
                ":autodesk_storage_key" => 'subscription_export_data',
                ":autodesk_storage_data" => $jsonData,
                ":autodesk_storage_data_update" => $jsonData
            ]);
        } catch (\Exception $e) {
            // Handle or log error as needed
            error_log("Database query failed: " . $e->getMessage());
            return false;
        }
    }

    public function database_get_api_export_data()
    {
        $query_sql = "select * from autodesk_storage where autodesk_storage_key = 'subscription_export_data' limit 1";
        $value = tcs_db_query($query_sql);
        return json_decode($value[0]['autodesk_storage_data']);
    }
}
