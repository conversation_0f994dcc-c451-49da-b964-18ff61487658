<?php
namespace autodesk_api;

class autodesk_customer_success {
    private autodesk_api_interface $api;
    public $debugLog;

    public function __construct($api) {
        $this->api = $api;
    }

    public function get_autodesk_promotions() { /* Configuration*/
        $csv_file_path = DIR_FS_CATALOG . '/feeds/autodesk_promotions.csv'; /* Define the path where you want to save the CSV file        *//* Check if the file exists and is less than a day old*/
        if (file_exists($csv_file_path)) {
            $file_age = time() - filemtime($csv_file_path);
            if ($file_age < 86400) return ['status' => 'Nothing to do', 'response' => 'File is less than a day old.'];
        }
        $get_catalog =  $this->api->get_autodesk_promotions();
        if ($get_catalog['status'] == 'fail') {
            return $get_catalog;
        } else {
            $response = $get_catalog['response'];
        }
        $response_headers = $response['headers'];
        if (is_string($response_headers['Location'][0])) {
            $input_file = file_get_contents($response_headers['Location'][0]);                            /* Download the CSV file and save it to the specified path                */
            file_put_contents($csv_file_path, $input_file);
            return ['status' => 'success', 'response' => $response];
        }
    }

    public function get_opportunity($opportunityNumber = null, $endCustomerCsn = null) {
        return $this->api->get_opportunity($opportunityNumber, $endCustomerCsn);
    }
}
?>