<?php

namespace autodesk_api;
use http\Exception;
use system\users;
use product_attributes\product_attributes;
use const http\Client\Curl\AUTH_ANY;

class website_quote {
    private autodesk_api_interface $api;
    public $debugLog;
    public $currency = "GBP";
    public $account = ['accountCsn' => ADWS_CSN];
    public $debug = false;
    public $quote_customer = [];
    public $quote_products = [];
    public $quote_data;
    public $quote_status;
    public $lineItems = [];
    public $opportunityNumber;

    private autodesk_products $products;
    private autodesk_customers $customers;


    public function __construct($api, $products, $customers) {

        $this->api = $api;
        $this->products = $products;
        $this->customers = $customers;
    }


//
    public function create($order_id, $include_all = false, $quote_products_id = false) {

        print_rr("create");
        $prompt_for_data = false;
        $sql_query = "select customers_id, customers_name, customers_company, customers_street_address, customers_suburb, customers_city, customers_postcode, customers_state, customers_country, customers_telephone, customers_email_address, customers_address_format_id, delivery_name, delivery_company, delivery_street_address, delivery_suburb, delivery_city, delivery_postcode, delivery_state, delivery_country, delivery_address_format_id, billing_name, billing_company, billing_street_address, billing_suburb, billing_city, billing_postcode, billing_state, billing_country, billing_address_format_id, payment_method, cc_type, cc_owner, cc_number, cc_expires, currency, currency_value, date_purchased, orders_status, last_modified from orders where orders_id = '" . (int)$order_id . "'";
        print_rr($sql_query, 'create quote qry 1');
        $order_query = tep_db_query($sql_query);
        $order = tep_db_fetch_array($order_query);
        $sql_query = "select customers_firstname, customers_lastname from customers where customers_id = '" . $order['customers_id'] . "'";
        print_rr($sql_query, 'create quote qry 2');
        $customers_query = tep_db_query($sql_query);
        $customers = tep_db_fetch_array($customers_query);

        $index = 0;$order_products = [];
        $sql_query = "select orders_products_id, products_id, products_name, products_model, products_price, products_tax, products_quantity,manufacturers_id, final_price from orders_products where orders_id = '" . (int)$order_id . "'";
        print_rr($sql_query, 'create quote qry 3');
        $orders_products_query = tep_db_query($sql_query);
        while ($orders_products = tep_db_fetch_array($orders_products_query)) {
            $order_products[$index] = array(
                'qty' => $orders_products['products_quantity'],
                'id' => $orders_products['products_id'],
                'name' => $orders_products['products_name'],
                'manufacturer' => $orders_products['manufacturers_id'],
                'model' => $orders_products['products_model'],
                'tax' => $orders_products['products_tax'],
                'price' => $orders_products['products_price'],
                'final_price' => $orders_products['final_price']
            );
            $sql_query = "select options_id, options_values_id, products_options, products_options_values, options_values_price, price_prefix from orders_products_attributes where orders_id = '" . (int)$order_id . "' and orders_products_id = '" . (int)$orders_products['orders_products_id'] . "'";
            print_rr($sql_query, 'create quote qry 4');
            $attributes_query = tep_db_query($sql_query);
            $subindex = 0;
            if (tep_db_num_rows($attributes_query)) {
                while ($attributes = tep_db_fetch_array($attributes_query)) {
                    $order_products[$index]['attributes'][$subindex] = array(
                        'option_id' => $attributes['options_id'],
                        'value_id' => $attributes['options_values_id'],
                        'option' => $attributes['products_options'],
                        'value' => $attributes['products_options_values'],
                        'prefix' => $attributes['price_prefix'],
                        'price' => $attributes['options_values_price']
                    );
                    $subindex++;
                }
            }
            $index++;
        }

        $order_customer = array(
            'id' => $order['customers_id'],
            'name' => $order['customers_name'],
            'first_name' => $customers['customers_firstname'],
            'last_name' => $customers['customers_lastname'],
            'company' => $order['customers_company'],
            'street_address' => $order['customers_street_address'],
            'suburb' => $order['customers_suburb'],
            'city' => $order['customers_city'],
            'postcode' => $order['customers_postcode'],
            'state' => $order['customers_state'],
            'country' => $order['customers_country'],
            'format_id' => $order['customers_address_format_id'],
            'telephone' => $order['customers_telephone'],
            'email_address' => $order['customers_email_address']
        );

        $quote_customer = $order_customer;
        $quote_products = $order_products;
        $orders_autodesk = $this->database_get_orders_autodesk($order_id);
        if (!empty($orders_autodesk))  $this->opportunityNumber = $orders_autodesk['opportunityNumber'];/*$quote_products_extra = $this->database_get_product_extra($orders_id);*/
        print_rr(i: $quote_products ?? '', l: "quote_products");
        $custData = $this->process_customer_data($quote_customer, $include_all);
        foreach ($quote_products as $key => $product) {
            $variation = $this->get_order_variations($product['id'], $product);
            $quote_products[$key]['autodesk_link'] = $variation['autodesk_link'];
        }
        print_rr($variation, 'variation');
        print_rr($quote_products, 'quote_products');
        $prompt = $this->process_product_data($quote_products, $include_all, null, $order_id);
        /*if ($productData == false ) {
            $prompt_for_data = true;
        }*/
        print_rr(i: $custData, l: "custData");
        $this->quote_customer = $custData;
        //print_rr(i: $productData, l: "productData");
        //$this->quote_products = $productData;
        print_rr(i: $this->quote_products, l: "create end");
       /** $this->quoteData = [
            'customer' => $this->quote_customer,

        ]**/
        if (isset($this->quote_products['error'])){
            $prompt_for_data = true;
        }
        return $prompt_for_data;
    }

    public function get($quote_id) {
        return $this->database_get_quote($quote_id);
    }

    public function view($quote = null, $orders_id = null) {
        if ($orders_id) {
            $quote_id = $this->database_get_quote(orders_id: $orders_id);
        }
        if (is_string($quote)) {
            $quote_id = $this->database_get_quote($quote['id']);
        }
        $quote = $this->database_get_quote($quote['id']);
        if ($quote['quoteStatus'] == 'Not sent to Autodesk' || $quote['quoteStatus'] == 'Not sent') {
            return $this->create($orders_id);
            // print_rr($quote,$quotes_id);
        } else if ($quote_id != '') {
            $quote_result = $this->api->quotes_view($quote_id);
            $quote_data = $quote_result['body']['results'][0];
            // print_rr($quote_data);
        } else {
            // print_rr($quote,$quotes_id);
            return 'Invalid Quote Number or order id';
        }
    }
    public function update($orderAction, $orders_id, $quote_products_id, $data) {

        $database = $this->database_update_product_extra(orders_id: $orders_id, products_id: $quote_products_id, extras: $data,);
        $out = $this->get_extra($orders_id, $quote_products_id, true);
        return $out['lineItems'][0];
    }
    private function database_get_orderAction($orders_id, $quote_products_id) {
        $query_sql = "SELECT * FROM orders_products_autodesk WHERE orders_id = :orders_id and products_id = :products_id";
        $result = tep_db_query($query_sql, null, [":orders_id" => $orders_id, ":products_id" => $quote_products_id]);
        if (tep_db_num_rows($result) > 0) {
            $row = tep_db_fetch_array($result);
            return $row['orderAction'];
        }
        return null;
    }

    private function database_get_orders_autodesk($orders_id) {
        $query_sql = "SELECT opportunityNumber FROM orders_autodesk WHERE orders_id = :orders_id LIMIT 1";
        $result = tep_db_query($query_sql, null, [":orders_id" => $orders_id]);
        return tep_db_fetch_array($result);
    }
    private function database_get_order_variations($orders_id) {
        $query_sql = "SELECT * FROM orders_products_variations WHERE orders_id = :orders_id";
        $result = tep_db_query($query_sql, null, [":orders_id" => $orders_id]);
        return tep_db_fetch_all($result);
    }

    public function database_addquote_from_json($order, $quote) {
        $query_sql = "SELECT * FROM autodesk_orders WHERE transactionId = :transaction_id LIMIT 1";
        $result = tep_db_query($query_sql, null, [":transaction_id" => $quote['transactionId']]);
        $params = [':customers_id' =>  '', ':orders_id' => '', ':quoteStatus' =>  $quote['quoteStatus'], ':transactionId' => $quote['transactionId'], ':quoteNumber' =>  $quote['quoteNumber']];
        $set_sql = 'SET customers_id = :customers_id,orders_id = :orders_id,quoteStatus = :quoteStatus,transactionId = :transactionId,quoteNumber = :quoteNumber';
        $where_sql = ' WHERE orders_id = :orders_id';
        if (!tep_db_num_rows($result)) {
            $query_sql = 'INSERT INTO autodesk_orders' . ' ' .  $set_sql;
        } else {
            $query_sql = 'UPDATE autodesk_orders' . ' ' . $set_sql . ' ' . $where_sql;
        }
        $response = tep_db_query($query_sql, null, $params);
        return ['response' => 'Updated quote', 'query_sql' => $query_sql];
    }
    public function database_addquote_from_transaction_id($orders_id, $customers_id, $quote) {
        $query_sql = "SELECT * FROM autodesk_orders WHERE transactionId = :transaction_id LIMIT 1";
        $result = tep_db_query($query_sql, null, [":transaction_id" => $quote['transactionId']]);
        if (!tep_db_num_rows($result)) {
            $query_sql =
                'INSERT INTO autodesk_orders
             SET `customers_id` = :customers_id,
             `orders_id` = :orders_id,
             `quoteStatus` = :quoteStatus,
             `transactionId` = :transactionId,
             `quoteNumber` = :quoteNumber;
            ';
            $params = [':customers_id' =>  $customers_id, ':orders_id' => $orders_id, ':quoteStatus' =>  $quote['quoteStatus'], ':transactionId' => $quote['transactionId'], ':quoteNumber' => $quote['quoteNumber']];
            $response = tep_db_query($query_sql, null, $params);
            return ['response' => $response, 'query_sql' => $query_sql];
        }
    }
    public function database_addquote(int|null $orders_id = null, int|null $customers_id = null, array|object|null $order = null, array|object|null $quote = null) {
        if ($customers_id == null) $customers_id = $order->customer['id'];
        if ($orders_id == null) $orders_id = $order->info['id'];
        print_rr($quote, 'orders_ideee');
        if ($quote == null) return ['response' => 'Error: No quote supplied', 'query_sql' => ''];
        if ($orders_id) $where_sql = 'orders_id = :orders_id';
        $def_select_params = [
            'orders_id' => $orders_id,
            'transactionId' => $quote['transactionId'],
            'quoteNumber' =>  $quote['quoteNumber']
        ];


        $select_params = [];
        $select_sql_params = [];
        foreach ($def_select_params as $key => $value) {
            if ($value != null && $value != '' && $value != 'NA') {
                $select_sql_params[] = "{$key} = :{$key}";
                $select_params[":{$key}"] = $value;
            }
        }
        $select_sql = "SELECT * FROM autodesk_orders WHERE " . implode(' OR ', $select_sql_params) . " LIMIT 1";
        $result = tep_db_query($select_sql, null,  $select_params);

         $set_params = [
            'customers_id' =>  (int)$customers_id,
            'orders_id' => (int)$orders_id,
            'quoteStatus' =>  $quote['quoteStatus'],
            'transactionId' => $quote['transactionId'],
            'quoteNumber' =>   $quote['quoteNumber']
        ];
        $query_params = $query_params = [];
        foreach ($def_select_params as $key => $value) {
            if ($value != null && $value != '' && $value != 'NA') {
                $where_params[] = "{$key} = :where_{$key}";
                $query_params[":where_{$key}"] = $value;
            }
        }
        foreach ($set_params as $key => $value) {
            $set_sql_params[] = "{$key} = :{$key}";
            $query_params[":{$key}"] = $value;

        }
        if (count($set_params) == 0 || count($query_params) == 0) return ['response' => 'Error: No ', 'query_sql' => ''];
        $set_params[] = "`modifiedAt` = NOW()";
        $set_sql = ' SET ' . implode(', ',$set_sql_params);
        $where_sql = ' WHERE ' . implode( ' OR ',$where_params);

        if (!tep_db_num_rows($result)) {
            $query_sql = 'INSERT INTO autodesk_orders' . $set_sql;
        } else {
            $query_sql = 'UPDATE autodesk_orders' . ' ' . $set_sql . $where_sql;
        }
        print_rr($query_sql,'final addquote query');
        print_rr($query_params,'final addquote query_params');
        $response = tep_db_query($query_sql, null, $query_params);
        return ['response' => 'Updated quote', 'query_sql' => $query_sql, 'params' => $query_params,'select'];
    }


    public function database_get_quote($quote_id = null, $orders_id = null) {
        if ($orders_id) $where_sql = ' orders_id = :orders_id';
        elseif ($quote_id) $where_sql = ' quote_id = :quote_id';
        else return 'error: No quote_id or orders_id supplied';
        return tcs_db_query("SELECT * FROM autodesk_orders WHERE $where_sql LIMIT 1", [":orders_id" => $orders_id, ":quote_id" => $quote_id]);
    }
    public function customers_update_quote($orders_id, $quote_products_id, $extras) {
        $query = tep_db_query("select * from orders_products_autodesk where orders_id = :orders_id and products_id = :products_id", null, [":orders_id" => $orders_id, ":products_id" => $quote_products_id]);
        if (tep_db_num_rows($query) < 1)  return ['response' => 'nothing to do'];
        $query_sql = "SELECT * FROM orders_products_autodesk WHERE orders_id = :orders_id and products_id = :products_id";
        $result = tep_db_query($query_sql, null, [":orders_id" => $orders_id, ":products_id" => $quote_products_id]);
        $params = [":orders_id" => $orders_id, ":products_id" => $quote_products_id];
        $set_sql = '';
        foreach ($extras as $key => $extra) {
            $params[':' . $key] = $extra;
            if ($set_sql != '') $set_sql .= ', ';
            $set_sql .= $key . ' = ' . ':' . $key;
        }
        if (!tep_db_num_rows($result)) {
            $query_sql = 'INSERT INTO orders_products_autodesk' . ' SET orders_id = :orders_id, products_id = :products_id, ' .  $set_sql;
        } else {
            $query_sql = 'UPDATE orders_products_autodesk' .  ' SET ' . $set_sql . ' WHERE orders_id = :orders_id and products_id = :products_id';
        }
        $response = tep_db_query($query_sql, null, $params);
        ////print_rr($query_sql, 'database_update_product_extra');
        return ['response' => 'Updated quote', 'query_sql' => $query_sql];
    }

    public function create_from_checkout($orders_id, $customers_id, $order) {
        $this->quote_status = 'Not sent';
        $quote = $this->create($orders_id);
        $database_response = $this->database_addquote($orders_id, $customers_id, $order, $quote);
        return $database_response;
    }

    private function get_order_variations($quote_products_id, $product) {
        print_rr($product,'proddduct');
        $attribute_array = [];
        foreach ($product['attributes'] as $attribute) {
            $attribute_array[$attribute['option_id']] = $attribute['value_id'];
        }
        print_rr($attribute_array,'attribute_array');
        $attributes = new product_attributes($quote_products_id, 1, $attribute_array);
        $variation = $attributes->get_current_selected_variation();
        print_rr($variation,'variation');
        print_rr($attributes,'variations');
        if (empty($variation)) return false;
        return $variation;
    }

    public function send($orders_id) {
        print_rr($orders_id);
        $prompt = $this->create($orders_id);
        print_rr($this->quote_data,'final_quote_data');
        if ($prompt) return ['response' => 'prompt', 'debug' => $this->api->debugLog];

        try {
            $quoteData['currency'] = "GBP"; // Assuming the currency is passed in the customer array
            $quoteData['agentAccount'] = ['accountCsn' => ADWS_CSN];
            $quoteData = array_merge($quoteData, $this->quote_customer);
            $this->quote_data = array_merge($quoteData,  $this->quote_products);

            $response = $this->api->send_quote($this->quote_data);
            $quote = $response['body'];
            if ($quote['status'] == 'fail') return $this->api->debugLog;
            $status_response = $this->api->quotes_status($quote['transactionId']);
            $quote_status = $status_response['body'];
        } catch (Exception $e) {
            $this->api->debugLog['log']['error'] = $e->getMessage();
            return $this->api->debugLog;
        }
        if ($quote_status['status'] == 'fail') return $this->api->debugLog;

        $database_response = $this->database_addquote($orders_id, null, null, $quote_status);
        $this->api->debugLog['log']['database_query'] = $database_response['query_sql'];
        $this->api->debugLog['log']['database_response'] = $database_response['response'];
        return ['response' => $quote_status, 'debug' => $this->api->debugLog];
    }
    public function finalize($quote_number) {
        return $this->api->quotes_finalize($quote_number);
    }


    private function process_customer_data($quote_customer, $include_all = false)
    {
        $endCustomer = []; /* search for customer account */
        $cust_search = $this->api->search_autodesk_customers(
            [
                'contactEmail' => $quote_customer['email_address']
            ]
        );

        $this->api->debugLog['log']['customerSearch'] = $cust_search;

        $resultsExist = false;

        if ($resultsExist) {
            $endCustomer['accountCsn'] = $cust_search['accountCsn'];
        }

        $endCustomer = array_merge(
            $endCustomer, [
                'isIndividual' => empty($quote_customer['company']), // ? true : false,
                'addressLine1' => $quote_customer['street_address'],
                'addressLine2' => $quote_customer['suburb'],
                'city' => $quote_customer['city'],
                'stateProvinceCode' => $quote_customer['state'],
                'postalCode' => $quote_customer['postcode'],
                'countryCode' => is_string($quote_customer['country']) ? "GB" : $quote_customer['country']['iso_code_2']
            ]
        );

        /* Only add 'name' if $quote_customer['company'] is not empty */
        if (!empty($quote_customer['company'])) {
            $endCustomer['name'] = $quote_customer['company'];
        }

        return [
            'agentContact' => [
                'email' => '<EMAIL>'
            ],
            'endCustomer' => $endCustomer,
            'quoteContact' => [
                'email' => $quote_customer['email_address'],
                'firstName' => $quote_customer['first_name'],
                'lastName' => $quote_customer['last_name'],
                'phone' => $quote_customer['telephone'],
                'preferredLanguage' => 'en'
            ]
        ];
    }

    private function process_product_data($quote_products, $include_all = false, $quote_products_id = null, $orders_id = null) {
        $lineItems = [];
        print_rr($quote_products,'process_product_data');
        foreach ($quote_products as $product) {
            if ($quote_products_id != null && $quote_products_id != $product['id']) {
                continue;
            }
            $autodesk_product = $this->products->get_autodesk_product_from_catalog($product['id'], $product['autodesk_link']);
            if (empty($autodesk_product)) {
                return array('error' => 'Product not found');
            }
            $autodesk_extras = $this->database_get_product_extra($orders_id, $product['id']);

            $orderAction = $autodesk_extras['orderAction'] ?? $autodesk_product['orderAction'];
            if ($orders_id != null) {
                $db_order_action = $this->database_get_orderAction($orders_id, $product['id']);
                if ($db_order_action != null) $orderAction = $db_order_action;
            }
            $orderAction_has_dda = false;
            if (str_ends_with($orderAction, '_DDA')) {
                $orderAction = str_replace('_DDA', '', $orderAction);
                $orderAction_has_dda = true;
            }
            $lineItems = [];
            if (is_array($autodesk_extras)) $autodesk_product = array_merge($autodesk_product, $autodesk_extras);
            $simpleAction = strtolower(str_replace(['_', '-'], '', $orderAction));
            $lineitems_extra = [];
            $prompt_for_data = false;
            switch ($simpleAction) {
                case 'new':
                    break;
                case 'renewal':
                    if (isset($autodesk_product['subscriptionId']) || $include_all == true) {
                        $lineItems = ["action" => $orderAction, "quantity" => $autodesk_product["quantity"], "subscriptionId" => $autodesk_product["subscriptionId"], "promotionCode" => $autodesk_product["promotionCode"], "offer" => ["term" => ["code" => $autodesk_product["offer"]["term"]["code"], "description" => $autodesk_product["offer"]["term"]["description"]], "accessModel" => ["code" => $autodesk_product["offer"]["accessModel"]["code"], "description" => $autodesk_product["offer"]["accessModel"]["description"]], "intendedUsage" => ["code" => $autodesk_product["offer"]["intendedUsage"]["code"], "description" => $autodesk_product["offer"]["intendedUsage"]["description"]], "connectivity" => ["code" => $autodesk_product["offer"]["connectivity"]["code"], "description" => $autodesk_product["offer"]["connectivity"]["description"]], "servicePlan" => ["code" => $autodesk_product["offer"]["servicePlan"]["code"], "description" => $autodesk_product["offer"]["servicePlan"]["description"]]]];
                        $lineitems_extra =  ["subscriptionId" => $autodesk_product["subscriptionId"]];
                    } else {
                        $prompt_for_data = true;
                    }
                    break;
                case 'switchproduct':
                    if (isset($autodesk_product['subscriptionId']) || $include_all == true) {
                        $lineItems = ["action" => $orderAction, "quantity" => $autodesk_product["quantity"], "subscriptionId" => $autodesk_product["subscriptionId"], "promotionCode" => $autodesk_product["promotionCode"], "offer" => ["term" => ["code" => $autodesk_product["offer"]["term"]["code"], "description" => $autodesk_product["offer"]["term"]["description"]], "accessModel" => ["code" => $autodesk_product["offer"]["accessModel"]["code"], "description" => $autodesk_product["offer"]["accessModel"]["description"]], "intendedUsage" => ["code" => $autodesk_product["offer"]["intendedUsage"]["code"], "description" => $autodesk_product["offer"]["intendedUsage"]["description"]], "connectivity" => ["code" => $autodesk_product["offer"]["connectivity"]["code"], "description" => $autodesk_product["offer"]["connectivity"]["description"]], "servicePlan" => ["code" => $autodesk_product["offer"]["servicePlan"]["code"], "description" => $autodesk_product["offer"]["servicePlan"]["description"]]]];
                        $lineitems_extra =  ["subscriptionId" => $autodesk_product["subscriptionId"]];
                    } else {
                        $prompt_for_data = true;
                    }
                    break;
                case 'switchterm':
                    if (isset($autodesk_product['subscriptionId']) || $include_all == true) {
                        $lineItems = ["subscriptionId" => $product['subscriptionId'], "endDate" => date('Y-m-d')];
                    } else {
                        $prompt_for_data = true;
                    }
                    $lineitems_extra =  ["subscriptionId" => $product['subscriptionId'], "endDate" => date('Y-m-d')];
                    break;
                case 'coterm':
                    if (isset($autodesk_product['referenceSubscriptionId']) || $include_all == true) {
                        $lineItems = ["action" => "Co-term", "referenceSubscriptionId" => $autodesk_product["referenceSubscriptionId"], "quantity" => $autodesk_product["quantity"], "startDate" => $autodesk_product["startDate"],];
                        $lineitems_extra =  ["referenceSubscriptionId" => $autodesk_product["referenceSubscriptionId"], "startDate" => $autodesk_product["startDate"],];
                    } else {
                        $prompt_for_data = true;
                    }
                    break;
                case 'trueup':
                    if (isset($autodesk_product['subscriptionId']) || $include_all == true) {
                        $lineItems = ["action" => "True-Up", "subscriptionId" => $autodesk_product["subscriptionId"], "quantity" => $autodesk_product["quantity"]];
                        $lineitems_extra =  ["subscriptionId" => $autodesk_product["subscriptionId"]];
                    } else {
                        $prompt_for_data = true;
                    }
                    break;
                case 'extension':
                    $lineItems = [
                        "action" => "Extension",
                        "subscriptionId" => $autodesk_product["subscriptionId"],
                        "endDate" => $autodesk_product["endDate"]
                    ];
                    $lineitems_extra =  [
                        "subscriptionId" => $autodesk_product["subscriptionId"],
                        "endDate" => $autodesk_product["endDate"]
                    ];
                default:
                    ["error" => "Action not found for product " . $product['id']];
                    break;
            }
            if ($orderAction_has_dda) {
                $lineitems_extra['opportunityLineItemId'] = $autodesk_product["opportunityLineItemId"];
            }
            $lineitems_new = null;
            ////print_rr($orderAction, 'orderAction beforeNew');
            if ($simpleAction == 'new' || $include_all == true) {
                ////print_rr($orderAction, 'orderAction pickedNew');
                if (isset($autodesk_product['offeringId'])) {
                    ////print_rr($autodesk_product['offeringId'], '$autodesk_product["offeringId"] isset');
                    $lineItems = ['offeringId' => $autodesk_product['offeringId'], 'offeringName' => $autodesk_product['offeringName'], 'offeringCode' => $autodesk_product['offeringCode'], 'action' => $orderAction, 'quantity' => $product['qty'], 'startDate' => date('Y-m-d'), 'offer' => ['term' => ['code' => $autodesk_product['term_code'], 'description' => $autodesk_product['term_description']], 'accessModel' => ['code' => $autodesk_product['accessModel_code'], 'description' => $autodesk_product['accessModel_description']], 'intendedUsage' => ['code' => $autodesk_product['intendedUsage_code'], 'description' => $autodesk_product['intendedUsage_description']],                'connectivity' => ['code' => $autodesk_product['connectivity_code'], 'description' => $autodesk_product['connectivity_description']], 'servicePlan' => ['code' => $autodesk_product['servicePlan_code'], 'description' => $autodesk_product['servicePlan_description']]],];
                }
            }
            if ($include_all) {
                $lineItems['new'] = $lineItems;
                $lineItems['extra'] = $lineitems_extra;
                $lineItems['internal'] = ["products_id" => $product['id']];
            }
            $this->lineItems[] = $lineItems;
        }
        print_rr($this->lineItems,'process_product_data lineItems');
        $this->quote_products = ['lineItems' => $this->lineItems];
        return $prompt_for_data;
    }
    public function get_extra($orders_id, $products_id, $include_all = false) {
        $order = new Order($orders_id);
        $products = $order->products;
        $productData = $this->process_product_data($products, $include_all, $products_id, $orders_id);
        return $productData;
    }
    public function update_product_extra($orders_id, $products_id, $extras) {
        return $this->database_update_product_extra($orders_id, $products_id, $extras);
    }
    private function database_update_product_extra($orders_id, $products_id, $extras) {
        if (sizeof($extras) < 1) return ['response' => 'nothing to do'];
        $query_sql = "SELECT * FROM orders_products_autodesk WHERE orders_id = :orders_id and products_id = :products_id";
        $result = tep_db_query($query_sql, null, [":orders_id" => $orders_id, ":products_id" => $products_id]);
        $params = [":orders_id" => $orders_id, ":products_id" => $products_id];
        $set_sql = '';
        foreach ($extras as $key => $extra) {
            $params[':' . $key] = $extra;
            if ($set_sql != '') $set_sql .= ', ';
            $set_sql .= $key . ' = ' . ':' . $key;
        }
        if (!tep_db_num_rows($result)) {
            $query_sql = 'INSERT INTO orders_products_autodesk' . ' SET orders_id = :orders_id, products_id = :products_id, ' .  $set_sql;
        } else {
            $query_sql = 'UPDATE orders_products_autodesk' .  ' SET ' . $set_sql . ' WHERE orders_id = :orders_id and products_id = :products_id';
        }
        $response = tep_db_query($query_sql, null, $params);
        ////print_rr($query_sql, 'database_update_product_extra');
        return ['response' => 'Updated quote', 'query_sql' => $query_sql];
    }

    private function database_get_product_extra($orders_id, $products_id = null) {
        $query_sql = "SELECT * FROM orders_products_autodesk WHERE orders_id = :orders_id";
        $params = [":orders_id" => $orders_id];
        if ($products_id != null) {
            $query_sql .= " and products_id = :products_id";
            $params[':products_id'] = $products_id;
        }
        $result = tep_db_query($query_sql, null, $params);
        print_rr($result, 'database_get_product_extra query_sql');
        if ($products_id) return tep_db_fetch_array($result);
        return tep_db_fetch_all($result);
    }

    public static function update_from_api($json_payload) {
        tcs_log(message: "Quote Status changed for quote number: " . $json_payload['payload']['quoteNumber'] . " - " . $json_payload['payload']['message'], subject: 'quote_update');
        $params = [
            ':quoteNumber' => $json_payload['payload']['quoteNumber'],
            ':quoteStatus' => $json_payload['payload']['quoteStatus'],
            ':transactionId'=> $json_payload['payload']['transactionId'],
            ':message' => $json_payload['payload']['message']
        ];
        $where_sql = [];
        $quoteNumber = $json_payload['payload']['quoteNumber'];
        $transactionId = $json_payload['payload']['transactionId'];
        tcs_log(message: "looking for quote number: " . $quoteNumber . " or " . $transactionId, subject: 'quote_update');
        if (isset($transactionId) OR isset($quoteNumber)) {
            tcs_log(message: "Found quote number: " . $quoteNumber . " or " . $transactionId, subject: 'quote_update');
            if ($quoteNumber != null && $quoteNumber != '' && $quoteNumber != 'NA') {
                $where_sql[] = "`quoteNumber` = :w_quoteNumber";
                $params[':w_quoteNumber'] = $quoteNumber;
            }
            if ($transactionId != null && $transactionId != '' && $transactionId != 'NA') {
                $where_sql[] = "`transactionId` = :w_transactionId";
                $params[':w_transactionId'] = $transactionId;
            }
            $wsp = ['\n', '\r', '\t', '  '];
            tcs_log(message: 'built where array:'  . preg_replace('/\s+/', ' ', trim(print_r($where_sql,true))), subject: 'quote_update');
            tcs_log(message: 'built params array:'  . preg_replace('/\s+/', ' ', trim(print_r($params,true))), subject: 'quote_update');

            $query_sql = "UPDATE autodesk_quotes SET `quoteStatus` = :quoteStatus, `transactionId` = :transactionId,`quoteNumber` = :quoteNumber, `message` = :message, `modifiedAt` = NOW() WHERE " . implode(' OR ', $where_sql);
            if (count($where_sql) > 0){
                tcs_log(message: 'performing query:'. preg_replace('/\s+/', ' ', trim(print_r($query_sql,true))), subject: 'quote_update');
                $result = tep_db_query($query_sql,null,$params,true);
                tcs_log(message: 'result:'. preg_replace('/\s+/', ' ', trim(print_r($result,true))), subject: 'quote_update');
            } else {
                tcs_log(message: "No quote number or transaction id found", subject: 'quote_update');
            }
            $response = ['success' => 1];
        } else {
            tcs_log(message: "No quote number or transaction id found", subject: 'quote_update');
            $response = ['success' => 0];
        }
        return tcs_log(message: "complete, success: " . $response['success'], subject: 'quote_update');
    }
}
