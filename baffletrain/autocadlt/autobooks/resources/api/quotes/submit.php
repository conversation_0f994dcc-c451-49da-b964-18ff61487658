<?php
// Set content type to JSON
header('Content-Type: application/json');

// Get the JSON data from the request
$json_data = file_get_contents('php://input');
$data = json_decode($json_data, true);

// First validate the data
$validation_url = 'http://' . $_SERVER['HTTP_HOST'] . '/api/quotes/validate.php';
$ch = curl_init($validation_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $json_data);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
$validation_result = curl_exec($ch);
$validation_status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

// If validation failed, return the validation errors
if ($validation_status !== 200) {
    http_response_code($validation_status);
    echo $validation_result;
    exit;
}

// Process the submission (in a real app, this would send the data to Autodesk API)
// For this example, we'll just return a success message with the quote ID

// Generate a random quote ID
$quote_id = 'Q-' . strtoupper(substr(md5(uniqid()), 0, 8));

// Return success response
echo json_encode([
    'success' => true,
    'message' => 'Quote submitted successfully',
    'quote_id' => $quote_id,
    'data' => $data
]);
