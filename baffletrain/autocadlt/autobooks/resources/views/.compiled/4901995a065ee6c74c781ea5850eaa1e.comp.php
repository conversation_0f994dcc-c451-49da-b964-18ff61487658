<?php namespace edgeTemplate\view_4901995a065ee6c74c781ea5850eaa1e;use edge\Edge;?><?php extract(['edge_manifest' => array (
)]);;print_rr($tag_content,"tag_content_start"); if (!isset($tag_content) || $tag_content == 0 || $tag_content == 1 ){ ?><?php
// Data table template
// This template will be used to generate data table views

// Headers and rows will be replaced with actual data
$headers = array (
);
$rows = array (
);

// Render the data table
echo Edge::render('data-table', [
    'headers' => $headers,
    'rows' => $rows,
    'striped' => true,
    'hover' => true
]);<?php } ?>