<?php namespace edgeTemplate\view_da3fc3f0ce3557f0108f573887258518;use edge\Edge;?><?php extract(Edge::pp(['edge_manifest' => array (
),'headers' => [],
    'rows' => []
],$edge_data));;print_rr($tag_content,"tag_content_start"); if (!isset($tag_content) || $tag_content == 0 || $tag_content == 1 ){?>

<?= Edge::render('data-table', ['headers' => $headers, 'rows' => $rows, "striped" => "true", "hover" => "true"], 0) ?><?php } ?>