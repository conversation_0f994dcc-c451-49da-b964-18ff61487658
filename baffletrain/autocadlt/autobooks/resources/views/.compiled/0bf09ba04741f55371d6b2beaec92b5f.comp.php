<?php namespace edgeTemplate\view_0bf09ba04741f55371d6b2beaec92b5f;use edge\Edge;?><?php extract(Edge::pp(['edge_manifest' => array (
),'headers' => [],
    'rows' => []
],$edge_data));;print_rr($tag_content,"tag_content_start"); if (!isset($tag_content) || $tag_content == 0 || $tag_content == 1 ){?>

<?= Edge::render('data-table', ['headers' => $headers, 'rows' => $rows, "striped" => "true", "hover" => "true"], 0) ?><?php } ?>