/* HTMX Indicator Styles */

.htmx-indicator {
    opacity: 0;
    transition: opacity 200ms ease-in-out;
}

.htmx-request .htmx-indicator {
    opacity: 1;
}

.htmx-request.htmx-indicator {
    opacity: 1;
}

/* Progress bar animation */
@keyframes progress {
    0% { width: 0; }
    10% { width: 30%; }
    20% { width: 50%; }
    30% { width: 70%; }
    40% { width: 80%; }
    50% { width: 85%; }
    60% { width: 90%; }
    70% { width: 93%; }
    80% { width: 95%; }
    90% { width: 97%; }
    100% { width: 99%; }
}

.htmx-request .progress-bar {
    animation: progress 2s ease-in-out forwards;
}
