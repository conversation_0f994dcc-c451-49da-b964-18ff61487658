<?php
/**
 * Update Autodesk Catalog Hashes
 *
 * This script updates the unique hashes in the products_autodesk_catalog table
 * and related tables to ensure consistency between old and new implementations.
 *
 * Usage: php update_autodesk_hashes.php
 */

// Include necessary files
require_once('../classes/data_importer.class.php');

// Set up logging
function log_message($message) {
    echo date('Y-m-d H:i:s') . " - " . $message . "\n";
    error_log(date('Y-m-d H:i:s') . " - " . $message);
}

log_message("Starting Autodesk hash update process");

// Set batch size (adjust as needed based on server resources)
$batch_size = 100;

// Run the update process
log_message("Running hash update with batch size: {$batch_size}");
$result = data_importer::update_autodesk_catalog_hashes($batch_size);

// Log the results
if (isset($result['error'])) {
    log_message("Error: " . $result['error']);
} else {
    log_message("Hash update completed successfully");
    log_message("Total records processed: " . $result['total_records']);
    log_message("Updated catalog records: " . $result['updated_catalog_records']);
    log_message("Updated relation records: " . $result['updated_relation_records']);
    log_message("Failed records: " . $result['failed_records']);
}

log_message("Autodesk hash update process completed");
