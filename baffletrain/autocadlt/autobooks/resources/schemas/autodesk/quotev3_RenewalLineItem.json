// RenewalLineItem.json
[
  {
    "field": "lineItems[i].action",
    "description": "Action to perform. - Renewal",
    "required": true,
    "type": "string"
  },
  {
    "field": "lineItems[i].agentLineReference",
    "description": "External line reference identifier, if applicable. Can be up to 37 characters and must start and end with a letter or digit. All characters must be a letter, digit, or hyphen.",
    "required": false,
    "type": "string"
  },
  {
    "field": "lineItems[i].quantity",
    "description": "Number of units. Must be greater than 0 (> 0).",
    "required": true,
    "type": "integer"
  },
  {
    "field": "lineItems[i].subscriptionId",
    "description": "Subscription ID of subscription to be renewed. Please refer to Appendix C: Same Subscription on Multiple Line Items for more information if multiple line items on the quote reference the same subscription. If the subscription is within 30 days of being cancelled, the quote expiration date will be determined by this cancellation date. The quote will expire when the subscription is cancelled. If there are multiple renewals on the same quote with different cancellation dates within 30 days, the earliest cancellation date will determine the quote expiration date.",
    "required": true,
    "type": "string"
  },
  {
    "field": "lineItems[i].promotionCode",
    "description": "Manually apply a promotion code to the line item. This promotion code overrides any automatically applied promotion codes. If the promotion code end date is earlier than the quote expiration date, the promotion code end date overrides the quote expiration date. The quote will expire when the promotion code expires.",
    "required": false,
    "type": "string"
  }
]
