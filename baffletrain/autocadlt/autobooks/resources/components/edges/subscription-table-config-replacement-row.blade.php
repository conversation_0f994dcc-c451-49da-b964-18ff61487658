@props([
    'search' => '',
    'replace' => '',
    'is_new' => false
])

<div class="replacement-item border rounded-lg overflow-hidden cursor-move" x-data="{ itemOpen: {{ $is_new ? 'true' : 'false' }} }">
    <div class="flex items-center bg-gray-50 p-2">
        <div class="drag-handle mr-2 text-gray-400 hover:text-gray-600">
            ⋮⋮
        </div>
        <button @click="itemOpen = !itemOpen" type="button" class="flex-grow flex items-center justify-between">
            <span class="text-sm">{{ $search }} → {{ $replace }}</span>
            <svg :class="{'rotate-180': itemOpen}" class="w-4 h-4 transform transition-transform" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
            </svg>
        </button>
    </div>
    <div x-show="itemOpen" x-transition class="p-3 border-t bg-white">
        <div class="flex gap-2">
            <input type="text" name="replacement_search[]" value="{{ $search }}"
                   class="block w-1/2 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                   placeholder="Search for...">
            <input type="text" name="replacement_replace[]" value="{{ $replace }}"
                   class="block w-1/2 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                   placeholder="Replace with...">
            <button type="button" class="text-red-600 hover:text-red-800"
                    hx-delete="api/remove_replacement"
                    hx-target="closest .replacement-item"
                    hx-swap="outerHTML">×</button>
        </div>
    </div>
</div>