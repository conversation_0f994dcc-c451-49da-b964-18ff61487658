@props([
    'title' => 'Quote V3 Editor',
    'description' => 'Editor for Autodesk CreateQuote v3 Request Body',
    'json_data' => '',
    'id' => 'quote_v3_editor_' . rand(0,10000),
    'class_suffix' => '',
    'height' => '600px',
    'readonly' => false
])

@php
    // Default to the template if no data is provided
    if (empty($json_data)) {
        $template_path = 'resources/templates/QuoteV3Template.json';
        if (file_exists($template_path)) {
            $json_data = file_get_contents($template_path);
        } else {
            $json_data = '{}';
        }
    }
    
    // Load the schema
    $schema_path = 'resources/schemas/QuoteV3.json';
    $schema = '';
    if (file_exists($schema_path)) {
        $schema = file_get_contents($schema_path);
        $schema = json_decode($schema);
        
        // Convert schema to JSONEditor format if needed
        // This is a simplified conversion - you may need to enhance this
        $jsoneditor_schema = [
            'type' => 'object',
            'title' => $schema->title ?? 'Quote V3',
            'description' => $schema->description ?? '',
            'properties' => []
        ];
        
        if (isset($schema->fields) && is_array($schema->fields)) {
            foreach ($schema->fields as $field) {
                if (!isset($field->name)) continue;
                
                $property = [
                    'type' => $field->type === 'select' ? 'string' : ($field->type === 'textarea' ? 'string' : $field->type),
                    'title' => $field->label ?? $field->name,
                    'description' => $field->description ?? ''
                ];
                
                if (isset($field->required) && $field->required) {
                    $jsoneditor_schema['required'][] = $field->name;
                }
                
                if (isset($field->options) && is_array($field->options)) {
                    $property['enum'] = [];
                    $property['options'] = ['enum_titles' => []];
                    
                    foreach ($field->options as $option) {
                        $property['enum'][] = $option->value;
                        $property['options']['enum_titles'][] = $option->label;
                    }
                }
                
                if (isset($field->children) && is_array($field->children)) {
                    $property['type'] = 'object';
                    $property['properties'] = [];
                    
                    foreach ($field->children as $child) {
                        if (!isset($child->name)) continue;
                        
                        $childProperty = [
                            'type' => $child->type === 'select' ? 'string' : ($child->type === 'textarea' ? 'string' : $child->type),
                            'title' => $child->label ?? $child->name,
                            'description' => $child->description ?? ''
                        ];
                        
                        if (isset($child->required) && $child->required) {
                            $property['required'][] = $child->name;
                        }
                        
                        if (isset($child->options) && is_array($child->options)) {
                            $childProperty['enum'] = [];
                            $childProperty['options'] = ['enum_titles' => []];
                            
                            foreach ($child->options as $option) {
                                $childProperty['enum'][] = $option->value;
                                $childProperty['options']['enum_titles'][] = $option->label;
                            }
                        }
                        
                        $property['properties'][$child->name] = $childProperty;
                    }
                }
                
                $jsoneditor_schema['properties'][$field->name] = $property;
            }
        }
        
        $schema = json_encode($jsoneditor_schema);
    }
    
    // Ensure we have valid JSON data
    $json_object = json_decode($json_data);
    if (json_last_error() !== JSON_ERROR_NONE) {
        $json_data = '{}';
    } else {
        // Re-encode to ensure proper formatting
        $json_data = json_encode($json_object, JSON_PRETTY_PRINT);
    }
    
    // Prepare editor options
    $editor_options = [
        'mode' => $readonly ? 'view' : 'tree',
        'modes' => $readonly ? ['view'] : ['tree', 'code', 'form', 'text', 'preview'],
        'search' => true,
        'navigationBar' => true,
        'statusBar' => true,
        'mainMenuBar' => true,
        'history' => true
    ];
    
    // Add schema if available
    if (!empty($schema)) {
        $editor_options['schema'] = json_decode($schema);
    }
    
    // Convert options to JSON for JavaScript
    $editor_options_json = json_encode($editor_options);
@endphp

<div class="w-full {{ $class_suffix }}">
    @if (!empty($title))
        <h3 class="text-lg font-medium leading-6 text-gray-900 mb-2">{{ $title }}</h3>
    @endif
    
    <div id="{{ $id }}_container" class="border border-gray-300 rounded-md" style="height: {{ $height }}">
        <div id="{{ $id }}" class="w-full h-full"></div>
    </div>
    
    <textarea id="{{ $id }}_data" name="quote_v3_data" class="hidden">{{ $json_data }}</textarea>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Create the editor
            const container = document.getElementById('{{ $id }}');
            const options = {!! $editor_options_json !!};
            
            // Initialize the editor with the JSON data
            const editor = new JSONEditor(container, options);
            const initialJson = {!! $json_data !!};
            editor.set(initialJson);
            
            // Update the hidden textarea when the JSON changes
            editor.onChange = function() {
                try {
                    const json = editor.get();
                    document.getElementById('{{ $id }}_data').value = JSON.stringify(json);
                } catch (err) {
                    console.error('JSON parsing error:', err);
                }
            };
        });
    </script>
</div>
