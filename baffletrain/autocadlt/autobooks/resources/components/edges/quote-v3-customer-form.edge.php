@props([
    'title' => 'New Customer Form',
    'description' => 'Form for creating a new customer',
    'quote_data' => [],
    'new_customer' => false,
    'errors' => []
])
<div id="customer-form-container" class="sm:col-span-6 space-y-6">
    <div id="customer-search-results" class="mt-2"></div>
    <x-forms-input-button
            name="lineItems[{{ $index }}].offeringId"
            id="lineItems[{{ $index }}].offeringId"
            value="{{ $item['offeringId'] }}"
            button_label="Search"
            hx-get="api/quote-v3/search_offerings"
            hx-target="#offering-search-results-{{ $index }}"
            hx-trigger="click"
            placeholder="Search for an existing customer"
    />
@if($new_customer)
    <!-- New Customer Fields -->

        <!-- Is Individual -->
        <div>
            <x-forms-field-with-description
                    label="Is Individual?"
                    field_name="isIndividual"
                    description_text="Determines if the end customer is an individual (true) or a company (false). Required when creating a new customer."
            >
                <x-forms-checkbox-toggle
                        name="endCustomer.isIndividual"
                        label="Is Individual?"
                        checked="{{ isset($quote_data['endCustomer']['isIndividual']) && $quote_data['endCustomer']['isIndividual'] ? 'true' : 'false' }}"
                        hx-get='api/quote-v3/toggle-individual'
                        hx-target='#company-fields'
                        hx-trigger='change'
                />
            </x-forms-field-with-description>
            @if(isset($errors['endCustomer.isIndividual']))
                <p class="mt-2 text-sm text-red-600">{{ $errors['endCustomer.isIndividual'] }}</p>
            @endif
        </div>

        <!-- Company Name (only if not individual) -->
        <div id="company-fields">
            <x-forms-field-with-description
                    label="Company Name"
                    field_name="companyName"
                    description_text="Company name. Required if creating a new company customer (isIndividual=false). Max 100 chars."
            >
                <x-forms-input
                        type="text"
                        name="endCustomer.name"
                        id="endCustomer.name"
                        value="{{ isset($quote_data['endCustomer']['name']) ? $quote_data['endCustomer']['name'] : '' }}"
                />
            </x-forms-field-with-description>
            @if(isset($errors['endCustomer.name']))
                <p class="mt-2 text-sm text-red-600">{{ $errors['endCustomer.name'] }}</p>
            @endif
        </div>

    <!-- Address Fields -->
    <div class="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
        <div class="sm:col-span-3">
            <x-forms-field-with-description
                    label="Address Line 1"
                    field_name="addressLine1"
                    description_text="Street address. Required when creating a new customer."
            >
                <x-forms-input
                        type="text"
                        name="endCustomer.addressLine1"
                        id="endCustomer.addressLine1"
                        value="{{ isset($quote_data['endCustomer']['addressLine1']) ? $quote_data['endCustomer']['addressLine1'] : '' }}"
                />
            </x-forms-field-with-description>
        </div>

        <div class="sm:col-span-3">
            <x-forms-field-with-description
                    label="Address Line 2"
                    field_name="addressLine2"
                    description_text="Suite or secondary address information."
            >
                <x-forms-input
                        type="text"
                        name="endCustomer.addressLine2"
                        id="endCustomer.addressLine2"
                        value="{{ isset($quote_data['endCustomer']['addressLine2']) ? $quote_data['endCustomer']['addressLine2'] : '' }}"
                />
            </x-forms-field-with-description>
        </div>
    </div>

@else

    <!-- Existing Customer CSN -->
        <x-forms-field-with-description
                label="End Customer CSN"
                field_name="endCustomerCsn"
                description_text="10-digit CSN for an existing End Customer. Required if action is Renewal, Switch, Extension, or True-up, and must match the subscription's customer. If creating from an Opportunity (As-Is), must match opportunity's customer."
        >
            <div class="mt-2 flex rounded-md shadow-sm">
                <x-forms-input
                        type="text"
                        name="endCustomer.accountCsn"
                        id="endCustomer.accountCsn"
                        placeholder="10-digit CSN"
                        value="{{ isset($quote_data['endCustomer']['accountCsn']) ? $quote_data['endCustomer']['accountCsn'] : '' }}"F
                />
                <button type="button"
                        class="relative -ml-px inline-flex items-center gap-x-1.5 rounded-r-md px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                        hx-get="api/quote-v3/search-customers"
                        hx-target="#customer-search-results"
                        hx-include="#endCustomer\.accountCsn"
                        hx-trigger="click">
                    Search
                </button>
            </div>
        </x-forms-field-with-description>
        @if(isset($errors['endCustomer.accountCsn']))
            <p class="mt-2 text-sm text-red-600">{{ $errors['endCustomer.accountCsn'] }}</p>
        @endif

        <div id="customer-details" class="mt-4"></div>
@endif
</div>
