@props([
    'title' => 'Autodesk Quote V3 Form',
    'description' => 'Form for creating Autodesk quotes based on QuoteV3 schema',
    'endpoint' => 'api/quotes/submit',
    'id' => 'quote_v3_form_' . rand(0,10000),
    'quote_data' => [
          "currency" => "GBP",
          "agentAccount" => [
            "accountCsn" =>  "**********"
          ],
          "skipDDACheck" => true
    ]
])

<!-- Include HTMX indicator styles -->
<link rel="stylesheet" href="resources/css/htmx-indicators.css">

<div class="bg-white shadow-sm ring-1 ring-gray-900/5 sm:rounded-xl md:col-span-2"
     id="{{ $id }}">
    <!-- Loading indicator -->
    <div id="form-indicator" class="htmx-indicator fixed top-0 left-0 w-full h-1 z-50">
        <div class="bg-indigo-600 h-full progress-bar"></div>
    </div>

    <form hx-post="{{ $endpoint }}"
          hx-swap="outerHTML"
          hx-indicator="#form-indicator"
          class="px-4 py-6 sm:p-8">

        <div class="space-y-12">
            <!-- Form sections will go here -->

            <!-- Basic Information Section -->
            <div class="border-b border-gray-900/10 pb-12">
                <h2 class="text-base font-semibold leading-7 text-gray-900">Basic Information</h2>
                <p class="mt-1 text-sm leading-6 text-gray-600">
                    Enter the basic information for the quote.
                </p>

                <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                    <!-- Currency -->
                    <div class="sm:col-span-3">
                        <x-forms-select
                            name="currency"
                            label="Currency"
                            :options="['GBP' => 'GBP - British Pound']"
                            :selected="$quote_data['currency'] ?? 'GBP'"
                            required
                        />
                    </div>

                    <!-- Quote Language -->
                    <div class="sm:col-span-3">
                        <x-forms-select
                            name="quoteLanguage"
                            label="Quote Language"
                            :options="['en' => 'English']"
                        />
                    </div>

                    <!-- Quote Notes -->
                    <div class="col-span-full">
                        <x-forms-textarea
                            name="quoteNotes"
                            label="Quote Notes (for Customer PDF)"
                            placeholder="Enter notes that will be visible to the customer on the quote PDF"
                            rows="3"
                            maxlength="254"
                        />
                    </div>

                    <!-- Opportunity Number -->
                    <div class="sm:col-span-3">
                        <x-forms-input
                            name="opportunityNumber"
                            label="Opportunity Number"
                            placeholder="A-XXXXXXXX"
                            type="text"
                        />
                    </div>
                </div>
            </div>

            <!-- Agent Information Section -->
            <div class="border-b border-gray-900/10 pb-12">
                <h2 class="text-base font-semibold leading-7 text-gray-900">Agent Information</h2>
                <p class="mt-1 text-sm leading-6 text-gray-600">
                    Enter the agent account and contact information.
                </p>

                <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                    <!-- Agent Account CSN -->
                    <div class="sm:col-span-3">
                        <x-forms-input
                            name="agentAccount.accountCsn"
                            label="Agent Account CSN"
                            type="text"
                            :value="$quote_data['agentAccount']['accountCsn'] ?? ''"
                            required
                        />
                    </div>

                    <!-- Agent Email -->
                    <div class="sm:col-span-3">
                        <x-forms-input
                            name="agentContact.email"
                            label="Agent Email"
                            type="email"
                            :value="$quote_data['agentContact']['email'] ?? ''"
                            required
                        />
                    </div>
                </div>
            </div>

            <!-- End Customer Section -->
            <div class="border-b border-gray-900/10 pb-12">
                <h2 class="text-base font-semibold leading-7 text-gray-900">End Customer Information</h2>
                <p class="mt-1 text-sm leading-6 text-gray-600">
                    Enter the end customer details. For existing customers, you only need to provide the CSN.
                </p>

                <div class="mt-4">
                    <x-forms-radio-group
                        name="existing_customer"
                        :options="[
                            [
                                'value' => 'true',
                                'label' => 'Existing Customer (CSN only)',
                                'checked' => true
                            ],
                            [
                                'value' => 'false',
                                'label' => 'New Customer (Full details)',
                                'checked' => false
                            ]
                        ]"
                        layout="vertical"
                        hx-get='api/customer/existing-form'
                        hx-target='#customer-form-container'
                        hx-trigger='change'
                    />
                </div>

                <!-- Customer form container that will be replaced via HTMX -->
                <div id="customer-form-container" class="mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                    <!-- Default: Existing customer form -->
                    <div class="sm:col-span-3">
                        <x-forms-input
                            name="endCustomer.accountCsn"
                            label="End Customer CSN"
                            type="text"
                            :value="$quote_data['endCustomer']['accountCsn'] ?? ''"
                            required
                        />
                    </div>
                </div>
            </div>

            <!-- Quote Contact Section -->
            <div class="border-b border-gray-900/10 pb-12">
                <h2 class="text-base font-semibold leading-7 text-gray-900">Quote Contact Information</h2>
                <p class="mt-1 text-sm leading-6 text-gray-600">
                    Enter the primary contact information for this quote.
                </p>

                <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                    <!-- Contact Email -->
                    <div class="sm:col-span-3">
                        <x-forms-input
                            name="quoteContact.email"
                            label="Quote Contact Email"
                            type="email"
                            :value="$quote_data['quoteContact']['email'] ?? ''"
                            required
                        />
                    </div>

                    <!-- First Name -->
                    <div class="sm:col-span-3">
                        <x-forms-input
                            name="quoteContact.firstName"
                            label="First Name"
                            type="text"
                        />
                    </div>

                    <!-- Last Name -->
                    <div class="sm:col-span-3">
                        <x-forms-input
                            name="quoteContact.lastName"
                            label="Last Name"
                            type="text"
                        />
                    </div>

                    <!-- Phone -->
                    <div class="sm:col-span-3">
                        <x-forms-input
                            name="quoteContact.phone"
                            label="Phone Number"
                            type="tel"
                        />
                    </div>

                    <!-- Preferred Language -->
                    <div class="sm:col-span-3">
                        <x-forms-select
                            name="quoteContact.preferredLanguage"
                            label="Preferred Language"
                            :options="['en' => 'English', 'es' => 'Spanish']"
                        />
                    </div>
                </div>
            </div>

            <!-- Admin Section (for New Subscriptions) -->
            <div class="border-b border-gray-900/10 pb-12">
                <div class="flex items-center justify-between">
                    <h2 class="text-base font-semibold leading-7 text-gray-900">Primary Admin (for New Subscriptions)</h2>
                    <div class="flex items-center">
                        <x-forms-checkbox-toggle
                            name="show_admin"
                            label="Add Primary Admin"
                            hx-get='api/admin/toggle-form'
                            hx-target='#admin-form-container'
                            hx-trigger='change'
                        />
                    </div>
                </div>
                <p class="mt-1 text-sm leading-6 text-gray-600">
                    Enter the primary admin for NEW subscriptions. If omitted, the quote contact becomes the admin.
                </p>

                <!-- Admin form container that will be replaced via HTMX -->
                <div id="admin-form-container" class="mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                    <!-- Empty by default, will be populated when checkbox is checked -->
                </div>
            </div>

            <!-- Additional Recipients Section -->
            <div class="border-b border-gray-900/10 pb-12">
                <h2 class="text-base font-semibold leading-7 text-gray-900">Additional Recipients</h2>
                <p class="mt-1 text-sm leading-6 text-gray-600">
                    Add up to 4 additional email recipients for the quote.
                </p>

                <div class="mt-6">
                    <div class="flex gap-x-3">
                        <x-forms-input
                            name="new_recipient_email"
                            label="Recipient Email"
                            type="email"
                            id="new-recipient-email"
                        />
                        <x-forms-button
                            label="Add"
                            variant="primary"
                            type="button"
                            hx-post='api/recipients/add'
                            hx-include='#new-recipient-email'
                            hx-target='#recipients-list'
                            hx-swap='innerHTML'
                            class_suffix="mt-8"
                        />
                    </div>

                    <div id="recipients-list" class="mt-4">
                        <!-- Recipients will be added here via HTMX -->
                        @if(isset($quote_data['additionalRecipients']) && is_array($quote_data['additionalRecipients']))
                            @foreach($quote_data['additionalRecipients'] as $index => $recipient)
                                <div class="flex items-center justify-between py-2 border-b">
                                    <div>
                                        <span>{{ $recipient['email'] }}</span>
                                        <input type="hidden" name="additionalRecipients[{{ $index }}].email" value="{{ $recipient['email'] }}">
                                    </div>
                                    <x-forms-button
                                        label="Remove"
                                        variant="danger"
                                        type="button"
                                        hx-delete='api/recipients/remove?email={{ urlencode($recipient['email']) }}'
                                        hx-target='#recipients-list'
                                        hx-swap='innerHTML'
                                        class_suffix="text-red-600 hover:text-red-800 bg-transparent shadow-none"
                                    />
                                </div>
                            @endforeach
                        @endif
                    </div>
                </div>
            </div>

            <!-- Additional Options Section -->
            <div class="border-b border-gray-900/10 pb-12">
                <h2 class="text-base font-semibold leading-7 text-gray-900">Additional Options</h2>

                <div class="mt-6 space-y-6">
                    <div class="flex items-center gap-x-3">
                        <x-forms-checkbox-toggle
                            name="skipDDACheck"
                            label="Skip DDA Check"
                            checked="{{ isset($quote_data['skipDDACheck']) && $quote_data['skipDDACheck'] ? 'true' : 'false' }}"
                        />
                    </div>

                    <div class="flex items-center gap-x-3">
                        <x-forms-checkbox-toggle
                            name="skipM2SDiscountValidation"
                            label="Skip M2S Discount Validation"
                        />
                    </div>

                    <div class="sm:col-span-3">
                        <x-forms-input
                            name="agentQuoteReference"
                            label="Agent Quote Reference"
                            type="text"
                            maxlength="63"
                        />
                    </div>
                </div>
            </div>

            <!-- Line Items Section -->
            <div class="pb-12">
                <h2 class="text-base font-semibold leading-7 text-gray-900">Line Items</h2>
                <p class="mt-1 text-sm leading-6 text-gray-600">
                    Add one or more line items to the quote.
                </p>

                <div class="mt-6">
                    <x-forms-button
                        label="Add Line Item"
                        variant="primary"
                        type="button"
                        hx-get='api/line-items/add'
                        hx-target='#line-items-container'
                        hx-swap='beforeend'
                    />
                </div>

                <div id="line-items-container" class="mt-6 space-y-8">
                    <!-- Line items will be added here via HTMX -->
                    @if(isset($quote_data['lineItems']) && is_array($quote_data['lineItems']))
                        @php
                            // Initialize session for line item count if not already set
                            if (!isset($_SESSION)) {
                                session_start();
                            }
                            if (!isset($_SESSION['line_item_count'])) {
                                $_SESSION['line_item_count'] = 0;
                            }
                        @endphp

                        @foreach($quote_data['lineItems'] as $index => $lineItem)
                            @php
                                // Increment the count for each line item
                                $lineItemIndex = $_SESSION['line_item_count']++;
                                // Generate a unique ID for this line item
                                $id = uniqid();
                            @endphp
                            <div id="line-item-{{ $id }}" class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                <div class="flex justify-between items-center mb-4">
                                    <h3 class="text-lg font-medium">Line Item {{ $lineItemIndex + 1 }}</h3>
                                    <x-forms-button
                                        label="Remove"
                                        variant="danger"
                                        type="button"
                                        hx-delete='api/line-items/remove?id={{ $id }}'
                                        hx-target='#line-item-{{ $id }}'
                                        hx-swap='outerHTML'
                                        class_suffix="text-red-600 hover:text-red-800 bg-transparent shadow-none"
                                    />
                                </div>

                                <div class="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                                    <!-- Action -->
                                    <div class="sm:col-span-3">
                                        <x-forms-select
                                            name="lineItems[{{ $lineItemIndex }}].action"
                                            label="Action"
                                            :options="[
                                                'New' => 'New',
                                                'Renewal' => 'Renewal',
                                                'Switch' => 'Switch (Product/Term)',
                                                'Mid-term Switch' => 'Mid-term Switch (ACS Only)',
                                                'Extension' => 'Extension',
                                                'True-up' => 'True-up',
                                                'Co-term' => 'Co-term'
                                            ]"
                                            :selected="$lineItem['action'] ?? ''"
                                            required
                                        />
                                    </div>

                                    <!-- Quantity -->
                                    <div class="sm:col-span-3">
                                        <x-forms-input
                                            name="lineItems[{{ $lineItemIndex }}].quantity"
                                            label="Quantity"
                                            type="number"
                                            min="1"
                                            :value="$lineItem['quantity'] ?? 1"
                                            required
                                        />
                                    </div>

                                    <!-- Subscription ID (for renewals) -->
                                    <div class="sm:col-span-3">
                                        <x-forms-input
                                            name="lineItems[{{ $lineItemIndex }}].subscriptionId"
                                            label="Subscription ID"
                                            type="text"
                                            :value="$lineItem['subscriptionId'] ?? ''"
                                        />
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @endif
                </div>
            </div>

        </div>

        <div class="mt-6 flex items-center justify-end gap-x-6">
            <x-forms-button
                label="Cancel"
                variant="secondary"
                type="button"
            />
            <x-forms-button
                label="Submit Quote"
                variant="primary"
                type="submit"
            />
        </div>
    </form>
</div>
