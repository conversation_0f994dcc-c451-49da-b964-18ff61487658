@props([
    'title' => 'Autodesk Quote V3 Form',
    'description' => 'Form for creating Autodesk quotes based on QuoteV3 schema',
    'endpoint' => 'api/quote-v3/submit',
    'schema_path' => 'resources/schemas/QuoteV3.json',
    'id' => 'quote_v3_form_' . rand(0,10000),
    'quote_data' => [
          "currency" => "GBP",
          "agentAccount" => [
            "accountCsn" =>  "**********"
          ],
          "skipDDACheck" => true
    ],
    'errors' => []
])

@php
// Load the schema
$schema = [];
if (file_exists($schema_path)) {
    $schema_content = file_get_contents($schema_path);
    $schema = json_decode($schema_content, true);
}

// Initialize quote data with defaults from schema if not provided
$initialData = $quote_data;
@endphp

<div class="bg-white shadow-sm ring-1 ring-gray-900/5 sm:rounded-xl md:col-span-2"
     id="{{ $id }}">
    <!-- Display any form-wide errors -->
    @if(isset($errors['api']))
    <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm text-red-700">{{ $errors['api'] }}</p>
            </div>
        </div>
    </div>
    @endif

    <form hx-post="{{ $endpoint }}"
          hx-swap="outerHTML"
          hx-indicator="#form-indicator"
          class="px-4 py-6 sm:p-8">

    <!-- Loading indicator -->
    <div id="form-indicator" class="htmx-indicator fixed top-0 left-0 w-full h-1 z-50">
        <div class="bg-indigo-600 h-full animate-progress-indeterminate"></div>
    </div>

        <div class="space-y-12">
            <!-- Form sections will go here -->

            <!-- Basic Information Section -->
            <div class="border-b border-gray-900/10 pb-12">
                <h2 class="text-base font-semibold leading-7 text-gray-900">Basic Information</h2>
                <p class="mt-1 text-sm leading-6 text-gray-600">
                    Enter the basic information for the quote.
                </p>

                <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                    <!-- Currency -->
                    <div class="sm:col-span-3">
                        <x-forms-field-with-description
                            label="Currency"
                            field_name="currency"
                            description_text="Currency for the quote (ISO 4217 code). Refer to the policy guide for supported currencies. If quoting from an opportunity with value-based items, this must match the opportunity currency."
                        >
                            <x-forms-select
                                id="currency"
                                name="currency"
                                extra_attributes="required hx-get='api/quote-v3/update-currency-dependent-fields' hx-trigger='change' hx-target='#currency-dependent-fields'"
                                :options="[
                                    '' => 'Select a currency',
                                    'USD' => 'USD',
                                    'EUR' => 'EUR',
                                    'GBP' => 'GBP',
                                    'JPY' => 'JPY',
                                    'AUD' => 'AUD',
                                    'CAD' => 'CAD'
                                ]"
                                :selected="isset($quote_data['currency']) ? $quote_data['currency'] : ''"
                            />
                        </x-forms-field-with-description>
                        @if(isset($errors['currency']))
                        <p class="mt-2 text-sm text-red-600">{{ $errors['currency'] }}</p>
                        @endif
                        <div id="currency-dependent-fields"></div>
                    </div>

                    <!-- Quote Language -->
                    <div class="sm:col-span-3">
                        <x-forms-field-with-description
                            label="Quote Language"
                            field_name="quoteLanguage"
                            description_text="Language for the quote (ISO 639-1 code). See Appendix D for logic on how language is determined if not specified. Must be supported by the end customer's country."
                        >
                            <x-forms-select
                                id="quoteLanguage"
                                name="quoteLanguage"
                                :options="[
                                    '' => 'Select a language',
                                    'en' => 'English',
                                    'fr' => 'French',
                                    'de' => 'German',
                                    'es' => 'Spanish',
                                    'it' => 'Italian',
                                    'ja' => 'Japanese',
                                    'ko' => 'Korean',
                                    'pt' => 'Portuguese',
                                    'ru' => 'Russian',
                                    'zh_CN' => 'Chinese Simplified'
                                ]"
                                :selected="isset($quote_data['quoteLanguage']) ? $quote_data['quoteLanguage'] : ''"
                            />
                        </x-forms-field-with-description>
                        @if(isset($errors['quoteLanguage']))
                        <p class="mt-2 text-sm text-red-600">{{ $errors['quoteLanguage'] }}</p>
                        @endif
                    </div>
                </div>
            </div>

            <!-- End Customer Section -->
            <div class="border-b border-gray-900/10 pb-12">
                <h2 class="text-base font-semibold leading-7 text-gray-900">End Customer Information</h2>
                <p class="mt-1 text-sm leading-6 text-gray-600">
                    Enter the end customer details. You can either use an existing customer CSN or enter new customer information.
                </p>

                <div class="mt-6">
                    <x-forms-radio-group
                        name="customerType"
                        :options="[
                            [
                                'value' => 'existing',
                                'label' => 'Existing Customer',
                                'checked' => isset($quote_data['endCustomer']['accountCsn'])
                            ],
                            [
                                'value' => 'new',
                                'label' => 'New Customer',
                                'checked' => !isset($quote_data['endCustomer']['accountCsn']) && isset($quote_data['endCustomer']['name'])
                            ]
                        ]"
                        hx-get='api/quote-v3/customer-form'
                        hx-target='#customer-form-container'
                        hx-trigger='change'
                    />
                </div>

                <div class="mt-0 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                    <div class="sm:col-span-6">
                    <x-quote-v3-customer-form quote_data="{{ $quote_data }}" existing_customer="{{ isset($quote_data['endCustomer']['accountCsn']) }}" />
                </div>
            </div>

            <!-- Quote Contact Section -->
            <div class="border-b border-gray-900/10 pb-12">
                <h2 class="text-base font-semibold leading-7 text-gray-900">Quote Contact Information</h2>
                <p class="mt-1 text-sm leading-6 text-gray-600">
                    Enter the contact information for the quote. This is the person who will receive the quote.
                </p>

                <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                    <!-- Email -->
                    <div class="sm:col-span-3">
                        <x-forms-field-with-description
                            label="Email"
                            field_name="quoteContactEmail"
                            description_text="Email address of the quote contact. Required field."
                        >
                            <div class="mt-2 flex rounded-md shadow-sm">
                                <x-forms-input
                                    type="email"
                                    name="quoteContact.email"
                                    id="quoteContact.email"
                                    value="{{ isset($quote_data['quoteContact']['email']) ? $quote_data['quoteContact']['email'] : '' }}"
                                    extra_attributes="required x-model='formData.quoteContact.email'"
                                />
                                <button type="button"
                                        class="relative -ml-px inline-flex items-center gap-x-1.5 rounded-r-md px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                                        @click="checkContactExists()"
                                        hx-get="api/quote-v3/check_contact"
                                        hx-target="#contact-check-result"
                                        hx-trigger="click"
                                        hx-vals="js:{email: formData.quoteContact.email}">
                                    Check
                                </button>
                            </div>
                        </x-forms-field-with-description>
                        <div id="contact-check-result" class="mt-2"></div>
                    </div>

                    <!-- First Name -->
                    <div class="sm:col-span-3">
                        <x-forms-field-with-description
                            label="First Name"
                            field_name="quoteContactFirstName"
                            description_text="First name. Required if contact doesn't exist or end customer is individual."
                        >
                            <x-forms-input
                                type="text"
                                name="quoteContact.firstName"
                                id="quoteContact.firstName"
                                value="{{ isset($quote_data['quoteContact']['firstName']) ? $quote_data['quoteContact']['firstName'] : '' }}"
                            />
                        </x-forms-field-with-description>
                    </div>

                    <!-- Last Name -->
                    <div class="sm:col-span-3">
                        <x-forms-field-with-description
                            label="Last Name"
                            field_name="quoteContactLastName"
                            description_text="Last name. Required if contact doesn't exist or end customer is individual."
                        >
                            <x-forms-input
                                type="text"
                                name="quoteContact.lastName"
                                id="quoteContact.lastName"
                                value="{{ isset($quote_data['quoteContact']['lastName']) ? $quote_data['quoteContact']['lastName'] : '' }}"
                            />
                        </x-forms-field-with-description>
                    </div>

                    <!-- Phone -->
                    <div class="sm:col-span-3">
                        <x-forms-field-with-description
                            label="Phone"
                            field_name="quoteContactPhone"
                            description_text="Phone number. Required if contact doesn't exist or end customer is individual."
                        >
                            <x-forms-input
                                type="tel"
                                name="quoteContact.phone"
                                id="quoteContact.phone"
                                value="{{ isset($quote_data['quoteContact']['phone']) ? $quote_data['quoteContact']['phone'] : '' }}"
                            />
                        </x-forms-field-with-description>
                    </div>
                </div>
            </div>

            <!-- Line Items Section -->
            <div class="border-b border-gray-900/10 pb-12">
                <h2 class="text-base font-semibold leading-7 text-gray-900">Line Items</h2>
                <p class="mt-1 text-sm leading-6 text-gray-600">
                    Add line items to your quote. You can add up to 250 line items.
                </p>

                <div class="mt-6">
                    <button type="button"
                            hx-get="api/quote-v3/add-line-item"
                            hx-target="#line-items-container"
                            hx-swap="beforeend"
                            hx-vals='{"index": {{ isset($quote_data["lineItems"]) ? count($quote_data["lineItems"]) : 0 }}}'
                            class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                        Add Line Item
                    </button>
                </div>

                <!-- Line Items Container -->
                <div id="line-items-container" class="mt-6 space-y-8">
                    @if(isset($quote_data['lineItems']) && is_array($quote_data['lineItems']))
                        @foreach($quote_data['lineItems'] as $index => $item)
                            <x-quote-v3-line-item :index="$index" :item="$item" />
                        @endforeach
                    @endif
                </div>
            </div>

            <!-- Submit Button -->
            <div class="mt-6 flex items-center justify-end gap-x-6">
                <x-forms-button
                    label="Cancel"
                    variant="secondary"
                    type="button"
                />
                <x-forms-button
                    label="Submit"
                    variant="primary"
                    type="submit"
                />
            </div>
        </div>
        </div>
    </form>
</div>
