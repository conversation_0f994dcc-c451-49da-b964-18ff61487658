@props([
    'offerings' => []
])

@if(count($offerings) > 0)
    <div class="mt-2 bg-white shadow overflow-hidden rounded-md">
        <ul class="divide-y divide-gray-200">
            @foreach($offerings as $offering)
                <li class="px-4 py-3 hover:bg-gray-50 cursor-pointer"
                    hx-post="api/quote-v3/select-offering"
                    hx-include="closest form"
                    hx-target="#offering-details"
                    hx-vals='{"id": "{{ $offering['id'] }}", "name": "{{ $offering['name'] }}", "code": "{{ $offering['code'] }}"}'>
                    <div class="flex justify-between">
                        <div class="text-sm font-medium text-indigo-600">{{ $offering['name'] }}</div>
                        <div class="text-sm text-gray-500">{{ $offering['id'] }} ({{ $offering['code'] }})</div>
                    </div>
                </li>
            @endforeach
        </ul>
    </div>
@else
    <div class="mt-2 text-sm text-gray-500">
        No offerings found. Please try a different search term.
    </div>
@endif
