@props([
    'config' => [],
    'replacements' => [],
    'db_fields' => []
])

<div class="space-y-6 bg-white px-4 py-5 sm:p-6">
    <h3 class="text-lg font-medium leading-6 text-gray-900">Subscription Table Configuration</h3>
    <form hx-post="{{ APP_ROOT }}api/save_subscription_config" hx-target="#config_status" class="space-y-4">
        <!-- Replacements Section -->
        <div x-data="{ open: true }" class="border rounded-lg overflow-hidden">
            <button @click="open = !open" type="button"
                    class="w-full px-4 py-2 text-left text-md font-medium text-gray-700 bg-gray-50 hover:bg-gray-100 flex justify-between items-center">
                <span>String Replacements</span>
                <svg :class="{'rotate-180': open}" class="w-5 h-5 transform transition-transform" fill="currentColor"
                     viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                          d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                          clip-rule="evenodd"/>
                </svg>
            </button>
            <div x-show="open" x-transition class="p-4 border-t">
                <div id="replacements_container" class="space-y-2">
                    @foreach($replacements as $search => $replace)
                        <x-subscription-table-config-replacement-row :search="$search" :replace="$replace" />
                    @endforeach
                </div>
                <button type="button"
                        class="mt-2 inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                        hx-post="{{ APP_ROOT }}api/add_replacement_row"
                        hx-target="#replacements_container"
                        hx-swap="beforeend">
                    Add Replacement
                </button>
            </div>
        </div>

        <!-- Columns Configuration -->
        <div x-data="{ open: true }" class="border rounded-lg overflow-hidden">
            <button @click="open = !open" type="button"
                    class="w-full px-4 py-2 text-left text-md font-medium text-gray-700 bg-gray-50 hover:bg-gray-100 flex justify-between items-center">
                <span>Columns Configuration</span>
                <svg :class="{'rotate-180': open}" class="w-5 h-5 transform transition-transform" fill="currentColor"
                     viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                          d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                          clip-rule="evenodd"/>
                </svg>
            </button>
            <div x-show="open" x-transition class="p-4 border-t">
                <div id="columns_container" class="space-y-4">
                    @foreach($config['columns'] as $key => $column)
                        <x-subscription-table-config-column 
                            :index="$loop->index"
                            :key="$key"
                            :column="$column"
                            :db_fields="$db_fields"
                        >
                    @endforeach
                </div>
                <button type="button"
                        class="mt-2 inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                        hx-post="{{ APP_ROOT }}api/add_column_row"
                        hx-target="#columns_container"
                        hx-swap="beforeend"
                        hx-vals='{"index": "{{ count($config['columns']) }}"}'>
                    Add Column
                </button>
            </div>
        </div>

        <div class="flex justify-end">
            <button type="submit"
                    class="ml-3 inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                Save Configuration
            </button>
        </div>
    </form>
    <div id="config_status"></div>
</div>

<script>
    // Initialize Sortable for replacements
    new Sortable(document.getElementById('replacements_container'), {
        handle: '.drag-handle',
        animation: 150
    });

    // Initialize Sortable for columns
    new Sortable(document.getElementById('columns_container'), {
        handle: '.drag-handle',
        animation: 150
    });

    function insertField(selectElement) {
        const inputElement = selectElement.previousElementSibling;
        const selectedValue = selectElement.value;

        if (!selectedValue) return;

        let currentValue = inputElement.value.trim();
        inputElement.value = currentValue
            ? currentValue + ', ' + selectedValue
            : selectedValue;

        selectElement.value = '';
    }
</script>
