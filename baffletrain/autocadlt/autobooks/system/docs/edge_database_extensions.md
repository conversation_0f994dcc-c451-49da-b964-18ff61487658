# Edge Template Database Extensions

The Edge template engine has been extended to support database query operations directly within templates. This provides a convenient way to query data without requiring PHP code blocks.

## Available Database Directives

### @db() - Raw Database Query
Execute a raw SQL query and store the result in `$db_result`.

```php
@db("SELECT * FROM users WHERE active = 1")
@foreach($db_result as $user)
    <p>{{ $user['name'] }}</p>
@endforeach
```

### @table() - Database Table Reference
Create a database table reference and store it in `$db_table`.

```php
@table("autobooks_navigation")
@get($db_table->where('parent_path', 'root'))
@foreach($db_data as $item)
    <p>{{ $item['name'] }}</p>
@endforeach
```

### @get() - Execute Query
Execute a query builder and store results in `$db_data`.

```php
@table("autobooks_users_data")
@get($db_table->limit(10))
@foreach($db_data as $row)
    <div>{{ json_decode($row['data_json'], true)['name'] ?? 'Unknown' }}</div>
@endforeach
```

## Hilt Template Integration

The database extensions work seamlessly with hilt templates for database-driven content:

```php
@props([
    'table_name' => 'autobooks_products_data',
    'search' => ''
])

@table($table_name)
@if(!empty($search))
    @get($db_table->where('data_json', 'LIKE', '%' . $search . '%')->limit(50))
@else
    @get($db_table->limit(50))
@endif

<div class="data-grid">
    @foreach($db_data as $item)
        @php
        $data = json_decode($item['data_json'], true);
        @endphp
        
        <div class="item">
            <h3>{{ $data['name'] ?? 'Untitled' }}</h3>
            <p>{{ $data['description'] ?? '' }}</p>
        </div>
    @endforeach
</div>
```

## Security Considerations

- Raw SQL queries (@db) should be used carefully to avoid SQL injection
- Use parameterized queries when possible
- The table and get directives use the existing database class which provides protection
- Always validate user input before using in database queries

## Examples

### Simple Data Display
```php
@table("autobooks_news_data")
@get($db_table->orderBy('created_at', 'desc')->limit(5))

<div class="news-list">
    @foreach($db_data as $article)
        @php $news = json_decode($article['data_json'], true); @endphp
        <article>
            <h2>{{ $news['title'] }}</h2>
            <p>{{ $news['summary'] }}</p>
            <time>{{ $article['created_at'] }}</time>
        </article>
    @endforeach
</div>
```

### Search Functionality
```php
@props(['search_term' => ''])

@table("autobooks_products_data")
@if($search_term)
    @get($db_table->where('data_json', 'LIKE', '%' . $search_term . '%'))
@else
    @get($db_table->limit(20))
@endif

<div class="search-results">
    <p>Found {{ count($db_data) }} results</p>
    @foreach($db_data as $product)
        @php $item = json_decode($product['data_json'], true); @endphp
        <div class="product">
            <h3>{{ $item['name'] }}</h3>
            <p>Price: ${{ $item['price'] }}</p>
        </div>
    @endforeach
</div>
```

### Pagination Support
```php
@props([
    'page' => 1,
    'per_page' => 10
])

@php
$offset = ($page - 1) * $per_page;
@endphp

@table("autobooks_events_data")
@get($db_table->limit($per_page)->offset($offset))

<div class="events">
    @foreach($db_data as $event)
        @php $evt = json_decode($event['data_json'], true); @endphp
        <div class="event">
            <h3>{{ $evt['title'] }}</h3>
            <p>{{ $evt['date'] }}</p>
        </div>
    @endforeach
</div>
```

## Variable Names

The database directives create the following variables:

- `$db_result` - Result from @db() raw queries
- `$db_table` - Database table reference from @table()
- `$db_data` - Query results from @get()

These variables can be used throughout the template after the directive is called.

## Error Handling

Database errors are handled gracefully:
- Failed queries return empty arrays
- Errors are logged to the system log
- Templates continue to render with empty data sets

## Performance Notes

- Database queries are executed when the directive is encountered
- Results are stored in memory for the duration of the template render
- Use LIMIT clauses to avoid loading large datasets
- Consider caching for frequently accessed data

## Migration from PHP Blocks

Old PHP approach:
```php
<?php
$users = database::table('users')->where('active', 1)->get();
?>
@foreach($users as $user)
    <p>{{ $user['name'] }}</p>
@endforeach
```

New Edge directive approach:
```php
@table("users")
@get($db_table->where('active', 1))
@foreach($db_data as $user)
    <p>{{ $user['name'] }}</p>
@endforeach
```

The new approach is more consistent with Edge template syntax and provides better integration with the template compilation system.
