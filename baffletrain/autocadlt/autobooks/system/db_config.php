<?php
/**
 * Database Configuration
 * Automatically detects local vs remote environment
 */

// Detect if we're running locally
$is_local = (
    // Check if running on localhost
    (isset($_SERVER['SERVER_NAME']) && (
        $_SERVER['SERVER_NAME'] === 'localhost' ||
        $_SERVER['SERVER_NAME'] === '127.0.0.1' ||
        strpos($_SERVER['SERVER_NAME'], 'localhost') !== false
    )) ||
    // Check if running from command line (CLI)
    (php_sapi_name() === 'cli') ||
    // Check if no SERVER_NAME is set (common in CLI/local scripts)
    !isset($_SERVER['SERVER_NAME']) ||
    // Check if running on local IP ranges
    (isset($_SERVER['SERVER_ADDR']) && (
        strpos($_SERVER['SERVER_ADDR'], '127.') === 0 ||
        strpos($_SERVER['SERVER_ADDR'], '192.168.') === 0 ||
        strpos($_SERVER['SERVER_ADDR'], '10.') === 0
    ))
);

if ($is_local) {
    // Local XAMPP configuration
    $db_server = '127.0.0.1';
    $db_username = 'root';
    $db_password = ''; // XAMPP default is empty password
    $db_database = 'autobooks_local';
    $tcs_database = 'autobooks_local';

    // Debug info for local development (remove if not needed)
    if (defined('DEBUG_DB') && DEBUG_DB) {
        error_log("Using LOCAL database: $db_database on $db_server");
    }
} else {
    // Production/Remote configuration
    $tcs_database = str_replace('.', '', DOMAIN ?? 'wwwcadservicescouk');
    $db_server = 'localhost';
    $db_username = $tcs_database;
    $db_password = 'S96#1kvYuCGE';
    $db_database = $tcs_database;

    // Debug info for production (remove if not needed)
    if (defined('DEBUG_DB') && DEBUG_DB) {
        error_log("Using REMOTE database: $db_database on $db_server");
    }
}

// Optional: Add a comment to HTML output for debugging
if ($is_local && isset($_SERVER['REQUEST_METHOD'])) {
    echo "<!-- LOCAL DEV MODE: Using database '$db_database' on '$db_server' -->\n";
}
?>
