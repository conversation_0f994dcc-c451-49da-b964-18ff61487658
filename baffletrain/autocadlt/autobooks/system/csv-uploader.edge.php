@props([
    'title' => 'CSV Uploader',
    'description' => 'Upload and display CSV data',
    'endpoint' => '/api/csv/upload', // API endpoint for uploading CSV
    'max_file_size' => 5, // Maximum file size in MB
])

<div x-data="{
    csvData: '',
    fileName: '',
    isUploading: false,
    hasHeader: true,
    delimiter: ',',
    error: '',
    
    handleFileUpload(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        this.fileName = file.name;
        this.error = '';
        
        // Check file size
        if (file.size > this.maxFileSize * 1024 * 1024) {
            this.error = `File size exceeds the maximum allowed size of ${this.maxFileSize}MB`;
            return;
        }
        
        // Check file type
        if (!file.name.endsWith('.csv') && !file.type.includes('csv')) {
            this.error = 'Please upload a CSV file';
            return;
        }
        
        const reader = new FileReader();
        reader.onload = (e) => {
            this.csvData = e.target.result;
        };
        reader.readAsText(file);
    },
    
    maxFileSize: {{ $max_file_size }}
}" class="space-y-6">
    <!-- File Upload Section -->
    <div class="space-y-4">
        <div class="flex items-center justify-between">
            <h3 class="text-base font-semibold leading-6 text-gray-900">Upload CSV File</h3>
        </div>
        
        <div class="mt-2">
            <label for="csv-file" class="block text-sm font-medium text-gray-700">
                Select CSV file
            </label>
            <div class="mt-1 flex items-center space-x-4">
                <label class="block w-full">
                    <span class="sr-only">Choose CSV file</span>
                    <input 
                        type="file" 
                        id="csv-file"
                        accept=".csv,text/csv"
                        @change="handleFileUpload"
                        class="block w-full text-sm text-gray-500
                               file:mr-4 file:py-2 file:px-4
                               file:rounded-md file:border-0
                               file:text-sm file:font-semibold
                               file:bg-indigo-50 file:text-indigo-700
                               hover:file:bg-indigo-100"
                    />
                </label>
                <span x-text="fileName" class="text-sm text-gray-500"></span>
            </div>
            <p class="mt-1 text-sm text-gray-500">
                Maximum file size: {{ $max_file_size }}MB
            </p>
            <div x-show="error" x-text="error" class="mt-2 text-sm text-red-600"></div>
        </div>
        
        <!-- CSV Options -->
        <div class="mt-4 space-y-4">
            <div class="flex items-center">
                <input 
                    id="has-header" 
                    type="checkbox" 
                    x-model="hasHeader"
                    class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                />
                <label for="has-header" class="ml-2 block text-sm text-gray-900">
                    First row contains headers
                </label>
            </div>
            
            <div>
                <label for="delimiter" class="block text-sm font-medium text-gray-700">
                    Delimiter
                </label>
                <select 
                    id="delimiter" 
                    x-model="delimiter"
                    class="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                >
                    <option value=",">Comma (,)</option>
                    <option value=";">Semicolon (;)</option>
                    <option value="\t">Tab</option>
                    <option value="|">Pipe (|)</option>
                </select>
            </div>
        </div>
    </div>
    
    <!-- Preview Section -->
    <div x-show="csvData" class="space-y-4">
        <div class="flex items-center justify-between">
            <h3 class="text-base font-semibold leading-6 text-gray-900">CSV Data Preview</h3>
        </div>
        
        <div class="mt-2">
            <x-templates-datatable-csv 
                :csv_data="''"
                x-bind:csv_data="csvData"
                x-bind:has_header="hasHeader"
                x-bind:delimiter="delimiter"
                :items_per_page="10"
            />
        </div>
    </div>
</div>
