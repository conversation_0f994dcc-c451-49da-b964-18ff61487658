<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2014 osCommerce

  Released under the GNU General Public License


// define our webserver variables
// FS = Filesystem (physical)
// WS = Webserver (virtual)

 $url = $_SERVER['SERVER_NAME'];


  //echo $url;
  define('HTTP_SERVER', 'https://' .$url); // eg, http://localhost or - https://localhost should not be NULL for productive servers
  define('HTTPS_SERVER', 'https://' .$url);
  define('ENABLE_SSL', true);
  define('HTTP_COOKIE_DOMAIN', '');
  define('HTTPS_COOKIE_DOMAIN', '');
  define('HTTP_COOKIE_PATH', '/');
  define('HTTPS_COOKIE_PATH', '/');
  define('HTTP_CATALOG_SERVER', 'https://' .$url);
  define('HTTPS_CATALOG_SERVER', 'https://' .$url);
  define('ENABLE_SSL_CATALOG', true); // secure webserver for catalog module
  if ($url == "www.cadservices.co.uk"){
    define('DIR_FS_DOCUMENT_ROOT', '/var/www/vhosts/cadservices.co.uk/httpdocs/');
}else{  
  define('DIR_FS_DOCUMENT_ROOT', '/var/www/vhosts/cadservices.co.uk/' . $url . '/'); // where your pages are located on the server. if $DOCUMENT_ROOT doesnt suit you, replace with your local path. (eg, /usr/local/apache/htdocs)
}

     
  //define('DIR_FS_DOCUMENT_ROOT', '/var/www/vhosts/cadservices.co.uk/httpdocs/'); // where your pages are located on the server. if $DOCUMENT_ROOT doesnt suit you, replace with your local path. (eg, /usr/local/apache/htdocs)
  define('DIR_WS_ADMIN', '/baffletrain/autocadlt/');
  define('DIR_WS_HTTPS_ADMIN', '/baffletrain/autocadlt/');
  define('DIR_FS_ADMIN', DIR_FS_DOCUMENT_ROOT . DIR_WS_ADMIN);
  define('DIR_WS_CATALOG', '/');
  define('DIR_WS_HTTPS_CATALOG', '/');
  define('DIR_FS_CATALOG', str_replace('//', '/', DIR_FS_DOCUMENT_ROOT . DIR_WS_CATALOG));
  //define('DIR_FS_CATALOG', dirname($_SERVER['SCRIPT_FILENAME']) . '/');
  define('DIR_FS_DOWNLOAD', DIR_FS_CATALOG . 'download/');
  define('DIR_FS_DOWNLOAD_PUBLIC', DIR_FS_CATALOG . 'pub/');
  define('DIR_WS_CATALOG_IMAGES', DIR_WS_CATALOG . 'images/');
  define('DIR_WS_CATALOG_LANGUAGES', DIR_WS_CATALOG . 'resourcess/languages/');
  define('DIR_FS_CATALOG_LANGUAGES', DIR_FS_CATALOG . 'resourcess/languages/');
  define('DIR_FS_CATALOG_IMAGES', DIR_FS_CATALOG . 'images/');
  define('DIR_FS_CATALOG_MODULES', DIR_FS_CATALOG . 'resourcess/modules/');
  define('DIR_FS_BACKUP', DIR_FS_ADMIN . 'backups/');


  define ("DIR_WS_AUTOBOOKS", "baffletrain/autocadlt/autobooks/");
  define ("DIR_FS_AUTOBOOKS", DIR_FS_CATALOG . "baffletrain/autocadlt/");

  define('DIR_FS_CACHE','/var/www/vhosts/cadservices.co.uk/temp/' . $url . '/');

 $tcsdatabase = str_replace('.','',$url);

  define('DB_SERVER', 'localhost');
  
  define('DB_SERVER_USERNAME', $tcsdatabase);
  define('DB_SERVER_PASSWORD', 'S96#1kvYuCGE');
  define('DB_DATABASE', $tcsdatabase);
  define('USE_PCONNECT', 'false');
  define('STORE_SESSIONS', 'mysql');
  define('CFG_TIME_ZONE', 'UTC');
       
$nav_items = [
  'Dashboard',
  'Quotes',
  'Subscriptions',
  'Customers',
  'Users',
  'Test'
];
$routes = [
  'dashboard' => 'resources/views/dashboard.php',
  'quotes' => 'resources/views/quotes.php',
  'subscriptions' => 'resources/views/subscriptions.php',
  'customers' => 'resources/views/customers.php',
  'users' => 'resources/views/users/users.php',
  'test' => 'resources/views/test.php'
];
*/
?>
