<?php
/**
 * API endpoint for updating Autodesk catalog hashes
 */
namespace api\admin;

use DataImporter;

// Check for API authentication
if (!api_is_authenticated() || !api_user_has_permission('admin')) {
    return api_error('Unauthorized', 401);
}

/**
 * Update Autodesk catalog hashes
 * 
 * @param array $p Parameters (optional)
 * @return array API response
 */
function update_autodesk_hashes($p = []) {
    // Include necessary files
    require_once(DOC_ROOT . '/resources/classes/data_importer.class.php');
    
    // Define the fields used to generate the unique hash
    // These should match the fields used in the import process
    $unique_hash_fields = [
        'offeringId', 
        'intendedUsage_code', 
        'accessModel_code', 
        'servicePlan_code', 
        'connectivity_code', 
        'term_code', 
        'orderAction', 
        'specialProgramDiscount_code', 
        'fromQty', 
        'toQty'
    ];
    
    // Set batch size from parameters or use default
    $batch_size = isset($p['batch_size']) ? (int)$p['batch_size'] : 100;
    
    // Run the update process
    $result = DataImporter::update_autodesk_catalog_hashes($unique_hash_fields, $batch_size);
    
    // Return the result
    if (isset($result['error'])) {
        return [
            'status' => 'error',
            'message' => $result['error'],
            'data' => $result
        ];
    } else {
        return [
            'status' => 'success',
            'message' => 'Hash update completed successfully',
            'data' => $result
        ];
    }
}
