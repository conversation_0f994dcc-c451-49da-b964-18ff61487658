<?php
namespace api\test_notifications;

use system\notifications;
use system\users;

/**
 * Add test notifications for the current user
 *
 * @return string Success message
 */
function add_test_notifications() {
    $user = users::checkAuth();
    if (!$user) {
        return json_encode(['error' => 'User not authenticated']);
    }

    // Add some test notifications
    $notifications = [
        [
            'type' => 'system',
            'title' => 'Welcome to the notification system',
            'message' => 'This is a test notification to demonstrate the notification system.',
            'link' => APP_ROOT . 'notifications'
        ],
        [
            'type' => 'order',
            'title' => 'New order received',
            'message' => 'You have received a new order #12345.',
            'link' => APP_ROOT . 'orders/12345'
        ],
        [
            'type' => 'quote',
            'title' => 'Quote approved',
            'message' => 'Your quote #54321 has been approved by the customer.',
            'link' => APP_ROOT . 'quotes/54321'
        ],
        [
            'type' => 'subscription',
            'title' => 'Subscription expiring soon',
            'message' => 'Your subscription will expire in 7 days. Please renew to avoid service interruption.',
            'link' => APP_ROOT . 'subscriptions'
        ],
        [
            'type' => 'customer',
            'title' => 'New customer registered',
            'message' => 'A new customer has registered on your platform.',
            'link' => APP_ROOT . 'customers'
        ]
    ];

    $notification_ids = [];
    foreach ($notifications as $notification) {
        $notification_id = add_notification_for_current_user(
            $notification['type'],
            $notification['title'],
            $notification['message'],
            $notification['link']
        );

        if ($notification_id) {
            $notification_ids[] = $notification_id;
        }
    }

    return json_encode([
        'success' => true,
        'message' => 'Added ' . count($notification_ids) . ' test notifications',
        'notification_ids' => $notification_ids
    ]);
}

/**
 * Clear all notifications for the current user
 *
 * @return string Success message
 */
function clear_notifications() {
    $user = users::checkAuth();
    if (!$user) {
        return json_encode(['error' => 'User not authenticated']);
    }

    global $db;
    $query = "DELETE FROM autobooks_notifications WHERE user_id = :user_id";
    $result = tep_db_query($query, null, [':user_id' => $user['id']]);

    return json_encode([
        'success' => true,
        'message' => 'Cleared all notifications for the current user'
    ]);
}
