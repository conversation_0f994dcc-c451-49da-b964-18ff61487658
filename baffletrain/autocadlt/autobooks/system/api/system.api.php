<?php
namespace api\system;

use system\users;
use edge\Edge;
use const icons\ICONS;
use DB;

/**
 * Display the form for adding a new navigation entry
 */
function add_nav_entry() {
    // Ensure only admin/dev can modify navigation
    users::requireRole(['admin', 'dev']);

    // Get the parent path from the request
    $parent_path = $_POST['parent_path'] ?? '';

    // Render the modal form for adding a new navigation entry
    return Edge::render('nav-entry-form', [
        'parent_path' => $parent_path,
        'icons' => ICONS
    ]);
}

/**
 * Save a new navigation entry to the database
 */
function save_nav_entry() {
    // Ensure only admin/dev can modify navigation
    users::requireRole(['admin', 'dev']);
    
    // Get form data
    $parent_path = $_POST['parent_path'] ?? '';
    $key = $_POST['key'] ?? '';
    $name = $_POST['name'] ?? '';
    $icon = $_POST['icon'] ?? 'document';
    $required_roles = $_POST['required_roles'] ?? [];
    
    // Validate required fields
    if (empty($key) || empty($name)) {
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "Key and name are required"}}');
        return;
    }
    
    // Convert required_roles array to JSON for storage
    $required_roles_json = json_encode($required_roles);
    
    // Insert the new navigation entry into the database
    $db = DB::getInstance();
    
    // Check if the navigation table exists, create it if not
    $db->query("
        CREATE TABLE IF NOT EXISTS navigation (
            id INT AUTO_INCREMENT PRIMARY KEY,
            parent_path VARCHAR(255) NOT NULL,
            route_key VARCHAR(255) NOT NULL,
            name VARCHAR(255) NOT NULL,
            icon VARCHAR(50) NOT NULL,
            required_roles JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // Insert the new entry
    $stmt = $db->prepare("
        INSERT INTO navigation (parent_path, route_key, name, icon, required_roles)
        VALUES (?, ?, ?, ?, ?)
    ");
    
    $stmt->bind_param("sssss", $parent_path, $key, $name, $icon, $required_roles_json);
    $result = $stmt->execute();
    
    if ($result) {
        header('HX-Trigger: {"showNotification": {"type": "success", "message": "Navigation entry added successfully"}}');
    } else {
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "Failed to add navigation entry"}}');
    }
    
    // Redirect to refresh the navigation
    header('HX-Redirect: ' . APP_ROOT);
}

/**
 * Get navigation items from the database
 * 
 * @param string $parent_path The parent path to get items for
 * @return array Navigation items
 */
function get_navigation_items($parent_path = '') {
    $db = DB::getInstance();
    
    // Check if the navigation table exists
    $result = $db->query("SHOW TABLES LIKE 'navigation'");
    if ($result->num_rows == 0) {
        // Table doesn't exist yet, return empty array
        return [];
    }
    
    // Get navigation items for the given parent path
    $stmt = $db->prepare("
        SELECT route_key, name, icon, required_roles
        FROM navigation
        WHERE parent_path = ?
        ORDER BY name ASC
    ");
    
    $stmt->bind_param("s", $parent_path);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $items = [];
    while ($row = $result->fetch_assoc()) {
        $key = $row['route_key'];
        $items[$key] = [
            'name' => $row['name'],
            'icon' => $row['icon'],
            'required_roles' => json_decode($row['required_roles'], true)
        ];
        
        // Check for sub-items
        $sub_path = $parent_path . $key . '/';
        $sub_items = get_navigation_items($sub_path);
        if (!empty($sub_items)) {
            $items[$key]['sub_folder'] = $sub_items;
        }
    }
    
    return $items;
}
