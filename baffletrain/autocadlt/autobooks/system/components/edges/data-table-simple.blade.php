@props([
    'title' => 'Users',
    'description' => 'A list of all the users in your account including their name, title, email and role.',
    'items' => [], // data array of items
    'id_count' => edge::id_count(),
    'columns' => [[
            'label' => 'Name',
            'field' => 'name',
            'filter' => false,
            'extra_parameters' => ''
     ]], // An array of column definitions: ['label' => 'Name', 'field' => 'name', 'filter' => false]
    'rows' => [
       'id_prefix' => 'row_',
       'id_field' => 'id',
       'class_postfix' => '',
       'extra_parameters' => ''
    ],// An array of row parameters such as ids,classes  etc
    'just_body' => false, // just return body
    'just_rows' => false, // just return rows
    'items_per_page' => 30, //max items to display before pagination
    'current_page_num' => 1,
    'class' => '', //extra classes
    'sort_column' => '',
    'sort_direction' => '',
    'auto_update' => false,
    'callback' => null,
    ''
])
<?php print_rr($columns,'columns poo');
print_rr($callback,'callybackA');
?>
@if (!$just_body && !$just_rows)

@if($auto_update)
    <x-forms-input
       type='hidden'
       name='last_update'
       :value='date("Y-m-d H:i:s")'
       hx-post='api/system/update'
       hx-swap='outerHTML'
       hx-trigger='every 10s'
    />
@endif
    <input type="hidden" class="data_table_filter" name="callback" value="{{ $callback }}">
    <table class="min-w-full border-collapse search_target data_table {{ $class }}" >
        <thead>
            <tr>
                @foreach($columns as $col)
                    <th scope="col" class="{{ $loop->first ? 'relative sticky top-0 border-b border-gray-300 bg-gray-200 py-1.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 lg:pl-8' : 'relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell' }}" style="isolation: isolate;">
                        <x-data-table-filter
                           :label="$col['label']"
                           :col="$col"
                           :sort_col="$sort_column"
                           :sort_dir="$sort_direction"
                           :callback="$callback"
                           :id_count="$id_count"
                        ></x-data-table-filter>
                    </th>
                @endforeach
            </tr>
        </thead>
@endif <!-- just_body & rows -->
@if (!$just_rows)
<tbody class="bg-white data_table_body">
@endif <!-- just rows -->
    @foreach ($items as $item)
        @if ($loop->first)
           {{ print_rr($item,'itamage') }}
        @endif
        <tr class="border-t {{ $loop->first ? 'border-gray-300' : 'border-gray-200' }} {{ $rows['class_postfix'] }}" id="{{ $rows['id_prefix'] . $item[$rows['id_field']] . $rows['id_postfix'] }}" {{ $rows['extra_parameters'] }}>
            @foreach ($columns as $col)
                @if ($col['replacements'])
                    @php $item[$col['field']] = str_replace(array_keys($col['replacements']),$col['replacements'],$item[$col['field']]) @endphp
                @endif
                <td class="{{ $loop->first ? 'whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8' : 'whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell' }}">
                    @if (isset($col['content']))
                        @if (is_callable($col['content']))
                            {!! $col['content']($item,$loop->first) !!}
                        @else
                            {!! $col['content'] ?? 'content' !!}
                        @endif
                    @elseif (isset($col['field']))
                        @if (is_array($col['field']))
                            {!! implode('<br>', array_map(fn($f) => $item[$f] ?? '', $col['field'])) !!}
                        @elseif (is_string($col['field']))
                            {{ $item[$col['field']] ?? '' }}
                        @endif
                    @endif
                </td>
            @endforeach
        </tr>
        @if ($items_per_page > 1 && $loop->iteration > $items_per_page )
            @break
        @endif
    @endforeach
@if (!$just_rows)
</tbody>
@endif
@if (!$just_body && !$just_rows)
        <tfoot>
            <tr>
                <td colspan="{{ count($columns) }}">
                    <x-pagination-strip
                        :item_count="count($items)"
                        :items_per_page="$items_per_page"
                        :current_page="$current_page"
                    ></x-pagination-strip>
                </td>
            </tr>
        </tfoot>
    </table>
@endif
