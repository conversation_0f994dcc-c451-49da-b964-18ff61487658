@props([
'title' => 'Select',
'description' => 'Basic form select',
'data' => [], // An array of items.
'label' => '',
'name' => '',
'class' => '',
'class_suffix' => '',
'options' => [], // An array of options: ['label' => 'Name', 'value' => 'name']
'selected' => null, // The selected option ['label' => 'Name', 'value' => 'name']
])
<div>
  <div class="m-0 grid grid-cols-1">
    <select id="{{ $id ?? 'select' }}" name="{{ $name ?? 'select' }}" {{ $extra_attributes }} class="{{ $class ?? "col-start-1 row-start-1 w-full appearance-none rounded-md py-0 pr-6 pl-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 sm:text-sm/"}} {{ $class_suffix ?? '' }}">
      <option value="" {{ ($selected ?? null) == $label ? 'selected' : '' }}>{{ $label }}</option>
      @foreach($options ?? [] as $key => $value)
        <option value="{{ $key }}" {{ ($selected ?? null) == $key ? 'selected' : '' }}>{{ $value }}</option>
      @endforeach
    </select>
     </div>
</div>