{{-- File: /resources/components/listbox-label.blade.php --}}
@props(['labels', 'labelled', 'setLabelled'])

<div x-data="{ open: false }" @click.away="open = false" class="shrink-0">
    <label class="sr-only">Add a label</label>
    <div class="relative">
        <button
                @click="open = !open"
                type="button"
                class="relative inline-flex items-center whitespace-nowrap rounded-full bg-gray-50 px-2 py-2 text-sm font-medium text-gray-500 hover:bg-gray-100 sm:px-3">
            <svg
                    aria-hidden="true"
                    class="{{ $labelled['value'] === null ? 'text-gray-300' : 'text-gray-500' }} size-5 shrink-0 sm:-ml-1"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg">
                <path
                        fill-rule="evenodd"
                        d="M3 4a2 2 0 00-2 2v9a2 2 0 002 2h6a2 2 0 002-2V6a2 2 0 00-2-2H3zm2-1a3 3 0 013 3v9a3 3 0 01-3 3H3a3 3 0 01-3-3V6a3 3 0 013-3h2zm7 0a2 2 0 012 2v3a2 2 0 01-2 2H9a2 2 0 01-2-2V5a2 2 0 012-2h2zm2 5a2 2 0 012 2v3a2 2 0 01-2 2H11a2 2 0 01-2-2V7a2 2 0 012-2h2z"
                        clip-rule="evenodd"></path>
            </svg>
            <span
                    class="{{ $labelled['value'] === null ? '' : 'text-gray-900' }} hidden truncate sm:ml-2 sm:block">
    {{ $labelled['value'] === null ? 'Label' : $labelled['name'] }}
   </span>
        </button>

        <div
                x-show="open"
                x-transition:enter="transition ease-out duration-100"
                x-transition:enter-start="transform opacity-0 scale-95"
                x-transition:enter-end="transform opacity-100 scale-100"
                x-transition:leave="transition ease-in duration-75"
                x-transition:leave-start="transform opacity-100 scale-100"
                x-transition:leave-end="transform opacity-0 scale-95"
                class="absolute right-0 z-10 mt-1 max-h-56 w-52 overflow-auto rounded-lg bg-white py-3 text-base shadow outline outline-1 outline-black/5 sm:text-sm"
                style="display: none;"
                @click.away="open = false">
            @foreach ($labels as $label)
                <button
                        @click="$store.labelled = JSON.parse('{{ json_encode($label) }}'); open = false"
                        type="button"
                        class="cursor-default select-none bg-white px-3 py-2 data-[focus]:relative data-[focus]:bg-gray-100 data-[focus]:hover:outline-none w-full text-left">
                    <div class="flex items-center">
                        <span class="block truncate font-medium">{{ $label['name'] }}</span>
                    </div>
                </button>
            @endforeach
        </div>
    </div>
</div>
