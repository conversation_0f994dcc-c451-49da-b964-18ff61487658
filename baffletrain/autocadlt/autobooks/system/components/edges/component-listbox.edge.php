{{-- File: /resources/components/listbox.blade.php --}}
@props(['assignees', 'assigned', 'setAssigned'])

<div x-data="{ open: false }" @click.away="open = false" class="shrink-0">
    <label class="sr-only">Assign</label>
    <div class="relative">
        <button
                @click="open = !open"
                type="button"
                class="relative inline-flex items-center whitespace-nowrap rounded-full bg-gray-50 px-2 py-2 text-sm font-medium text-gray-500 hover:bg-gray-100 sm:px-3">
            @if ($assigned['value'] === null)
                <svg
                        aria-hidden="true"
                        class="size-5 shrink-0 text-gray-300 sm:-ml-1"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg">
                    <path
                            fill-rule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.914A5.005 5.005 0 0010 16a5.005 5.005 0 004.546-2.086A5 5 0 008 11z"
                            clip-rule="evenodd"></path>
                </svg>
            @else
                <img alt="" src="{{ $assigned['avatar'] }}" class="size-5 shrink-0 rounded-full" />
            @endif

            <span
                    class="{{ $assigned['value'] === null ? '' : 'text-gray-900' }} hidden truncate sm:ml-2 sm:block">
    {{ $assigned['value'] === null ? 'Assign' : $assigned['name'] }}
   </span>
        </button>

        <div
                x-show="open"
                x-transition:enter="transition ease-out duration-100"
                x-transition:enter-start="transform opacity-0 scale-95"
                x-transition:enter-end="transform opacity-100 scale-100"
                x-transition:leave="transition ease-in duration-75"
                x-transition:leave-start="transform opacity-100 scale-100"
                x-transition:leave-end="transform opacity-0 scale-95"
                class="absolute right-0 z-10 mt-1 max-h-56 w-52 overflow-auto rounded-lg bg-white py-3 text-base shadow outline outline-1 outline-black/5 sm:text-sm"
                style="display: none;"
                @click.away="open = false">
            @foreach ($assignees as $assignee)
                <button
                        wire:click="$set('assigned', {{ json_encode($assignee) }})"
                        type="button"
                        class="cursor-default select-none bg-white px-3 py-2 data-[focus]:relative data-[focus]:bg-gray-100 data-[focus]:hover:outline-none w-full text-left">
                    <div class="flex items-center">
                        @if (isset($assignee['avatar']))
                            <img alt="" src="{{ $assignee['avatar'] }}" class="size-5 shrink-0 rounded-full" />
                        @else
                            <svg
                                    aria-hidden="true"
                                    class="size-5 shrink-0 text-gray-400"
                                    fill="currentColor"
                                    viewBox="0 0 20 20"
                                    xmlns="http://www.w3.org/2000/svg">
                                <path
                                        fill-rule="evenodd"
                                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.914A5.005 5.005 0 0010 16a5.005 5.005 0 004.546-2.086A5 5 0 008 11z"
                                        clip-rule="evenodd"></path>
                            </svg>
                        @endif

                        <span class="ml-3 block truncate font-medium">{{ $assignee['name'] }}</span>
                    </div>
                </button>
            @endforeach
        </div>
    </div>
</div>
