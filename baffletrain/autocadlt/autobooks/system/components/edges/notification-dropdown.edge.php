@props([
    'title' => 'Notifications',
    'description' => 'Notification dropdown component',
])

<div x-data="{ open: false, unreadCount: 0 }"
     class="relative"
     @click.away="open = false"
     @keydown.escape.window="open = false"
     id="notification-dropdown">

    <!-- Notification button with badge -->
    <button type="button"
            class="relative -m-2.5 p-2.5 text-gray-400 hover:text-gray-500"
            @click="open = !open"
            aria-expanded="false"
            aria-haspopup="true">
        <span class="sr-only">View notifications</span>
        <?= icon('bell') ?>

        <!-- Notification badge -->
        <div id="notification-badge"
             hx-get="{{ APP_ROOT }}api/system/notifications/get_unread_count"
             hx-trigger="load, every 30s, notification-updated from:body"
             hx-swap="outerHTML">
            <!-- Badge will be loaded here -->
        </div>
    </button>

    <!-- Notification dropdown -->
    <div x-show="open"
         x-transition:enter="transition ease-out duration-100"
         x-transition:enter-start="transform opacity-0 scale-95"
         x-transition:enter-end="transform opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-75"
         x-transition:leave-start="transform opacity-100 scale-100"
         x-transition:leave-end="transform opacity-0 scale-95"
         class="absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
         role="menu"
         aria-orientation="vertical"
         tabindex="-1">

        <!-- Notification header -->
        <div class="px-4 py-2 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h3 class="text-sm font-semibold text-gray-900">Notifications</h3>
                <a href="{{ APP_ROOT }}notifications"
                   class="text-xs text-blue-600 hover:text-blue-800"
                   hx-get="{{ APP_ROOT }}notifications"
                   hx-target="#content_wrapper"
                   hx-push-url="true">
                    View all
                </a>
            </div>
        </div>

        <!-- Notification list -->
        <div id="notification-list-container"
             hx-get="{{ APP_ROOT }}api/system/notifications/get_notifications"
             hx-trigger="load, notification-updated from:body"
             hx-swap="innerHTML">
            <!-- Notification list will be loaded here -->
        </div>
    </div>
</div>

<!-- SSE connection for real-time notifications -->
<div hx-ext="sse"
     sse-connect="{{ APP_ROOT }}api/system/notifications/sse"
     sse-swap="none"
     class="hidden">
    <div sse-swap="count"
         hx-trigger="sse:count"
         @sse-message="
            const data = JSON.parse(event.detail.data);
            if (data.count > 0) {
                document.body.dispatchEvent(new Event('notification-updated'));
            }
         ">
    </div>
    <div sse-swap="notifications"
         hx-trigger="sse:notifications"
         @sse-message="
            document.body.dispatchEvent(new Event('notification-updated'));
         ">
    </div>
</div>
