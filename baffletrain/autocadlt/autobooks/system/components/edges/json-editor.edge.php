@props([
    'title' => 'JSON Editor',
    'description' => 'A component for editing JSON data',
    'json_data' => '{}',
    'schema' => [],
    'template_name' => '',
    'id' => 'json_editor_' . rand(0,10000),
    'class_suffix' => '',
    'height' => '500px',
    'readonly' => false
])

@php
    // If template_name is provided, load the template file
    if (!empty($template_name)) {
        $template_path = 'resources/templates/' . $template_name . '.json';
        if (file_exists($template_path)) {
            $json_data = file_get_contents($template_path);
        }
    }
    
    // Ensure we have valid JSON
    $json_object = json_decode($json_data);
    if (json_last_error() !== JSON_ERROR_NONE) {
        $json_data = '{}';
    } else {
        // Re-encode to ensure proper formatting
        $json_data = json_encode($json_object, JSON_PRETTY_PRINT);
    }
    
    // Prepare editor options
    $editor_options = [
        'mode' => $readonly ? 'view' : 'tree',
        'modes' => $readonly ? ['view'] : ['tree', 'code', 'form', 'text', 'preview'],
        'search' => true,
        'navigationBar' => true,
        'statusBar' => true,
        'mainMenuBar' => true,
        'history' => true
    ];
    
    // Convert options to JSON for JavaScript
    $editor_options_json = json_encode($editor_options);
@endphp

<div class="w-full {{ $class_suffix }}">
    @if (!empty($title))
        <h3 class="text-lg font-medium leading-6 text-gray-900 mb-2">{{ $title }}</h3>
    @endif
    
    <div id="{{ $id }}_container" class="border border-gray-300 rounded-md" style="height: {{ $height }}">
        <div id="{{ $id }}" class="w-full h-full"></div>
    </div>
    
    <textarea id="{{ $id }}_data" name="json_data" class="hidden">{{ $json_data }}</textarea>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Create the editor
            const container = document.getElementById('{{ $id }}');
            const options = {!! $editor_options_json !!};
            
            // Initialize the editor with the JSON data
            const editor = new JSONEditor(container, options);
            const initialJson = {!! $json_data !!};
            editor.set(initialJson);
            
            // Update the hidden textarea when the JSON changes
            editor.onChange = function() {
                try {
                    const json = editor.get();
                    document.getElementById('{{ $id }}_data').value = JSON.stringify(json);
                } catch (err) {
                    console.error('JSON parsing error:', err);
                }
            };
        });
    </script>
</div>
