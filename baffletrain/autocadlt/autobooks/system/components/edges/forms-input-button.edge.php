@props([
    'title' => 'form input control',
    'description' => 'Basic form select',
    'value', // An array of items.
    'label' => null,
    'type' => null,
    'name' => '',
    'hidden' => false,
    'placeholder' => '',
    'class_suffix' => ''
])
@if ($label)
    <label for="email" class="block text-sm/6 font-medium text-gray-900">{{ $label }}</label>
@endif
<input type="{{ $type ?? 'text' }}"
       name="{{ $name }}"
       id="{{ $id }}"
       class="rounded-l-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 {{ $class }}"
       placeholder="{{ $placeholder }}"
/><button type="button"
    {{ $extra_attributes }}
    class="relative -ml-px inline-flex items-center gap-x-1.5 rounded-r-md px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
    {{ $button_label }}
</button>