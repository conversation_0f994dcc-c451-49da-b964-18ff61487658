@props([
'title' => 'custom Select',
'description' => 'custom form select',
'data' => [], // An array of items.
'label' => '',
'name' => '',
'class_suffix' => '',
'options' => [], // An array of options: ['label' => 'Name', 'value' => 'name'],
'selected' => null, // The selected options ['label' => 'Name', 'value' => 'name'],
'multiple' => 0,
'id' => preg_replace('/[^a-zA-Z0-9_-]/','',$name)
])
<div class="group row-span-3 relative flex gap-x-6 rounded-lg p-2 hover:bg-gray-50">
    <div class="w-full" x-data="multiSelect{{ $id }}()">
        <div class="relative">
            <label for="multi-select" class="block text-sm font-medium text-gray-700 mb-1">Select
                options:</label>
            <div class="mt-1 relative">
                <input type="text"
                       class="relative w-full bg-white border border-gray-300 rounded-md shadow-sm pl-3 pr-10 py-2 text-left cursor-default focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm {{ $class_suffix }}"
                       x-model="selectedOptions.join(', ')"
                       name="{{ $name }}"
                       id="{{ $id }}"
                       {{ $extra_attributes }}
                       value="{{ $selected }}"
                >
                </input> <span class="block truncate"
                               ></span>
                <span class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">

                                        </span>
                <div class="max-h-60 overflow-auto">
                    {{-- Loop through the options and display them --}}
                    @php
                        print_rr($options,"optionywaptiony", false,true);
                    @endphp
                    @foreach($options as $key => $option)
                        <div @click="toggleOption('{{ $option }}')"
                             class="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-indigo-600 hover:text-white">
                            <span :class="{ 'font-semibold': selectedOptions.includes('{{ $option }}') }"
                                  class="block truncate">{{ $option }}</span>
                            <span x-show="selectedOptions.includes('{{ $option }}')"
                                  class="absolute inset-y-0 right-0 flex items-center pr-4 text-indigo-600 hover:text-white">
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                     fill="currentColor">
                                    <path fill-rule="evenodd"
                                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                          clip-rule="evenodd"/>
                                </svg>
                            </span>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <script>
        function multiSelect{{ $id }}() {
            return {
                open: false,
                selectedOptions: [{{ $selected ? '\'' . preg_replace('/\s?,\s?/','\',\'',$selected) . '\'' : '' }}],
                toggleOption(option) {
                    if (this.selectedOptions.includes(option)) {
                        this.selectedOptions = this.selectedOptions.filter(item => item !== option);
                    } else {
                        this.selectedOptions.push(option);
                    }
                }
            }
        }
    </script>
</div>