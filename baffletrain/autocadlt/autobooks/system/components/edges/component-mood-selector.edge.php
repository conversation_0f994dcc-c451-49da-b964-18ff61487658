@props([
    'title' => '',
    'description' => ''
])
{{-- Create a separate component for the mood selector --}}
{{-- resources/views/components/mood-selector.blade.php --}}
@php
    $moods = [
    ['name' => 'Excited', 'value' => 'excited', 'bgColor' => 'bg-red-500'],
    ['name' => 'Loved', 'value' => 'loved', 'bgColor' => 'bg-pink-400'],
    ['name' => 'Happy', 'value' => 'happy', 'bgColor' => 'bg-green-400'],
    ['name' => 'Sad', 'value' => 'sad', 'bgColor' => 'bg-yellow-400'],
    ['name' => 'Thumbsy', 'value' => 'thumbsy', 'bgColor' => 'bg-blue-500'],
    ['name' => 'I feel nothing', 'value' => null, 'bgColor' => 'bg-transparent'],
    ];
@endphp

<div x-data="{ open: false, selected: @entangle('mood') }">
    <button type="button" @click="open = !open" class="-m-2.5 flex size-10 items-center justify-center rounded-full text-gray-400 hover:text-gray-500">
        <svg class="size-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 100-2 1 1 0 000 2zm7-1a1 1 0 11-2 0 1 1 0 012 0zm-3.536 5.536a.75.75 0 001.061 0 3.5 3.5 0 00-4.95 0 .75.75 0 001.061 0 2 2 0 012.828 0z" clip-rule="evenodd" />
        </svg>
        <span class="sr-only">Add your mood</span>
    </button>

    <div x-show="open"
         x-transition:leave="transition ease-in duration-100"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="absolute bottom-10 z-10 -ml-6 w-60 rounded-lg bg-white py-3 text-base shadow ring-1 ring-black/5 focus:outline-none sm:ml-auto sm:w-64 sm:text-sm">
        @foreach ($moods as $mood)
            <button type="button"
                    @click="selected = '{{ $mood['value'] }}'; open = false"
                    class="relative w-full cursor-default select-none px-3 py-2 hover:bg-gray-100">
                <div class="flex items-center">
                    <div class="flex size-8 items-center justify-center rounded-full {{ $mood['bgColor'] }}">
                        {{-- Add appropriate SVG icon for each mood --}}
                    </div>
                    <span class="ml-3 block truncate font-medium">{{ $mood['name'] }}</span>
                </div>
            </button>
        @endforeach
    </div>
</div>
