<?php
/*
$Id$

osCommerce, Open Source E-Commerce Solutions
http://www.oscommerce.com

Copyright (c) 2015 osCommerce

Released under the GNU General Public License
*/

require('includes/application_top.php');

require('includes/classes/currencies.php');
$currencies = new currencies();
require('includes/functions/googleCategoryfunctions.php');

require('ext/Google/ProductsSample.php');

function tep_get_products_video_url($product_id, $language_id)
{
    $product_query = tep_db_query("select products_video_url from " . TABLE_PRODUCTS_DESCRIPTION . " where products_id = '" . (int) $product_id . "' and language_id = '" . (int) $language_id . "'");
    $product       = tep_db_fetch_array($product_query);
    
    return $product['products_video_url'];
}


function tep_get_models($models_array = '') // Function modified from tep_get_manufacturers()
{
    global $language, $first, $last;
    if (!is_array($models_array))
        $models_array = array();
    $models_query = tep_db_query("SELECT products_id,
                                         products_model 
                                  FROM " . TABLE_PRODUCTS . " 
                                  ORDER BY products_model");
    $count        = 0;
    while ($models = tep_db_fetch_array($models_query)) {
        if ($count == 0) {
            $first = $models['products_model'];
        }
        $models_array[] = array(
            'id' => $models['products_id'],
            'text' => $models['products_model']
        );
        $count++;
        $last = $models['products_model'];
    }
    
    return $models_array;
}
// end mark

$action = (isset($_GET['action']) ? $_GET['action'] : '');

if (@tep_not_null($action)) {
    // Ultimate SEO URLs v2.2d
    // If the action will affect the cache entries
    if (preg_match("/(insert|update|setflag)/i", $action))
        include_once('includes/reset_seo_cache.php');
    switch ($action) {
//sort order
      case 'beginsort':
        $sorting = true;
        break;

        
      case 'setsortorder':
	$count = 0;
        for ($i=0, $n=sizeof($_POST['products_id']); $i<$n; $i++) {
		echo '<br>' . $count . ': ';
		$count++;
		tep_set_product_sort_order($_POST['products_id'][$i], $_POST['sortorder'][$i], $_POST['productsprice'][$i]);
        }
        $sorting = false;	
	if(isset($_POST['search'])){
		tep_redirect(tep_href_link('categories2.php', 'search=' . $_POST['search']));
	} else { 
		tep_redirect(tep_href_link('categories2.php', 'cPath=' . $_POST['cPath']));
	}
        break;
//end sort order
        case 'setflag':
            if (($_GET['flag'] == '0') || ($_GET['flag'] == '1')) {
                if (isset($_GET['pID'])) {
                    tep_set_product_status($_GET['pID'], $_GET['flag']);
                }
                if (USE_CACHE == 'true') {
                    tep_reset_cache_block('categories');
                    tep_reset_cache_block('also_purchased');
                }
            }
            
            tep_redirect(tep_href_link('categories.php', 'cPath=' . $_GET['cPath'] . '&pID=' . $_GET['pID']));
            break;
	case 'setgflag':
            if (($_GET['flag'] == '0') || ($_GET['flag'] == '1')) {
                if (isset($_GET['pID'])) {
                    tep_set_product_google_status($_GET['pID'], $_GET['flag']);
                }
                if (USE_CACHE == 'true') {
                    tep_reset_cache_block('categories');
                    tep_reset_cache_block('also_purchased');
                }
            }
            
            tep_redirect(tep_href_link('categories.php', 'cPath=' . $_GET['cPath'] . '&pID=' . $_GET['pID']));
            break;
        case 'insert_category':
        case 'update_category':
            if (isset($_POST['categories_id']))
                $categories_id = tep_db_prepare_input($_POST['categories_id']);
            $sort_order = tep_db_prepare_input($_POST['sort_order']);
            
            $sql_data_array = array(
                'sort_order' => (int) $sort_order
            );
            
            /* Mark, stuff for setting up google categories*/
            $models_array = tep_get_models(); // Mark: for google categpries
            
            if (isset($_POST['categories_id'])) $categories_id = tep_db_prepare_input($_POST['categories_id']);
            $sort_order                                 = tep_db_prepare_input($_POST['sort_order']);
            $google_category                            = $_POST['google_category'];
            $google_category_baseline                   = $_POST['google_category_baseline'];
            $sql_data_array                             = array('sort_order' => $sort_order);
            $sql_data_array['google_category']          = $google_category;
            $sql_data_array['google_category_baseline'] = $google_category_baseline;
            //die(print_r($google_category));
           /* end mark*/
            if ($action == 'insert_category') {
                $insert_sql_data = array(
                    'parent_id' => $current_category_id,
                    'date_added' => 'now()'
                );
                
                $sql_data_array = array_merge($sql_data_array, $insert_sql_data);
                
                tep_db_perform(TABLE_CATEGORIES, $sql_data_array);
                
                $categories_id = tep_db_insert_id();
            } elseif ($action == 'update_category') {
                $update_sql_data = array(
                    'last_modified' => 'now()'
                );
                
                $sql_data_array = array_merge($sql_data_array, $update_sql_data);
                
                tep_db_perform(TABLE_CATEGORIES, $sql_data_array, 'update', "categories_id = '" . (int) $categories_id . "'");
            }
            
            $languages = tep_get_languages();
            for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                $categories_name_array            = $_POST['categories_name'];
                $categories_description_array     = $_POST['categories_description'];
                $categories_seo_description_array = $_POST['categories_seo_description'];
                $categories_seo_keywords_array    = $_POST['categories_seo_keywords'];
                $categories_seo_title_array       = $_POST['categories_seo_title'];
                
                $language_id = $languages[$i]['id'];
                
                $sql_data_array                               = array(
                    'categories_name' => tep_db_prepare_input($categories_name_array[$language_id])
                );
                $sql_data_array['categories_description']     = tep_db_prepare_input($categories_description_array[$language_id]);
                $sql_data_array['categories_seo_description'] = tep_db_prepare_input($categories_seo_description_array[$language_id]);
                $sql_data_array['categories_seo_keywords']    = tep_db_prepare_input($categories_seo_keywords_array[$language_id]);
                $sql_data_array['categories_seo_title']       = tep_db_prepare_input($categories_seo_title_array[$language_id]);
                
                if ($action == 'insert_category') {
                    $insert_sql_data = array(
                        'categories_id' => $categories_id,
                        'language_id' => $languages[$i]['id']
                    );
                    
                    $sql_data_array = array_merge($sql_data_array, $insert_sql_data);
                    
                    tep_db_perform(TABLE_CATEGORIES_DESCRIPTION, $sql_data_array);
                } elseif ($action == 'update_category') {
                    tep_db_perform(TABLE_CATEGORIES_DESCRIPTION, $sql_data_array, 'update', "categories_id = '" . (int) $categories_id . "' and language_id = '" . (int) $languages[$i]['id'] . "'");
                }
            }
            
            $categories_image = new upload('categories_image');
            $categories_image->set_destination(DIR_FS_CATALOG_IMAGES);
            
            if ($categories_image->parse() && $categories_image->save()) {
                tep_db_query("update " . TABLE_CATEGORIES . " set categories_image = '" . tep_db_input($categories_image->filename) . "' where categories_id = '" . (int) $categories_id . "'");
            }
            //delete image thumbnails
            foreach (glob(DIR_FS_CATALOG_IMAGES . 'thumbs/*/' . $categories_image->filename) as $filename) {
                //$firephp->log($filename, 'found deleting');
                @unlink($filename);
            }
            
            
            
            // end delete image thumbnails
            
            if (USE_CACHE == 'true') {
                tep_reset_cache_block('categories');
                tep_reset_cache_block('also_purchased');
            }
            
            tep_redirect(tep_href_link('categories.php', 'cPath=' . $cPath . '&cID=' . $categories_id));
            break;
        case 'delete_category_confirm':
            if (isset($_POST['categories_id'])) {
                $categories_id = tep_db_prepare_input($_POST['categories_id']);
                
                $categories      = tep_get_category_tree($categories_id, '', '0', '', true);
                $products        = array();
                $products_delete = array();
                
                for ($i = 0, $n = sizeof($categories); $i < $n; $i++) {
                    $product_ids_query = tep_db_query("select products_id from " . TABLE_PRODUCTS_TO_CATEGORIES . " where categories_id = '" . (int) $categories[$i]['id'] . "'");
                    
                    while ($product_ids = tep_db_fetch_array($product_ids_query)) {
                        $products[$product_ids['products_id']]['categories'][] = $categories[$i]['id'];
                    }
                }
                
                reset($products);
                while (list($key, $value) = each($products)) {
                    $category_ids = '';
                    
                    for ($i = 0, $n = sizeof($value['categories']); $i < $n; $i++) {
                        $category_ids .= "'" . (int) $value['categories'][$i] . "', ";
                    }
                    $category_ids = substr($category_ids, 0, -2);
                    
                    $check_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = '" . (int) $key . "' and categories_id not in (" . $category_ids . ")");
                    $check       = tep_db_fetch_array($check_query);
                    if ($check['total'] < '1') {
                        $products_delete[$key] = $key;
                    }
                }
                
                // removing categories can be a lengthy process
                tep_set_time_limit(0);
                for ($i = 0, $n = sizeof($categories); $i < $n; $i++) {
                    tep_remove_category($categories[$i]['id']);
                }
                
                reset($products_delete);
                while (list($key) = each($products_delete)) {
                    tep_remove_product($key);
                }
            }
            
            if (USE_CACHE == 'true') {
                tep_reset_cache_block('categories');
                tep_reset_cache_block('also_purchased');
            }
            
            tep_redirect(tep_href_link('categories.php', 'cPath=' . $cPath));
            break;
        case 'delete_product_confirm':
            if (isset($_POST['products_id']) && isset($_POST['product_categories']) && is_array($_POST['product_categories'])) {
                $product_id         = tep_db_prepare_input($_POST['products_id']);
                $product_categories = $_POST['product_categories'];
                
                for ($i = 0, $n = sizeof($product_categories); $i < $n; $i++) {
                    tep_db_query("delete from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = '" . (int) $product_id . "' and categories_id = '" . (int) $product_categories[$i] . "'");
                }
                
                $product_categories_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = '" . (int) $product_id . "'");
                $product_categories       = tep_db_fetch_array($product_categories_query);
                
                if ($product_categories['total'] == '0') {
                    tep_remove_product($product_id);
                }
            }
            
            if (USE_CACHE == 'true') {
                tep_reset_cache_block('categories');
                tep_reset_cache_block('also_purchased');
            }
            
            tep_redirect(tep_href_link('categories.php', 'cPath=' . $cPath));
            break;
        case 'move_category_confirm':
            if (isset($_POST['categories_id']) && ($_POST['categories_id'] != $_POST['move_to_category_id'])) {
                $categories_id = tep_db_prepare_input($_POST['categories_id']);
                $new_parent_id = tep_db_prepare_input($_POST['move_to_category_id']);
                
                $path = explode('_', tep_get_generated_category_path_ids($new_parent_id));
                
                if (in_array($categories_id, $path)) {
                    $messageStack->add_session(ERROR_CANNOT_MOVE_CATEGORY_TO_PARENT, 'error');
                    
                    tep_redirect(tep_href_link('categories.php', 'cPath=' . $cPath . '&cID=' . $categories_id));
                } else {
                    tep_db_query("update " . TABLE_CATEGORIES . " set parent_id = '" . (int) $new_parent_id . "', last_modified = now() where categories_id = '" . (int) $categories_id . "'");
                    
                    if (USE_CACHE == 'true') {
                        tep_reset_cache_block('categories');
                        tep_reset_cache_block('also_purchased');
                    }
                    
                    tep_redirect(tep_href_link('categories.php', 'cPath=' . $new_parent_id . '&cID=' . $categories_id));
                }
            }
            
            break;
        case 'move_product_confirm':
            $products_id   = tep_db_prepare_input($_POST['products_id']);
            $new_parent_id = tep_db_prepare_input($_POST['move_to_category_id']);
            
            $duplicate_check_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = '" . (int) $products_id . "' and categories_id = '" . (int) $new_parent_id . "'");
            $duplicate_check       = tep_db_fetch_array($duplicate_check_query);
            if ($duplicate_check['total'] < 1)
                tep_db_query("update " . TABLE_PRODUCTS_TO_CATEGORIES . " set categories_id = '" . (int) $new_parent_id . "' where products_id = '" . (int) $products_id . "' and categories_id = '" . (int) $current_category_id . "'");
            
            if (USE_CACHE == 'true') {
                tep_reset_cache_block('categories');
                tep_reset_cache_block('also_purchased');
            }
            
            tep_redirect(tep_href_link('categories.php', 'cPath=' . $new_parent_id . '&pID=' . $products_id));
            break;
        case 'insert_product':
        case 'update_product':
			if (isset($_GET['pID']))
                $products_id = tep_db_prepare_input($_GET['pID']);
            $products_date_available = tep_db_prepare_input($_POST['products_date_available']);
            
            $products_date_available = (date('Y-m-d') < $products_date_available) ? $products_date_available : 'null';
            
            $sql_data_array                  = array(
                'products_quantity' => (int) tep_db_prepare_input($_POST['products_quantity']),
                'products_model' => tep_db_prepare_input($_POST['products_model']),
                'products_price' => tep_db_prepare_input($_POST['products_price']),
                'products_date_available' => $products_date_available,
                'products_weight' => (float) tep_db_prepare_input($_POST['products_weight']),
                'products_status' => tep_db_prepare_input($_POST['products_status']),
		'products_google_status' => tep_db_prepare_i