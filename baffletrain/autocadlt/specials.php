<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2014 osCommerce

  Released under the GNU General Public License
*/

require('includes/application_top.php');

require('includes/classes/currencies.php');
require(DIR_FS_CATALOG . 'includes/functions/tcs_components.php');
$currencies = new currencies();

$action = (isset($_GET['action']) ? $_GET['action'] : '');

if (@tep_not_null($action)) {
  switch ($action) {
    case 'setflag':
      tep_set_specials_status($_GET['id'], $_GET['flag']);

      tep_redirect(tep_href_link('specials.php', (isset($_GET['page']) ? 'page=' . $_GET['page'] . '&' : '') . 'sID=' . $_GET['id']));
      break;
    case 'insert':
      $products_id = tep_db_prepare_input($_POST['products_id'] ?? $_POST['special_products_select']);
      $products_price = tep_db_prepare_input($_POST['products_price']);
      $specials_price = tep_db_prepare_input($_POST['specials_price']);
      $expdate = tep_db_prepare_input($_POST['expdate']);
      $variations_id = tep_db_prepare_input($_POST['variations_id']);

      if (substr($specials_price, -1) == '%') {
        $new_special_insert_query = tep_db_query("select products_id, products_price from products where products_id = '" . (int)$products_id . "'");
        $new_special_insert = tep_db_fetch_array($new_special_insert_query);

        $products_price = $new_special_insert['products_price'];
        $specials_price = ($products_price - (($specials_price / 100) * $products_price));
      }

      $expires_date = '';
      if (@tep_not_null($expdate)) {
        $expires_date = substr($expdate, 0, 4) . substr($expdate, 5, 2) . substr($expdate, 8, 2);
      }
      
      echo "insert into specials (products_id, specials_new_products_price, specials_date_added, expires_date, variations_id, status) values ('" . (int)$products_id . "', '" . tep_db_input($specials_price) . "', now(), " . (@tep_not_null($expires_date) ? "'" . tep_db_input($expires_date) . "'" : 'null') . "," . (int)$variations_id . ", '1')";

      tep_db_query("insert into specials (products_id, specials_new_products_price, specials_date_added, expires_date, variations_id, status) values ('" . (int)$products_id . "', '" . tep_db_input($specials_price) . "', now(), " . (@tep_not_null($expires_date) ? "'" . tep_db_input($expires_date) . "'" : 'null') . "," . (int)$variations_id . ", '1')");

      //tep_redirect(tep_href_link('specials.php', 'page=' . $_GET['page']));
      break;
    case 'update':
      $specials_id = tep_db_prepare_input($_POST['specials_id']);
      $products_price = tep_db_prepare_input($_POST['products_price']);
      $specials_price = tep_db_prepare_input($_POST['specials_price']);
      $expdate = tep_db_prepare_input($_POST['expdate']);
      $variations_id = tep_db_prepare_input($_POST['variations_id']);

      if (substr($specials_price, -1) == '%') $specials_price = ($products_price - (($specials_price / 100) * $products_price));

      $expires_date = '';
      if (@tep_not_null($expdate)) {
        $expires_date = substr($expdate, 0, 4) . substr($expdate, 5, 2) . substr($expdate, 8, 2);
      }
       tep_db_query("update specials set specials_new_products_price = '" . tep_db_input($specials_price) . "', specials_last_modified = now(), expires_date = " . (@tep_not_null($expires_date) ? "'" . tep_db_input($expires_date) . "'" : 'null') . ", variations_id = '" . (int)$variations_id . "'  where specials_id = '" . (int)$specials_id . "'");

      tep_redirect(tep_href_link('specials.php', 'page=' . $_GET['page'] . '&sID=' . $specials_id));
      break;
    case 'deleteconfirm':
      $specials_id = tep_db_prepare_input($_GET['sID']);

      tep_db_query("delete from specials where specials_id = '" . (int)$specials_id . "'");

      tep_redirect(tep_href_link('specials.php', 'page=' . $_GET['page']));
      break;
  }
}

require('includes/template_top.php');
?>

<table border="0" width="100%" cellspacing="0" cellpadding="2">
  <tr>
    <td width="100%">
      <table border="0" width="100%" cellspacing="0" cellpadding="0">
        <tr>
          <td class="pageHeading"><?php echo HEADING_TITLE; ?></td>
          <td class="pageHeading" align="right"><?php echo tep_draw_separator('pixel_trans.gif', HEADING_IMAGE_WIDTH, HEADING_IMAGE_HEIGHT); ?></td>
        </tr>
      </table>
    </td>
  </tr>
  <?php
  if (($action == 'new') || ($action == 'edit')) {
    $form_action = 'insert';
    if (($action == 'edit') && isset($_GET['sID'])) {
      $form_action = 'update';

      $product_query = tep_db_query("
          SELECT  p.products_id, 
                  pd.products_name, 
                  p.products_price, 
                  s.specials_new_products_price, 
                  s.expires_date,
                  s.variations_id,
                  v.model,
                  v.price
          FROM products p
          JOIN products_description pd ON p.products_id = pd.products_id
          JOIN specials s ON p.products_id = s.products_id
          LEFT JOIN products_variations v ON s.variations_id = v.products_variations_id
          WHERE 
              pd.language_id = '" . (int)$languages_id . "' 
              AND s.specials_id = '" . (int)$_GET['sID'] . "'
      ");
      $product = tep_db_fetch_array($product_query);

      $sInfo = new objectInfo($product);
    } else {
      $sInfo = new objectInfo(array());
      $specials_array = [];
      // create an array of products on special, which will be excluded from the pull down menu of products
      // (when creating a new product on special)
      // $specials_array = array();
      // $specials_query = tep_db_query("select p.products_id from products p, specials s where s.products_id = p.products_id");
      // while ($specials = tep_db_fetch_array($specials_query)) {
      //   $specials_array[] = $specials['products_id'];
      // }
    }
    $attribs = new tcs_product_attributes($sInfo->products_id);

    $variations = $attribs->get_variations();
    $variation_ops = [];
    print_rr($variations);
    foreach ($variations as $variation) {
      $variation_ops[$variation['products_variations_id']] = $variation['model'] . " ({$variation['price']})";
    }
    print_rr($sInfo);

    
      $product_select = tcs_draw_input_html('text','search_keywords','specialProductSearchinput','Search for a product','Search for a product',null,null,null,[
        "button1" => [
          'method' => 'post',
          'endpoint' => 'api_h.php',
          'target' =>'#special_products_select',
          'swap' => 'innerHTML',
          'text' => 'Search',
          'vals' => ["action" => "search_products"],
          'extra_params' => [
            'hx-trigger' => 'click',
            'hx-include' => '#specialProductSearchinput'
          ],
          'type' => 'button'
        ]
      ]) . 
      tcs_draw_select_html('special_products_select', 'special_products_select', [], null, [
        'hx-method' => 'post',
        'hx-endpoint' => 'api_h.php',
        'hx-target' => '#variations_select',
        'hx-trigger' => 'change',
        'hx-vals' =>[
          "action" => "specials_update_variation_select"
        ]
      ]);

  ?>
    <tr>
      <form name="new_special" <?php echo 'action="' . tep_href_link('specials.php', tep_get_all_get_params(array('action', 'info', 'sID')) . 'action=' . $form_action) . '"'; ?> method="post"><?php if ($form_action == 'update') echo tep_draw_hidden_field('specials_id', $_GET['sID']); ?>
        <td><br />
          <table border="0" cellspacing="0" cellpadding="2">
            <tr>
              <td class="main"><?php echo TEXT_SPECIALS_PRODUCT; ?>&nbsp;</td>
              <td class="main"><?php echo (isset($sInfo->products_name)) ? $sInfo->products_name . ' <small>(' . $currencies->format($sInfo->products_price) . ')</small>' : $product_select;
                                echo '<div id="products_price_field">' . tep_draw_hidden_field('products_price', (isset($sInfo->products_price) ? $sInfo->products_price : '')) . '</div>'; ?></td>
            </tr>
            <tr>
              <td class="main"><?php echo TEXT_SPECIALS_SPECIAL_PRICE; ?>&nbsp;</td>
              <td class="main"><?php echo tep_draw_input_field('specials_price', (isset($sInfo->specials_new_products_price) ? $sInfo->specials_new_products_price : '')); ?></td>
            </tr>
            <tr>
              <td class="main"><?php echo TEXT_SPECIALS_EXPIRES_DATE; ?>&nbsp;</td>
              <td class="main"><?php echo tep_draw_input_field('expdate', (@tep_not_null($sInfo->expires_date) ? substr($sInfo->expires_date, 0, 4) . '-' . substr($sInfo->expires_date, 5, 2) . '-' . substr($sInfo->expires_date, 8, 2) : ''), 'id="expdate"') . ' <small>(YYYY-MM-DD)</small>'; ?></td>
            </tr>
            <tr>
              <td class="main"><?php echo "Apply to variation:" ?>&nbsp;</td>
              <td class="main"><?= tcs_draw_select_html('variations_id', 'variations_select', $variation_ops, null, [
                    'hx-method' => 'post',
                    'hx-endpoint' => 'api_h.php',
                    'hx-target' => '#products_price_field',
                    'hx-trigger' => 'change',
                    'hx-vals' => [
                      "action" => "specials_update_products_price_field"
                ]
              ], $sInfo->variations_id);
             
               ?></td>
            </tr>

          </table>
          <?php



          ?>
          <script type="text/javascript">
            $('#expdate').datepicker({
              dateFormat: 'yy-mm-dd'
            });
          </script>

        </td>
    </tr>
    <tr>
      <td>
        <table border="0" width="100%" cellspacing="0" cellpadding="2">
          <tr>
            <td class="main"><br /><?php echo TEXT_SPECIALS_PRICE_TIP; ?></td>
            <td class="smallText" align="right" valign="top"><br /><?php echo tep_draw_button(IMAGE_SAVE, 'disk', null, 'primary') . tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('specials.php', 'page=' . $_GET['page'] . (isset($_GET['sID']) ? '&sID=' . $_GET['sID'] : ''))); ?></td>
          </tr>
        </table>
      </td>
      </form>
    </tr>
  <?php
  } else {
  ?>
    <tr>
      <td>
        <table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td valign="top">
              <table border="0" width="100%" cellspacing="0" cellpadding="2">
                <tr class="dataTableHeadingRow">
                  <td class="dataTableHeadingContent"><?php echo TABLE_HEADING_PRODUCTS; ?></td>
                  <td class="dataTableHeadingContent" align="right"><?php echo TABLE_HEADING_PRODUCTS_PRICE; ?></td>
                  <td class="dataTableHeadingContent" align="right"><?php echo TABLE_HEADING_STATUS; ?></td>
                  <td class="dataTableHeadingContent" align="right"><?php echo TABLE_HEADING_ACTION; ?>&nbsp;</td>
                </tr>
                <?php
                // $specials_query_raw = "select p.products_id, pd.products_name, p.products_price, s.specials_id, s.specials_new_products_price, s.specials_date_added, s.specials_last_modified, s.expires_date, s.date_status_change, s.status                       from products p, specials s, products_description pd where p.products_id = pd.products_id and pd.language_id = '" . (int)$languages_id . "' and p.products_id = s.products_id order by pd.products_name";
                $specials_query_raw = "select p.products_id, pd.products_name, p.products_price, s.specials_id, s.specials_new_products_price, s.specials_date_added, s.specials_last_modified, s.expires_date, s.date_status_change, s.status, s.variations_id, v.products_variations_id, v.price, v.model from products p JOIN products_description pd ON p.products_id = pd.products_id JOIN specials s ON p.products_id = s.products_id LEFT JOIN products_variations v ON s.variations_id = v.products_variations_id WHERE pd.language_id = 1 ORDER BY pd.products_name";
                $specials_split = new splitPageResults($_GET['page'], MAX_DISPLAY_SEARCH_RESULTS, $specials_query_raw, $specials_query_numrows);
                $specials_query = tep_db_query($specials_query_raw);

                while ($specials = tep_db_fetch_array($specials_query)) {
                  $variation_string = "";
                  if ((!isset($_GET['sID']) || (isset($_GET['sID']) && ($_GET['sID'] == $specials['specials_id']))) && !isset($sInfo)) {
                    $products_query = tep_db_query("select products_image from products where products_id = '" . (int)$specials['products_id'] . "'");
                    $products = tep_db_fetch_array($products_query);
                    $sInfo_array = array_merge($specials, $products);
                    $sInfo = new objectInfo($sInfo_array);
                  }
                  print_rr($specials);
                  if ($specials['variations_id'] > 0) {
                    $variation_string = ": <strong>Applied to " . $specials['model'] . "</strong>";
                  }
                  if (isset($sInfo) && is_object($sInfo) && ($specials['specials_id'] == $sInfo->specials_id)) {
                    echo '                  <tr id="defaultSelected" class="dataTableRowSelected" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link('specials.php', 'page=' . $_GET['page'] . '&sID=' . $sInfo->specials_id . '&action=edit') . '\'">';
                  } else {
                    echo '                  <tr class="dataTableRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link('specials.php', 'page=' . $_GET['page'] . '&sID=' . $specials['specials_id']) . '\'">';
                  }
                ?>
                  <td class="dataTableContent"><?php echo $specials['products_name'] . $variation_string ?></td>
                  <td class="dataTableContent" align="right"><span class="oldPrice"><?php echo $currencies->format($specials['products_price']); ?></span> <span class="specialPrice"><?php echo $currencies->format($specials['specials_new_products_price']); ?></span></td>
                  <td class="dataTableContent" align="right">
                    <?php
                    if ($specials['status'] == '1') {
                      echo tep_image('images/icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) . '&nbsp;&nbsp;<a href="' . tep_href_link('specials.php', 'action=setflag&flag=0&id=' . $specials['specials_id']) . '">' . tep_image('images/icon_status_red_light.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10) . '</a>';
                    } else {
                      echo '<a href="' . tep_href_link('specials.php', 'action=setflag&flag=1&id=' . $specials['specials_id']) . '">' . tep_image('images/icon_status_green_light.gif', IMAGE_ICON_STATUS_GREEN_LIGHT, 10, 10) . '</a>&nbsp;&nbsp;' . tep_image('images/icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
                    }
                    ?></td>
                  <td class="dataTableContent" align="right"><?php if (isset($sInfo) && is_object($sInfo) && ($specials['specials_id'] == $sInfo->specials_id)) {
                                                                echo tep_image('images/icon_arrow_right.gif', '');
                                                              } else {
                                                                echo '<a href="' . tep_href_link('specials.php', 'page=' . $_GET['page'] . '&sID=' . $specials['specials_id']) . '">' . tep_image('images/icon_info.gif', IMAGE_ICON_INFO) . '</a>';
                                                              } ?>&nbsp;</td>
          </tr>
        <?php
                }
        ?>
        <tr>
          <td colspan="4">
            <table border="0" width="100%" cellpadding="0" cellspacing="2">
              <tr>
                <td class="smallText" valign="top"><?php echo $specials_split->display_count($specials_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_GET['page'], TEXT_DISPLAY_NUMBER_OF_SPECIALS); ?></td>
                <td class="smallText" align="right"><?php echo $specials_split->display_links($specials_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $_GET['page']); ?></td>
              </tr>
              <?php
              if (empty($action)) {
              ?>
                <tr>
                  <td class="smallText" colspan="2" align="right"><?php echo tep_draw_button(IMAGE_NEW_PRODUCT, 'plus', tep_href_link('specials.php', 'page=' . $_GET['page'] . '&action=new')); ?></td>
                </tr>
              <?php
              }
              ?>
            </table>
          </td>
        </tr>
        </table>
      </td>
    <?php
    $heading = array();
    $contents = array();

    switch ($action) {
      case 'delete':
        $heading[] = array('text' => '<strong>' . TEXT_INFO_HEADING_DELETE_SPECIALS . '</strong>');

        $contents = array('form' => tep_draw_form('specials', 'specials.php', 'page=' . $_GET['page'] . '&sID=' . $sInfo->specials_id . '&action=deleteconfirm'));
        $contents[] = array('text' => TEXT_INFO_DELETE_INTRO);
        $contents[] = array('text' => '<br /><strong>' . $sInfo->products_name . '</strong>');
        $contents[] = array('align' => 'center', 'text' => '<br />' . tep_draw_button(IMAGE_DELETE, 'trash', null, 'primary') . tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('specials.php', 'page=' . $_GET['page'] . '&sID=' . $sInfo->specials_id)));
        break;
      default:
        if (is_object($sInfo)) {
          $heading[] = array('text' => '<strong>' . $sInfo->products_name . '</strong>');

          $contents[] = array('align' => 'center', 'text' => tep_draw_button(IMAGE_EDIT, 'document', tep_href_link('specials.php', 'page=' . $_GET['page'] . '&sID=' . $sInfo->specials_id . '&action=edit')) . tep_draw_button(IMAGE_DELETE, 'trash', tep_href_link('specials.php', 'page=' . $_GET['page'] . '&sID=' . $sInfo->specials_id . '&action=delete')));
          $contents[] = array('text' => '<br />' . TEXT_INFO_DATE_ADDED . ' ' . tep_date_short($sInfo->specials_date_added));
          $contents[] = array('text' => '' . TEXT_INFO_LAST_MODIFIED . ' ' . tep_date_short($sInfo->specials_last_modified));
          $contents[] = array('align' => 'center', 'text' => '<br />' . tep_info_image($sInfo->products_image, $sInfo->products_name, SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT));
          $contents[] = array('text' => '<br />' . TEXT_INFO_ORIGINAL_PRICE . ' ' . $currencies->format($sInfo->products_price));
          $contents[] = array('text' => '' . TEXT_INFO_NEW_PRICE . ' ' . $currencies->format($sInfo->specials_new_products_price));
          //$contents[] = array('text' => '' . TEXT_INFO_PERCENTAGE . ' ' . number_format(100 - (($sInfo->specials_new_products_price / $sInfo->products_price) * 100)) . '%');

          $contents[] = array('text' => '<br />' . TEXT_INFO_EXPIRES_DATE . ' <strong>' . tep_date_short($sInfo->expires_date) . '</strong>');
          $contents[] = array('text' => '' . TEXT_INFO_STATUS_CHANGE . ' ' . tep_date_short($sInfo->date_status_change));
        }
        break;
    }
    if ((@tep_not_null($heading)) && (@tep_not_null($contents))) {
      echo '            <td width="25%" valign="top">' . "\n";

      $box = new box;
      echo $box->infoBox($heading, $contents);

      echo '            </td>' . "\n";
    }
  }
    ?>
    </tr>
</table>
</td>
</tr>
</table>

<?php
require('includes/template_bottom.php');
require('includes/application_bottom.php');
?>