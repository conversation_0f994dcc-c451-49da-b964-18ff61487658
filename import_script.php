<?php
include 'includes/application_top.php';

// Define the path to the input CSV file
$input_file = DIR_FS_CATALOG . 'product_catalog.csv';
$table_name = 'products_autodesk_catalog';

// Function to drop the table if it exists
function dropTable($pdo, $table_name) {
    $dropTableSQL = "DROP TABLE IF EXISTS $table_name";
    try {
        $pdo->exec($dropTableSQL);
        echo json_encode(['message' => "Table $table_name dropped successfully."]);
    } catch (PDOException $e) {
        die(json_encode(['error' => "Could not drop table: " . $e->getMessage()]));
    }
}

// Function to create the table
function createTable($pdo, $table_name) {
    $createTableSQL = "
    CREATE TABLE IF NOT EXISTS $table_name (
        id INT AUTO_INCREMENT PRIMARY KEY,
        offeringName VARCHAR(255),
        offeringCode VARCHAR(255),
        offeringId VARCHAR(255),
        intendedUsage_code VARCHAR(255),
        intendedUsage_description VARCHAR(255),
        accessModel_code VARCHAR(255),
        accessModel_description VARCHAR(255),
        servicePlan_code VARCHAR(255),
        servicePlan_description VARCHAR(255),
        connectivity_code VARCHAR(255),
        connectivity_description VARCHAR(255),
        term_code VARCHAR(255),
        term_description VARCHAR(255),
        lifeCycleState VARCHAR(255),
        renewOnlyDate DATE,
        discontinueDate DATE,
        orderAction VARCHAR(255),
        specialProgramDiscount_code VARCHAR(255),
        specialProgramDiscount_description VARCHAR(255),
        fromQty INT,
        toQty INT,
        currency VARCHAR(255),
        SRP DECIMAL(10, 2),
        costAfterSpecialProgramDiscount DECIMAL(10, 2),
        renewalDiscountPercent DECIMAL(5, 2),
        renewalDiscountAmount DECIMAL(10, 2),
        costAfterRenewalDiscount DECIMAL(10, 2),
        transactionVolumeDiscountPercent DECIMAL(5, 2),
        transactionVolumeDiscountAmount DECIMAL(10, 2),
        costAfterTransactionVolumeDiscount DECIMAL(10, 2),
        serviceDurationDiscountPercent DECIMAL(5, 2),
        serviceDurationDiscountAmount DECIMAL(10, 2),
        costAfterServiceDurationDiscount DECIMAL(10, 2),
        effectiveStartDate DATE,
        effectiveEndDate DATE,
        changeFlag VARCHAR(255)
    );
    ";

    try {
        $pdo->exec($createTableSQL);
        echo json_encode(['message' => "Table $table_name created successfully."]);
    } catch (PDOException $e) {
        die(json_encode(['error' => "Could not create table: " . $e->getMessage()]));
    }
}

// Function to import CSV data into the table
function importCSV($pdo, $csvFilePath, $table_name) {
    $importedLines = 0;
    $skippedLines = 0;
    $skippedReasons = [];

    if (($handle = fopen($csvFilePath, "r")) !== FALSE) {
        // Get the CSV header row
        $csvHeader = fgetcsv($handle, 1000, ",");

        // Define the mapping from CSV headers to table columns
        $headerMap = [
            'offeringName' => 'offeringName',
            'offeringCode' => 'offeringCode',
            'offeringId' => 'offeringId',
            'intendedUsage.code' => 'intendedUsage_code',
            'intendedUsage.description' => 'intendedUsage_description',
            'accessModel.code' => 'accessModel_code',
            'accessModel.description' => 'accessModel_description',
            'servicePlan.code' => 'servicePlan_code',
            'servicePlan.description' => 'servicePlan_description',
            'connectivity.code' => 'connectivity_code',
            'connectivity.description' => 'connectivity_description',
            'term.code' => 'term_code',
            'term.description' => 'term_description',
            'lifeCycleState' => 'lifeCycleState',
            'renewOnlyDate' => 'renewOnlyDate',
            'discontinueDate' => 'discontinueDate',
            'orderAction' => 'orderAction',
            'specialProgramDiscount.code' => 'specialProgramDiscount_code',
            'specialProgramDiscount.description' => 'specialProgramDiscount_description',
            'fromQty' => 'fromQty',
            'toQty' => 'toQty',
            'currency' => 'currency',
            'SRP' => 'SRP',
            'costAfterSpecialProgramDiscount' => 'costAfterSpecialProgramDiscount',
            'renewalDiscountPercent' => 'renewalDiscountPercent',
            'renewalDiscountAmount' => 'renewalDiscountAmount',
            'costAfterRenewalDiscount' => 'costAfterRenewalDiscount',
            'transactionVolumeDiscountPercent' => 'transactionVolumeDiscountPercent',
            'transactionVolumeDiscountAmount' => 'transactionVolumeDiscountAmount',
            'costAfterTransactionVolumeDiscount' => 'costAfterTransactionVolumeDiscount',
            'serviceDurationDiscountPercent' => 'serviceDurationDiscountPercent',
            'serviceDurationDiscountAmount' => 'serviceDurationDiscountAmount',
            'costAfterServiceDurationDiscount' => 'costAfterServiceDurationDiscount',
            'effectiveStartDate' => 'effectiveStartDate',
            'effectiveEndDate' => 'effectiveEndDate',
            'changeFlag' => 'changeFlag'
        ];

        // Ensure all required columns are present in the CSV
        $missingColumns = array_diff(array_keys($headerMap), $csvHeader);
        if (!empty($missingColumns)) {
            die(json_encode(['error' => "CSV is missing the following columns: " . implode(", ", $missingColumns)]));
        }

        // Read the CSV data and insert into the database
        while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
            $mappedData = [];
            foreach ($headerMap as $csvColumn => $tableColumn) {
                $index = array_search($csvColumn, $csvHeader);
                if ($index !== FALSE) {
                    $mappedData[$tableColumn] = $pdo->quote($data[$index]);
                } else {
                    $mappedData[$tableColumn] = 'NULL';
                }
            }

            // Prepare the SQL insert statement
            $insertSQL = "INSERT INTO $table_name (" . implode(", ", array_keys($mappedData)) . ") VALUES (" . implode(", ", array_values($mappedData)) . ")";

            // Execute the insert statement
            try {
                $pdo->exec($insertSQL);
                $importedLines++;
            } catch (PDOException $e) {
                $skippedLines++;
                $skippedReasons[] = "Line " . ($importedLines + $skippedLines + 1) . ": " . $e->getMessage();
            }
        }

        fclose($handle);
    } else {
        die(json_encode(['error' => "Could not open CSV file."]));
    }

    // Output the results in JSON format
    echo json_encode([
        'message' => 'CSV data import completed.',
        'imported_lines' => $importedLines,
        'skipped_lines' => $skippedLines,
        'skipped_reasons' => $skippedReasons
    ]);
}

// Main script execution
$pdo = tep_db_connect();
dropTable($pdo, $table_name); // Drop the table if it exists
createTable($pdo, $table_name);
importCSV($pdo, $input_file, $table_name);
tep_db_close();
?>
