<?php
$email_subject = 'Your {{product_name}} subscription {{status,"active":"is ending soon", "expired": "has expired"}}';
$email = '
your subscription {{status,"active":"is ending soon", "expired": "has expired"}}  
<h1 style="color: #0078D7;">RENEW NOW</h1>
    <p><strong>ITS TIME TO RENEW YOUR AUTODESK SUBSCRIPTION CONTRACT</strong></p>
    <p style="color: #666;">Please disregard this email if you have already renewed.</p>
    
    <p>Dear <strong>{{endCustomerName}}</strong>,</p>
    <p>Your Autodesk subscription expires soon. Please check the contract details below and the renewal procedures.</p>
    
    <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
        <tr>
            <td style="padding: 8px; border: 1px solid #ddd;"><strong>Product:</strong></td>
            <td style="padding: 8px; border: 1px solid #ddd;">{{product_name}}</td>
        </tr>
        <tr>
            <td style="padding: 8px; border: 1px solid #ddd;"><strong>Terms:</strong></td>
            <td style="padding: 8px; border: 1px solid #ddd;">{{term}}</td>
        </tr>
        <tr>
            <td style="padding: 8px; border: 1px solid #ddd;"><strong>Seats:</strong></td>
            <td style="padding: 8px; border: 1px solid #ddd;">{{seats}}</td>
        </tr>
        <tr>
            <td style="padding: 8px; border: 1px solid #ddd;"><strong>Serial Number:</strong></td>
            <td style="padding: 8px; border: 1px solid #ddd;">{{subscriptionReferenceNumber}}</td>
        </tr>
        <tr>
            <td style="padding: 8px; border: 1px solid #ddd;"><strong>Renewal Number:</strong></td>
            <td style="padding: 8px; border: 1px solid #ddd;">{{opportunityNumber}}</td>
        </tr>
    </table>
    
    <p>Please reply to this email to receive a quotation for this renewal.</p>
    
    <h2>Why buy from Autodesk Partner?</h2>
    <p>As a partner, we understand your unique business and industry needs. Autodesk Authorized partners develop solutions, implement and provide specialist support services and much more. See below:</p>
    <ul style="list-style: disc; margin-left: 20px;">
        <li>Competitive Pricing</li>
        <li>Experienced Sales Staff</li>
        <li>Dedicated sales contact with phone number</li>
        <li>Access to value-added services and products</li>
        <li>Product support and training</li>
    </ul>
    Your Autodesk contract is {{status,\'active\':\'is ending soon\', \'expired\': \'has expired\'}} 

    <p>Regards,</p>
    <p><strong>Aurangzaib Mahmood</strong><br>
    TCS CAD & BIM Solutions Limited<br>
    Unit F, Yorkway<br>
    Mandale Industrial Estate<br>
    Thornaby<br>
    Stockton-on-Tees<br>
    TS17 6BX<br>
    Tel: ************</p>
    
    <p style="font-size: 0.9em; color: #666;">You have received this email because your administrator has set up an automated email notification.</p>

                ';


$sub = [
    'status' => 'active',
    'endCustomerName' => 'Terry Mahmood',
    'product_name' => 'Autodesk Revit',
    'term' => 'Annual',
    'seats' => '1',
    'subscriptionReferenceNumber' => '1234567890',
    'opportunityNumber' => '099986545',
];

preg_match_all('/{{\s*([^,}]+)(?:,(.+?))?\s*}}/', $email, $matches, PREG_SET_ORDER);

foreach ($matches as $match) {
    $placeholder = $match[0];
    $key = $match[1];
    $expression = isset($match[2]) ? $match[2] : null;

    if (array_key_exists($key, $sub)) {
        if ($expression) {
            $conditions = explode(',', $expression);
            foreach ($conditions as $condition) {
                [$condition_key, $replacement] = explode(':', $condition);
                $condition_key = trim(str_replace(["'", '"'], '', $condition_key));
                $replacement = trim(str_replace(["'", '"'], '', $replacement));

                if (strtolower($sub[$key]) === strtolower($condition_key)) {
                    $email = str_replace($placeholder, $replacement, $email);
                    $email_subject = str_replace($placeholder, $replacement, $email_subject);
                    break;
                }
            }
        } else {
            $email = str_replace($placeholder, $sub[$key], $email);
            $email_subject = str_replace($placeholder, $sub[$key], $email_subject);
        }
    } else {
        $email = str_replace($placeholder, '', $email);
        $email_subject = str_replace($placeholder, '', $email_subject);
    }
}

echo $email_subject;
echo "<br><br>";
echo nl2br(htmlentities($email));
?>